
// v1
const path = require('path');

const join = currentPath => path.join(__dirname, currentPath);

const reactRules =
{
	// React
	'react-hooks/exhaustive-deps': 0,
	'react/jsx-sort-props': 0,
	'react/jsx-one-expression-per-line': 0,
	'react/jsx-filename-extension': [1, { extensions: ['.js', '.jsx', '.ts', '.tsx'] }],
	'react/jsx-indent': [1, 'tab', { checkAttributes: true, indentLogicalExpressions: true }],
	'react/jsx-indent-props': [1, 'tab'],
	'react/forbid-prop-types': 0,
	'react/jsx-props-no-spreading': 0,
	'react/prop-types': [1, { ignore: ['className'] }],
	// New babel JSX transpiler
	'react/react-in-jsx-scope': 0,
	'react/jsx-uses-react': 0,
	// JSX
	'jsx-quotes': [1, 'prefer-double'],
	'jsx-a11y/label-has-associated-control': 0,
	'jsx-a11y/label-has-for': [0, {
		required: { every: ['nesting', 'id'] },
		allowChildren: true,
	}],
	'jsx-a11y/anchor-is-valid': [1, {
		components: ['Link'],
		specialLink: ['to', '$to'],
		aspects: ['noHref', 'invalidHref', 'preferButton'],
	}],

	'react/function-component-definition': [1, {
		namedComponents: ['function-declaration', 'arrow-function'],
		unnamedComponents: ['arrow-function'],
	}],
};

const commonRules =
{
	'no-tabs': 0,
	'no-underscore-dangle': 0,
	'prefer-destructuring': 0,
	'prefer-object-spread': 0,
	'linebreak-style': 0,
	'no-unused-expressions': 0,
	'class-methods-use-this': 0,
	'no-template-curly-in-string': 0,
	'template-curly-spacing': 0,
	'no-debugger': 1,
	'no-unreachable': 1,
	'no-unused-vars': 1,
	'spaced-comment': 1,
	'eol-last': 2,
	'no-param-reassign': [2, { props: false }],
	indent: [2, 'tab', {
		MemberExpression: 1,
		ignoredNodes: ['TemplateLiteral'],
		SwitchCase: 1,
	}],
	quotes: [1, 'single', { avoidEscape: true }],
	semi: [1, 'always', { omitLastInOneLineBlock: true }],
	'max-len': [2, 100, 1, {
		ignoreUrls: true,
		ignoreComments: true,
		ignoreRegExpLiterals: true,
		ignoreStrings: true,
		ignoreTemplateLiterals: true,
	}],
	'brace-style': [1, 'allman', { allowSingleLine: true }],
	'comma-dangle': [1, 'only-multiline'],
	'operator-linebreak': [1, 'before', {
		overrides: {
			'?': 'before',
			':': 'before',
			'{': 'ignore',
			'=': 'ignore',
			'??': 'after',
			'||': 'after',
			'&&': 'after',
		// '+': 'before',
		}
	}],
	'semi-spacing': [1, { before: false, after: true }],
	'function-paren-newline': [1, 'consistent'],
	'func-names': [1, 'as-needed'],
	'lines-between-class-members': [1, 'always', { exceptAfterSingleLine: true }],
	'prefer-arrow-callback': [1, { allowNamedFunctions: true }],
	'arrow-parens': [1, 'as-needed', { requireForBlockBody: true }],
	'no-plusplus': [1, { allowForLoopAfterthoughts: true }],
	'no-multiple-empty-lines': [1, { max: 2, maxEOF: 0, maxBOF: 1 }],
	'import/extensions': [2, 'ignorePackages', {
		js: 'never',
		jsx: 'never',
		ts: 'never',
		tsx: 'never',
	}],
};

const cliRules =
{
	...commonRules,

	'import/no-dynamic-require': 0,
	'global-require': 0,
	'no-console': 0,
	strict: 0,
	'import/no-extraneous-dependencies': [2, { devDependencies: true }],
	'no-restricted-syntax': ['error', 'ForInStatement', 'LabeledStatement', 'WithStatement'],
};

const serverRules =
{
	...commonRules,
	...reactRules,
	...cliRules,
};

const browserRules =
{
	...commonRules,
	...reactRules,
	// 'import/no-dynamic-require': 2,
	// strict: 2,

	'default-case': 1,
	'no-continue': 1,
	'no-restricted-syntax': 2,
	'no-await-in-loop': 2,
	'no-console': [1, { allow: ['warn', 'error'] }],
	'import/no-extraneous-dependencies': [2, { devDependencies: false }],
};

const typescriptServerRules =
{
	...serverRules,

	// Disabled Eslint Rules
	quotes: 0,
	'react/display-name': 0,
	'react/prop-types': 0,
	'react/react-in-jsx-scope': 0,
	'naming-convention': 0,
	'no-unused-vars': 0,
	'no-use-before-define': 0,
	'default-param-last': 0,
	'brace-style': 0,
	'lines-between-class-members': 0,

	// Disabled TS Rules
	'@typescript-eslint/indent': 0,
	'@typescript-eslint/restrict-template-expressions': 0,
	'@typescript-eslint/no-unsafe-assignment': 0,
	'@typescript-eslint/no-unsafe-call': 0,
	'@typescript-eslint/no-unsafe-member-access': 0,
	'@typescript-eslint/no-floating-promises': 0,
	'@typescript-eslint/no-non-null-assertion': 0,

	// Overrides
	'@typescript-eslint/no-unused-vars': 1,
	'@typescript-eslint/no-use-before-define': 1,

	// Need to add manually the rules
	// ...appRules,
	'import/extensions': [2, 'ignorePackages', {
		'': 'never',
		js: 'never',
		jsx: 'never',
		ts: 'never',
		tsx: 'never',
	}],

	'@typescript-eslint/quotes': [1, 'single', { avoidEscape: true }],
	// Fix typescript specific the same rules
	'@typescript-eslint/brace-style': [1, 'allman', { allowSingleLine: true }],

	// semi: [1, 'always', { omitLastInOneLineBlock: true }],
	'@typescript-eslint/semi': [1, 'always', { omitLastInOneLineBlock: true }],

	'@typescript-eslint/naming-convention': [1, {
		selector: 'variableLike',
		format: null,
		leadingUnderscore: 'allow',
		trailingUnderscore: 'allow',
		filter:
		{
			regex: '^[_]$',
			match: true,
		},
	}],

	'@typescript-eslint/no-namespace': [2, { allowDeclarations: true }],
	'@typescript-eslint/no-empty-interface': [2, { allowSingleExtends: true }],

	// temp allowing during TS migration
	'@typescript-eslint/ban-ts-comment': [2, {
		'ts-ignore': 'allow-with-description',
		minimumDescriptionLength: 4,
	}],

	// React
	'react/require-default-props': [2, { functions: 'defaultArguments' }],

	// For beginners
	// '@typescript-eslint/explicit-function-return-type': 0,
	// '@typescript-eslint/no-unused-vars': 1,
	// '@typescript-eslint/no-floating-promises': 0,
	// '@typescript-eslint/strict-boolean-expressions': 0,
	// '@typescript-eslint/no-empty-interface': 0,
	// '@typescript-eslint/promise-function-async': 0,
	// '@typescript-eslint/no-misused-promises': 0,
};

const typescriptBrowserRules =
{
	...browserRules,
	...typescriptServerRules,
	// Disabled Rules
	'react/display-name': 0,
	'react/react-in-jsx-scope': 0,
	'react/prop-types': 0,
	// 'react/require-default-props': 0,
	'react/require-default-props': [2, {
		functions: 'defaultArguments',
		// forbidDefaultForRequired: false,
		// "classes": "defaultProps" | "ignore",
		// @deprecated Use `functions` option instead.
	}],
};

const commonTestRules =
{
	js:
	{
		'no-script-url': 0,
		'no-restricted-syntax': 0,
		'guard-for-in': 0,
		'no-plusplus': 0,
		radix: 0,
	},
	ts:
	{
		'@typescript-eslint/return-await': 0,
	},
};

process.env.BABEL_ENV = 'development';
process.env.NODE_ENV = 'development';

const commonGlobals =
{
	DI: 'readonly',
	__DEV__: 'readonly',
	__PROD__: 'readonly',
	__TEST__: 'readonly',
	__DEBUG__: 'readonly',
	Atomics: 'readonly',
	SharedArrayBuffer: 'readonly',
};

const createCLIRules = (
	rootDir = path.dirname(__dirname),
	tsConfigPath = './_config/tsconfig.base.json',
) => ({
	root: true,
	env: { node: true },
	rules: { ...cliRules },
	globals: { ...commonGlobals },
	parser: '@babel/eslint-parser',
	parserOptions:
	{
		ecmaFeatures: { jsx: true },
		requireConfigFile: false,
		ecmaVersion: 2020, // Allows for the parsing of modern ECMAScript features
		sourceType: 'module', // Allows for the use of imports
	},
	extends: ['airbnb'],
	plugins: ['import'],
	settings:
	{
		'import/resolver':
		{
			node:
			{
				moduleDirectory: ['node_modules'],
				extensions: ['.js', '.jsx', '.ts', '.tsx'],
			},
			// alias: aliasMap,
			typescript:
			{
				project: path.join(rootDir, tsConfigPath),
				alwaysTryTypes: true,
			},
		},
		// ['.js', '.jsx', '.ts', '.tsx'] | ['.ts', '.tsx']
		'import/parsers':
		{
			'@typescript-eslint/parser': ['.js', '.ts', '.tsx'],
		},
	},
	overrides:
	[
		{
			files: ['*.{ts,tsx}'],
			parser: '@typescript-eslint/parser',
			parserOptions:
			{
				tsconfigRootDir: rootDir,
				project: path.join(rootDir, tsConfigPath),
				requireConfigFile: false,
			},
			globals: { ...commonGlobals },
			extends:
			[
				'airbnb-typescript',
				'plugin:@typescript-eslint/recommended',
				'plugin:@typescript-eslint/eslint-recommended',
				// 'plugin:@typescript-eslint/recommended-requiring-type-checking',
			],
			plugins: ['@typescript-eslint', 'import'],
			rules: { ...cliRules },
		},
	],
});

const createBrowserRules = (
	rootDir = path.dirname(__dirname),
	tsConfigPath = './_config/tsconfig.browser.json',
) => ({
	root: true,
	env: { browser: true },
	globals: { React: 'writable', ...commonGlobals },
	parser: '@babel/eslint-parser',
	parserOptions:
	{
		ecmaFeatures: { jsx: true },
		requireConfigFile: false,
		ecmaVersion: 2020, // Allows for the parsing of modern ECMAScript features
		sourceType: 'module', // Allows for the use of imports
	},
	extends: ['airbnb', 'plugin:jsx-a11y/recommended', 'plugin:react/jsx-runtime'],
	plugins: ['react', 'react-hooks', 'jsx-a11y', 'import'],
	settings:
	{
		'import/resolver':
		{
			node:
			{
				moduleDirectory: ['node_modules'],
				extensions: ['.js', '.jsx', '.ts', '.tsx'],
			},
			// alias: aliasMap,
			typescript:
			{
				project: path.join(rootDir, tsConfigPath),
				alwaysTryTypes: true,
			},
		},
		// ['.js', '.jsx', '.ts', '.tsx'] | ['.ts', '.tsx']
		'import/parsers':
		{
			'@typescript-eslint/parser': ['.js', '.ts', '.tsx'],
		},
	},
	rules: { ...browserRules },
	overrides:
	[
		{
			files: ['*.{ts,tsx}'],
			parser: '@typescript-eslint/parser',
			parserOptions:
			{
				tsconfigRootDir: rootDir,
				project: path.join(rootDir, tsConfigPath),
				requireConfigFile: false,
				ecmaFeatures: { jsx: true },
				ecmaVersion: 2020, // Allows for the parsing of modern ECMAScript features
				sourceType: 'module', // Allows for the use of imports
			},
			globals: { React: 'writable', ...commonGlobals },
			extends:
			[
				'airbnb-typescript',
				'airbnb/hooks',
				'plugin:react/recommended',
				'plugin:jsx-a11y/recommended',
				'plugin:react/jsx-runtime',
				'plugin:@typescript-eslint/recommended',
				'plugin:@typescript-eslint/eslint-recommended',
				// 'plugin:@typescript-eslint/recommended-requiring-type-checking',
			],
			plugins:
			[
				'react',
				'@typescript-eslint',
				'react-hooks',
				'jsx-a11y',
				'import',
				// '@stylexjs',
			],
			rules: { ...typescriptBrowserRules },
		},
	],
});

const createServerRules = (
	rootDir = path.dirname(__dirname),
	tsConfigPath = './_config/tsconfig.server.json',
) => ({
	root: true,
	env: { node: true, browser: true },
	globals: { ...commonGlobals },
	parser: '@babel/eslint-parser',
	parserOptions:
	{
		ecmaFeatures: { jsx: true },
		requireConfigFile: false,
		ecmaVersion: 2020, // Allows for the parsing of modern ECMAScript features
		sourceType: 'module', // Allows for the use of imports
	},
	extends: ['airbnb', 'plugin:jsx-a11y/recommended', 'plugin:react/jsx-runtime'],
	plugins: ['react', 'react-hooks', 'jsx-a11y', 'import'],
	settings:
	{
		'import/resolver':
		{
			node:
			{
				moduleDirectory: ['node_modules'],
				extensions: ['.js', '.jsx', '.ts', '.tsx'],
			},
			// alias: aliasMap,
			typescript:
			{
				project: path.join(rootDir, tsConfigPath),
				alwaysTryTypes: true,
			},
		},
		// ['.js', '.jsx', '.ts', '.tsx'] | ['.ts', '.tsx']
		'import/parsers':
		{
			'@typescript-eslint/parser': ['.js', '.ts', '.tsx'],
		},
	},
	rules: { ...serverRules },
	overrides:
	[
		{
			files: ['*.{ts,tsx}'],
			parser: '@typescript-eslint/parser',
			parserOptions:
			{
				tsconfigRootDir: rootDir,
				project: path.join(rootDir, tsConfigPath),
				requireConfigFile: false,
				// project: './tsconfig.json',
			},
			globals: { React: 'writable', ...commonGlobals },
			extends:
			[
				'airbnb-typescript',
				'airbnb/hooks',
				'plugin:react/recommended',
				'plugin:jsx-a11y/recommended',
				'plugin:react/jsx-runtime',
				'plugin:@typescript-eslint/recommended',
				'plugin:@typescript-eslint/eslint-recommended',
				// 'plugin:@typescript-eslint/recommended-requiring-type-checking',
			],
			plugins:
			[
				'react',
				'@typescript-eslint',
				'react-hooks',
				'jsx-a11y',
				'import',
				// '@stylexjs',
			],
			rules: { ...typescriptServerRules },
		},
	],
});

const createWebTestRules = (
	rootDir = path.dirname(__dirname),
	tsConfigPath = './_config/tsconfig.test-web.json',
) => ({
	root: true,
	env: { node: true, jest: true },
	globals: {
		// Vitest globals
		vi: 'readonly',
		// Jest globals
		jest: 'readonly',
		// Common test globals (works with both)
		describe: 'readonly',
		it: 'readonly',
		test: 'readonly',
		expect: 'readonly',
		beforeAll: 'readonly',
		afterAll: 'readonly',
		beforeEach: 'readonly',
		afterEach: 'readonly',
		...commonGlobals,
	},
	parser: '@babel/eslint-parser',
	parserOptions: {
		ecmaFeatures: { jsx: true },
		requireConfigFile: false,
		ecmaVersion: 2020,
		sourceType: 'module',
	},
	settings: {
		'import/resolver': {
			node: {
				moduleDirectory: ['node_modules'],
				extensions: ['.js', '.jsx', '.ts', '.tsx'],
			},
			typescript: {
				project: path.join(rootDir, tsConfigPath),
				alwaysTryTypes: true,
			},
		},
		'import/parsers': {
			'@typescript-eslint/parser': ['.js', '.ts', '.tsx'],
		},
	},
	extends: [
		'airbnb-base',
		'plugin:testing-library/dom',
		'plugin:jest-formatting/recommended',
	],
	plugins: ['import', 'testing-library', 'jest-formatting', 'jest'],
	rules: {
		// Base common rules for consistency
		...commonRules,
		// Relaxed for tests
		'no-console': 0,
		'no-underscore-dangle': 0,
		'no-unused-expressions': 0,
		'max-classes-per-file': 0,
		'class-methods-use-this': 0,
		'no-promise-executor-return': 0,
		'no-await-in-loop': 0,
		'no-loop-func': 0,
		'import/no-extraneous-dependencies': [2, { devDependencies: true }],
		'import/extensions': [2, 'ignorePackages', {
			js: 'never', jsx: 'never', ts: 'never', tsx: 'never'
		}],

		// Testing best-practices (works with both Jest and Vitest)
		'jest-formatting/padding-around-all': 1,

		// Jest rules (when using Jest)
		'jest/expect-expect': 0, // Allow both jest.expect and vitest expect
		'jest/no-focused-tests': 1, // Warn about .only and .skip
		'jest/no-identical-title': 2, // Error on duplicate test names
		'jest/valid-expect': 2, // Ensure expect is used correctly
		'jest/no-disabled-tests': 1, // Warn about skipped tests
	},
	overrides: [
		{
			files: ['**/*.{ts,tsx}'],
			parser: '@typescript-eslint/parser',
			parserOptions: {
				tsconfigRootDir: rootDir,
				project: path.join(rootDir, tsConfigPath),
				requireConfigFile: false,
			},
			extends: ['airbnb-typescript/base', 'plugin:@typescript-eslint/recommended'],
			plugins: ['@typescript-eslint'],
			rules: {
				// Base TypeScript server rules for consistency
				...typescriptServerRules,

				// Relaxed for tests
				'@typescript-eslint/no-explicit-any': 0,
				'@typescript-eslint/no-unsafe-assignment': 0,
				'@typescript-eslint/no-unsafe-call': 0,
				'@typescript-eslint/no-unsafe-member-access': 0,
				'@typescript-eslint/no-non-null-assertion': 0,
				'@typescript-eslint/no-unused-vars': [1, { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
				'@typescript-eslint/consistent-type-assertions': 0,
				'no-console': 0,
				'max-classes-per-file': 0,
				'class-methods-use-this': 0,
				'no-promise-executor-return': 0,
				'no-await-in-loop': 0,
				'no-loop-func': 0,
				'import/no-extraneous-dependencies': [2, { devDependencies: true }],
			},
		},
		{
			files: ['**/*.{test,spec}.{ts,tsx,js,jsx}'],
			plugins: ['@vitest'],
			rules: {
				'@vitest/expect-expect': 'error',
				'@vitest/no-focused-tests': 'error',
				'@vitest/no-identical-title': 'error',
				'@vitest/valid-expect': 'error',
			},
		},
		{
			files: ['**/*.{test,spec}.tsx'],
			extends: ['plugin:testing-library/react'],
			rules: {},
		},
	],
});

const createMobileTestRules = (
	rootDir = path.dirname(__dirname),
	tsConfigPath = './_config/tsconfig.test-mobile.json',
) => ({
	root: true,
	env: { jest: true },
	globals: {
		// Jest globals
		jest: 'readonly',
		// Common test globals
		describe: 'readonly',
		it: 'readonly',
		test: 'readonly',
		expect: 'readonly',
		beforeAll: 'readonly',
		afterAll: 'readonly',
		beforeEach: 'readonly',
		afterEach: 'readonly',
		// React Native globals
		__DEV__: 'readonly',
		fetch: 'readonly',
		FormData: 'readonly',
		navigator: 'readonly',
		requestAnimationFrame: 'readonly',
		cancelAnimationFrame: 'readonly',
		global: 'readonly',
		window: 'readonly',
		document: 'readonly',
		...commonGlobals,
	},
	parser: '@babel/eslint-parser',
	parserOptions: {
		ecmaFeatures: { jsx: true },
		requireConfigFile: false,
		ecmaVersion: 2020,
		sourceType: 'module',
	},
	settings: {
		'import/resolver': {
			node: {
				moduleDirectory: ['node_modules'],
				extensions: ['.js', '.jsx', '.ts', '.tsx'],
			},
			typescript: {
				project: path.join(rootDir, tsConfigPath),
				alwaysTryTypes: true,
			},
		},
		'import/parsers': {
			'@typescript-eslint/parser': ['.js', '.ts', '.tsx'],
		},
	},
	extends: [
		'airbnb-base',
		'plugin:testing-library/react', // React config works for React Native
		'plugin:jest-formatting/recommended',
	],
	plugins: ['import', 'testing-library', 'jest-formatting', 'jest', 'react-native'],
	rules: {
		// Base common rules for consistency
		...commonRules,

		// Relaxed for tests
		'no-console': 0,
		'no-underscore-dangle': 0,
		'no-unused-expressions': 0,
		'max-classes-per-file': 0,
		'class-methods-use-this': 0,
		'no-promise-executor-return': 0,
		'no-await-in-loop': 0,
		'no-loop-func': 0,
		'import/no-extraneous-dependencies': [2, { devDependencies: true }],
		'import/extensions': 0, // React Native has different module resolution
		'import/no-unresolved': 0, // React Native has different module resolution

		// Testing best-practices (Jest)
		'jest-formatting/padding-around-all': 1,
		'jest/expect-expect': 0,
		'jest/no-focused-tests': 1,
		'jest/no-identical-title': 2,
		'jest/valid-expect': 2,
		'jest/no-disabled-tests': 1,

		// React Native specific rules
		'react-native/no-unused-styles': 2,
		'react-native/split-platform-components': 2,
		'react-native/no-inline-styles': 1,
		'react-native/no-color-literals': 1,

		// Adjust testing-library rules for React Native
		'testing-library/no-node-access': 0, // React Native doesn't have DOM nodes
	},
	overrides: [
		{
			files: ['**/*.{ts,tsx}'],
			parser: '@typescript-eslint/parser',
			parserOptions: {
				tsconfigRootDir: rootDir,
				project: path.join(rootDir, tsConfigPath),
				requireConfigFile: false,
			},
			extends: ['airbnb-typescript/base', 'plugin:@typescript-eslint/recommended'],
			plugins: ['@typescript-eslint'],
			rules: {
				// Base TypeScript server rules for consistency
				...typescriptServerRules,

				// Relaxed for mobile tests
				'@typescript-eslint/no-explicit-any': 0,
				'@typescript-eslint/no-unsafe-assignment': 0,
				'@typescript-eslint/no-unsafe-call': 0,
				'@typescript-eslint/no-unsafe-member-access': 0,
				'@typescript-eslint/no-non-null-assertion': 0,
				'@typescript-eslint/no-unused-vars': [1, { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
				'@typescript-eslint/consistent-type-assertions': 0,
				'no-console': 0,
				'max-classes-per-file': 0,
				'class-methods-use-this': 0,
				'no-promise-executor-return': 0,
				'no-await-in-loop': 0,
				'import/no-extraneous-dependencies': [2, { devDependencies: true }],
				'import/extensions': 0,
				'import/no-unresolved': 0,
			},
		},
	],
});

module.exports = {
	createCLIRules,
	createBrowserRules,
	createServerRules,
	createWebTestRules,
	createMobileTestRules,
};
