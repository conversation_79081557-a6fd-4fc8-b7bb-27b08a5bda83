import { defineConfig } from 'vitest/config';

// Use dynamic import for ESM module
export default defineConfig(async () =>
{
	const tsconfigPaths = (await import('vite-tsconfig-paths')).default;

	return {
		plugins: [
			tsconfigPaths({
				projects: ['./_config/tsconfig.base.json'],
			}),
		],
		test: {
			include: [
				'**/__tests__/**/*.(test|spec).{ts,tsx,js}',
				'**/?(*.)+(spec|test).{ts,tsx,js}',
			],
			exclude: ['node_modules', 'dist', 'build', '**/logs/**'],
			environment: 'node',
			coverage: {
				provider: 'v8',
				reporter: ['text', 'lcov', 'html', 'json'],
				reportsDirectory: './coverage',
				include: ['**/src/**/*.{ts,tsx}'],
				exclude: [
					'**/src/**/*.d.ts',
					'**/src/**/__tests__/**',
					'**/src/**/index.ts',
				],
			},
			testTimeout: 30000,
			maxConcurrency: 4,
		},
	};
});
