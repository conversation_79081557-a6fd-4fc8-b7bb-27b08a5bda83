// eslint-disable-next-line max-classes-per-file
import * as mysql from 'mysql2/promise';
import {
	Pool,
	PoolConnection,
	ResultSetHeader,
	RowDataPacket,
} from 'mysql2/promise';
import Config from '../utils/Config';
import Logger from '../utils/Logger';

/**
 * MariaDB client wrapper with connection pooling and error handling
 */
class MariaClient
{
	private pool: Pool | null = null;

	private logger = Logger.getLogger('MariaClient');

	private isConnected = false;

	/**
   * Connect to MariaDB and create connection pool
   */
	async connect(): Promise<void>
	{
		try
		{
			const config = Config.getAll();

			this.pool = mysql.createPool({
				host: config.MARIA_HOST,
				port: config.MARIA_PORT,
				user: config.MARIA_USER,
				password: config.MARIA_PASSWORD,
				database: config.MARIA_DATABASE,
				connectionLimit: 10,
				charset: 'utf8mb4',
				timezone: '+00:00',
				supportBigNumbers: true,
				bigNumberStrings: true,
			});

			// Test connection
			const connection = await this.pool.getConnection();
			await connection.ping();
			connection.release();

			this.isConnected = true;
			this.logger.info('Connected to MariaDB successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to connect to MariaDB:', error);
			throw error;
		}
	}

	/**
   * Disconnect from MariaDB
   */
	async disconnect(): Promise<void>
	{
		if (this.pool)
		{
			await this.pool.end();
			this.isConnected = false;
			this.pool = null;
			this.logger.info('Disconnected from MariaDB');
		}
	}

	/**
   * Execute query with retry logic
   */
	async execute<T extends RowDataPacket[] | RowDataPacket[][] | ResultSetHeader>(
		query: string,
		params: unknown[] = [],
		options: { maxRetries?: number } = {},
	): Promise<{ rows: T; fields: mysql.FieldPacket[] }>
	{
		if (!this.isConnected || !this.pool)
		{
			throw new Error('MariaDB client not connected');
		}

		const maxRetries = options.maxRetries || 3;
		let lastError: Error;

		for (let attemptIndex = 1; attemptIndex <= maxRetries; attemptIndex += 1)
		{
			try
			{
				// eslint-disable-next-line no-await-in-loop
				const [rows, fields] = await this.pool.execute<T>(query, params);
				return { rows, fields };
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn(`Query attempt ${attemptIndex} failed:`, error);
				if (attemptIndex < maxRetries)
				{
					// eslint-disable-next-line no-await-in-loop
					await this.delay(1000 * attemptIndex);
				}
			}
		}

		this.logger.error('Query failed after all retries:', lastError!);
		throw lastError!;
	}

	/**
   * Execute transaction
   */
	async transaction<T>(callback: (connection: PoolConnection) => Promise<T>): Promise<T>
	{
		if (!this.isConnected || !this.pool)
		{
			throw new Error('MariaDB client not connected');
		}

		const connection = await this.pool.getConnection();

		try
		{
			await connection.beginTransaction();
			const result = await callback(connection);
			await connection.commit();
			return result;
		}
		catch (error)
		{
			await connection.rollback();
			this.logger.error('Transaction failed:', error);
			throw error;
		}
		finally
		{
			connection.release();
		}
	}

	/**
   * Health check
   */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			if (!this.pool) return false;

			const connection = await this.pool.getConnection();
			await connection.ping();
			connection.release();
			return true;
		}
		catch (error)
		{
			this.logger.error('MariaDB health check failed:', error);
			return false;
		}
	}

	/**
   * Get pool instance
   */
	getPool(): Pool | null
	{
		return this.pool;
	}

	/**
   * Delay utility for retry logic
   */
	private delay(ms: number): Promise<void>
	{
		return new Promise<void>((resolve) => { setTimeout(resolve, ms) });
	}
}

// Domain Category Interfaces
export interface DomainCategory
{
  id?: number;
  name: string;
  description?: string;
  parentCategoryId?: number;
  createdAt?: Date;
}

export interface DomainCategoryMapping
{
  domain: string;
  categoryId: number;
  confidenceScore: number;
  assignedAt?: Date;
}

// Backlink Interfaces
export interface Backlink
{
  id?: string;
  sourceDomain: string;
  targetDomain: string;
  linkQualityScore: number;
  anchorText?: string;
  linkType: 'follow' | 'nofollow' | 'sponsored' | 'ugc';
  discoveredAt?: Date;
  lastVerified?: Date;
  isActive: boolean;
}

// Domain Whois Interfaces
export interface DomainWhois
{
  domain: string;
  registrar?: string;
  registrationDate?: Date;
  expirationDate?: Date;
  nameServers?: string;
  registrantCountry?: string;
  registrantOrganization?: string;
  privacyProtected: boolean;
  lastUpdated?: Date;
}

// Domain Review Interfaces
export interface DomainReview
{
  id?: string;
  domain: string;
  source: string;
  rating: number;
  reviewText?: string;
  reviewDate: Date;
  sentimentScore?: number;
  verified: boolean;
  createdAt?: Date;
}

/**
 * Repository pattern for Domain Categories
 */
export class DomainCategoryRepository
{
	constructor(private mariaClient: MariaClient) {}

	async create(category: DomainCategory): Promise<number>
	{
		const query = `
      INSERT INTO domain_categories (name, description, parent_category_id)
      VALUES (?, ?, ?)
    `;
		const params = [category.name, category.description, category.parentCategoryId];

		const result = await this.mariaClient.execute<ResultSetHeader>(query, params);
		return result.rows.insertId;
	}

	async findById(id: number): Promise<DomainCategory | null>
	{
		const query = 'SELECT * FROM domain_categories WHERE id = ?';
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [id]);

		if (result.rows.length === 0) return null;
		return this.mapRowToCategory(result.rows[0]);
	}

	async findByName(name: string): Promise<DomainCategory | null>
	{
		const query = 'SELECT * FROM domain_categories WHERE name = ?';
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [name]);

		if (result.rows.length === 0) return null;
		return this.mapRowToCategory(result.rows[0]);
	}

	async findAll(): Promise<DomainCategory[]>
	{
		const query = 'SELECT * FROM domain_categories ORDER BY name';
		const result = await this.mariaClient.execute<RowDataPacket[]>(query);

		return result.rows.map(row => this.mapRowToCategory(row));
	}

	async findByParent(parentId: number): Promise<DomainCategory[]>
	{
		const query = 'SELECT * FROM domain_categories WHERE parent_category_id = ? ORDER BY name';
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [parentId]);

		return result.rows.map(row => this.mapRowToCategory(row));
	}

	async update(id: number, category: Partial<DomainCategory>): Promise<void>
	{
		const fields = [];
		const params = [];

		if (category.name)
		{
			fields.push('name = ?');
			params.push(category.name);
		}
		if (category.description !== undefined)
		{
			fields.push('description = ?');
			params.push(category.description);
		}
		if (category.parentCategoryId !== undefined)
		{
			fields.push('parent_category_id = ?');
			params.push(category.parentCategoryId);
		}

		if (fields.length === 0) return;

		params.push(id);
		const query = `UPDATE domain_categories SET ${fields.join(', ')} WHERE id = ?`;

		await this.mariaClient.execute(query, params);
	}

	async delete(id: number): Promise<void>
	{
		const query = 'DELETE FROM domain_categories WHERE id = ?';
		await this.mariaClient.execute(query, [id]);
	}

	// Domain Category Mapping methods
	async assignDomainToCategory(mapping: DomainCategoryMapping): Promise<void>
	{
		const query = `
      INSERT INTO domain_category_mapping (domain, category_id, confidence_score)
      VALUES (?, ?, ?)
      ON DUPLICATE KEY UPDATE confidence_score = VALUES(confidence_score), assigned_at = CURRENT_TIMESTAMP
    `;
		const params = [mapping.domain, mapping.categoryId, mapping.confidenceScore];

		await this.mariaClient.execute(query, params);
	}

	async getDomainCategories(domain: string): Promise<DomainCategoryMapping[]>
	{
		const query = `
      SELECT dcm.*, dc.name as category_name
      FROM domain_category_mapping dcm
      JOIN domain_categories dc ON dcm.category_id = dc.id
      WHERE dcm.domain = ?
      ORDER BY dcm.confidence_score DESC
    `;
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [domain]);

		return result.rows.map(row => ({
			domain: row.domain,
			categoryId: row.category_id,
			confidenceScore: row.confidence_score,
			assignedAt: row.assigned_at,
		}));
	}

	private mapRowToCategory(row: RowDataPacket): DomainCategory
	{
		return {
			id: row.id,
			name: row.name,
			description: row.description,
			parentCategoryId: row.parent_category_id,
			createdAt: row.created_at,
		};
	}
}

/**
 * Repository pattern for Backlinks
 */
export class BacklinkRepository
{
	constructor(private mariaClient: MariaClient) {}

	async create(backlink: Backlink): Promise<string>
	{
		const id = backlink.id || this.generateId();
		const query = `
      INSERT INTO backlinks (id, source_domain, target_domain, link_quality_score,
                           anchor_text, link_type, discovered_at, last_verified, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
		const params = [
			id,
			backlink.sourceDomain,
			backlink.targetDomain,
			backlink.linkQualityScore,
			backlink.anchorText,
			backlink.linkType,
			backlink.discoveredAt || new Date(),
			backlink.lastVerified,
			backlink.isActive,
		];

		await this.mariaClient.execute(query, params);
		return id;
	}

	async findById(id: string): Promise<Backlink | null>
	{
		const query = 'SELECT * FROM backlinks WHERE id = ?';
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [id]);

		if (result.rows.length === 0) return null;
		return this.mapRowToBacklink(result.rows[0]);
	}

	async findByTargetDomain(domain: string, limit: number = 100): Promise<Backlink[]>
	{
		const query = `
      SELECT * FROM backlinks
      WHERE target_domain = ? AND is_active = TRUE
      ORDER BY link_quality_score DESC, discovered_at DESC
      LIMIT ?
    `;
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [domain, limit]);

		return result.rows.map(row => this.mapRowToBacklink(row));
	}

	async findBySourceDomain(domain: string, limit: number = 100): Promise<Backlink[]>
	{
		const query = `
      SELECT * FROM backlinks
      WHERE source_domain = ? AND is_active = TRUE
      ORDER BY discovered_at DESC
      LIMIT ?
    `;
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [domain, limit]);

		return result.rows.map(row => this.mapRowToBacklink(row));
	}

	async getBacklinkStats(domain: string): Promise<{
		totalBacklinks: number; averageQuality: number; uniqueDomains: number
	}>
	{
		const query = `
      SELECT
        COUNT(*) as total_backlinks,
        AVG(link_quality_score) as average_quality,
        COUNT(DISTINCT source_domain) as unique_domains
      FROM backlinks
      WHERE target_domain = ? AND is_active = TRUE
    `;
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [domain]);

		const row = result.rows[0];
		return {
			totalBacklinks: row.total_backlinks || 0,
			averageQuality: row.average_quality || 0,
			uniqueDomains: row.unique_domains || 0,
		};
	}

	async update(id: string, backlink: Partial<Backlink>): Promise<void>
	{
		const fields = [];
		const params = [];

		if (backlink.linkQualityScore !== undefined)
		{
			fields.push('link_quality_score = ?');
			params.push(backlink.linkQualityScore);
		}
		if (backlink.lastVerified !== undefined)
		{
			fields.push('last_verified = ?');
			params.push(backlink.lastVerified);
		}
		if (backlink.isActive !== undefined)
		{
			fields.push('is_active = ?');
			params.push(backlink.isActive);
		}

		if (fields.length === 0) return;

		params.push(id);
		const query = `UPDATE backlinks SET ${fields.join(', ')} WHERE id = ?`;

		await this.mariaClient.execute(query, params);
	}

	async delete(id: string): Promise<void>
	{
		const query = 'DELETE FROM backlinks WHERE id = ?';
		await this.mariaClient.execute(query, [id]);
	}

	private mapRowToBacklink(row: RowDataPacket): Backlink
	{
		return {
			id: row.id,
			sourceDomain: row.source_domain,
			targetDomain: row.target_domain,
			linkQualityScore: row.link_quality_score,
			anchorText: row.anchor_text,
			linkType: row.link_type,
			discoveredAt: row.discovered_at,
			lastVerified: row.last_verified,
			isActive: row.is_active,
		};
	}

	private generateId(): string
	{
		return `bl_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
	}
}

/**
 * Repository pattern for Domain Whois
 */
export class DomainWhoisRepository
{
	constructor(private mariaClient: MariaClient) {}

	async create(whois: DomainWhois): Promise<void>
	{
		const query = `
      INSERT INTO domain_whois (domain, registrar, registration_date, expiration_date,
                               name_servers, registrant_country, registrant_organization, privacy_protected)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        registrar = VALUES(registrar),
        registration_date = VALUES(registration_date),
        expiration_date = VALUES(expiration_date),
        name_servers = VALUES(name_servers),
        registrant_country = VALUES(registrant_country),
        registrant_organization = VALUES(registrant_organization),
        privacy_protected = VALUES(privacy_protected),
        last_updated = CURRENT_TIMESTAMP
    `;
		const params = [
			whois.domain,
			whois.registrar,
			whois.registrationDate,
			whois.expirationDate,
			whois.nameServers,
			whois.registrantCountry,
			whois.registrantOrganization,
			whois.privacyProtected,
		];

		await this.mariaClient.execute(query, params);
	}

	async findByDomain(domain: string): Promise<DomainWhois | null>
	{
		const query = 'SELECT * FROM domain_whois WHERE domain = ?';
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [domain]);

		if (result.rows.length === 0) return null;
		return this.mapRowToWhois(result.rows[0]);
	}

	async findByRegistrar(registrar: string, limit: number = 100): Promise<DomainWhois[]>
	{
		const query = 'SELECT * FROM domain_whois WHERE registrar = ? ORDER BY registration_date DESC LIMIT ?';
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [registrar, limit]);

		return result.rows.map(row => this.mapRowToWhois(row));
	}

	async findExpiringDomains(days: number = 30): Promise<DomainWhois[]>
	{
		const query = `
      SELECT * FROM domain_whois
      WHERE expiration_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
      ORDER BY expiration_date ASC
    `;
		const result = await this.mariaClient.execute<RowDataPacket[]>(query, [days]);

		return result.rows.map(row => this.mapRowToWhois(row));
	}

	async update(domain: string, whois: Partial<DomainWhois>): Promise<void>
	{
		const fields = [];
		const params = [];

		if (whois.registrar !== undefined)
		{
			fields.push('registrar = ?');
			params.push(whois.registrar);
		}
		if (whois.registrationDate !== undefined)
		{
			fields.push('registration_date = ?');
			params.push(whois.registrationDate);
		}
		if (whois.expirationDate !== undefined)
		{
			fields.push('expiration_date = ?');
			params.push(whois.expirationDate);
		}
		if (whois.nameServers !== undefined)
		{
			fields.push('name_servers = ?');
			params.push(whois.nameServers);
		}
		if (whois.registrantCountry !== undefined)
		{
			fields.push('registrant_country = ?');
			params.push(whois.registrantCountry);
		}
		if (whois.registrantOrganization !== undefined)
		{
			fields.push('registrant_organization = ?');
			params.push(whois.registrantOrganization);
		}
		if (whois.privacyProtected !== undefined)
		{
			fields.push('privacy_protected = ?');
			params.push(whois.privacyProtected);
		}

		if (fields.length === 0) return;

		fields.push('last_updated = CURRENT_TIMESTAMP');
		params.push(domain);
		const query = `UPDATE domain_whois SET ${fields.join(', ')} WHERE domain = ?`;

		await this.mariaClient.execute(query, params);
	}

	async delete(domain: string): Promise<void>
	{
		const query = 'DELETE FROM domain_whois WHERE domain = ?';
		await this.mariaClient.execute(query, [domain]);
	}

	private mapRowToWhois(row: RowDataPacket): DomainWhois
	{
		return ({
			domain: row.domain,
			registrar: row.registrar,
			registrationDate: row.registration_date,
			expirationDate: row.expiration_date,
			nameServers: row.name_servers,
			registrantCountry: row.registrant_country,
			registrantOrganization: row.registrant_organization,
			privacyProtected: row.privacy_protected,
			lastUpdated: row.last_updated,
		});
	}
}

export default MariaClient;
