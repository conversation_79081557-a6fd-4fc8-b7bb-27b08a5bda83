import ScyllaClient from './ScyllaClient';
import MariaC<PERSON> from './MariaClient';
import RedisClientWrapper from './RedisClient';
import ManticoreClient from './ManticoreClient';
import { logger } from '../utils/Logger';

/**
 * Central database manager for all database connections
 */
class DatabaseManager
{
	private scylla: ScyllaClient | null = null;

	private maria: MariaClient | null = null;

	private redis: RedisClientWrapper | null = null;

	private manticore: ManticoreClient | null = null;

	private logger = logger.getLogger('DatabaseManager');

	/**
	 * Initialize all database connections
	 */
	async initialize(): Promise<void>
	{
		try
		{
			this.logger.info('Initializing database connections...');

			// Initialize ScyllaDB connection
			this.scylla = new ScyllaClient();
			await this.scylla.connect();

			// Initialize MariaDB connection
			this.maria = new MariaClient();
			await this.maria.connect();

			// Initialize Redis connection
			this.redis = new RedisClientWrapper();
			await this.redis.connect();

			// Initialize Manticore Search connection
			this.manticore = new ManticoreClient();
			await this.manticore.connect();

			this.logger.info('All database connections initialized successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize database connections:', error);
			throw error;
		}
	}

	/**
	 * Close all database connections
	 */
	async close(): Promise<void>
	{
		try
		{
			this.logger.info('Closing database connections...');

			if (this.scylla)
			{
				await this.scylla.disconnect();
			}

			if (this.maria)
			{
				await this.maria.disconnect();
			}

			if (this.redis)
			{
				await this.redis.disconnect();
			}

			if (this.manticore)
			{
				await this.manticore.disconnect();
			}

			this.logger.info('All database connections closed');
		}
		catch (error)
		{
			this.logger.error('Error closing database connections:', error);
			throw error;
		}
	}

	/**
	 * Get ScyllaDB client
	 */
	getScyllaClient(): ScyllaClient
	{
		if (!this.scylla)
		{
			throw new Error('ScyllaDB client not initialized');
		}
		return this.scylla;
	}

	/**
	 * Get MariaDB client
	 */
	getMariaClient(): MariaClient
	{
		if (!this.maria)
		{
			throw new Error('MariaDB client not initialized');
		}
		return this.maria;
	}

	/**
	 * Get Redis client
	 */
	getRedisClient(): RedisClientWrapper
	{
		if (!this.redis)
		{
			throw new Error('Redis client not initialized');
		}
		return this.redis;
	}

	/**
	 * Get Manticore Search client
	 */
	getManticoreClient(): ManticoreClient
	{
		if (!this.manticore)
		{
			throw new Error('Manticore client not initialized');
		}
		return this.manticore;
	}

	/**
	 * Health check for all database connections
	 */
	async healthCheck(): Promise<{
		scylla: boolean;
		maria: boolean;
		redis: boolean;
		manticore: boolean;
	}>
	{
		const health = {
			scylla: false,
			maria: false,
			redis: false,
			manticore: false,
		};

		try
		{
			if (this.scylla)
			{
				health.scylla = await this.scylla.healthCheck();
			}

			if (this.maria)
			{
				health.maria = await this.maria.healthCheck();
			}

			if (this.redis)
			{
				health.redis = await this.redis.healthCheck();
			}

			if (this.manticore)
			{
				health.manticore = await this.manticore.healthCheck();
			}
		}
		catch (error)
		{
			this.logger.error('Health check failed:', error);
		}

		return health;
	}
}

export default DatabaseManager;
