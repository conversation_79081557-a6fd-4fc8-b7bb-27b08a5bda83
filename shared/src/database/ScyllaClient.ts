import { Client, types, mapping } from 'cassandra-driver';
import { config } from '../utils/Config';
import { logger } from '../utils/Logger';
import { DomainAnalysis, DomainCrawlJob, DomainRanking } from '../models/DomainModels';

/**
 * ScyllaDB client wrapper with connection management and retry logic
 */
class ScyllaClient
{
	private client: Client | null = null;

	private logger = logger.getLogger('ScyllaClient');

	private isConnected = false;

	private mapper: mapping.Mapper | null = null;

	constructor()
	{
		this.setupClient();
	}

	/**
   * Setup ScyllaDB client configuration
   */
	private setupClient(): void
	{
		const allConfig = config.getAll();

		this.client = new Client({
			contactPoints: allConfig.SCYLLA_HOSTS,
			localDataCenter: allConfig.SCYLLA_LOCAL_DC,
			keyspace: allConfig.SCYLLA_KEYSPACE,
			pooling: {
				maxRequestsPerConnection: 32768,
			},
			socketOptions: {
				connectTimeout: 30000,
				readTimeout: 30000,
			},
			queryOptions: {
				consistency: types.consistencies.localQuorum,
			},
		});
	}

	/**
   * Connect to ScyllaDB
   */
	async connect(): Promise<void>
	{
		try
		{
			if (!this.client)
			{
				this.setupClient();
			}

			await this.client!.connect();
			this.isConnected = true;

			// Setup mapper for object mapping
			this.mapper = new mapping.Mapper(this.client!, {
				models: {
					DomainAnalysis: {
						tables: ['domain_analysis'],
						mappings: new mapping.UnderscoreCqlToCamelCaseMappings(),
					},
					DomainRanking: {
						tables: ['domain_rankings'],
						mappings: new mapping.UnderscoreCqlToCamelCaseMappings(),
					},
					DomainCrawlJob: {
						tables: ['domain_crawl_jobs'],
						mappings: new mapping.UnderscoreCqlToCamelCaseMappings(),
					},
				},
			});

			this.logger.info('Connected to ScyllaDB successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to connect to ScyllaDB:', error);
			throw error;
		}
	}

	/**
   * Disconnect from ScyllaDB
   */
	async disconnect(): Promise<void>
	{
		if (this.client)
		{
			await this.client.shutdown();
			this.isConnected = false;
			this.client = null;
			this.mapper = null;
			this.logger.info('Disconnected from ScyllaDB');
		}
	}

	/**
   * Execute query with retry logic
   */
	async execute(query: string, params: Array<string | number | boolean | null> = [], options: Record<string, unknown> = {}): Promise<types.ResultSet>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('ScyllaDB client not connected');
		}

		const maxRetries = options.maxRetries || 3;
		let lastError: Error;

		for (let attempt = 1; attempt <= maxRetries; attempt++)
		{
			try
			{
				const result = await this.client.execute(query, params, options);
				return result;
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn(`Query attempt ${attempt} failed:`, error);

				if (attempt < maxRetries)
				{
					// eslint-disable-next-line no-await-in-loop
					await this.delay(1000 * attempt);
				}
			}
		}

		this.logger.error('Query failed after all retries:', lastError!);
		throw lastError!;
	}

	/**
   * Execute batch queries
   */
	async batch(queries: Array<{ query: string; params?: unknown[] }>, options: Record<string, unknown> = {}): Promise<types.ResultSet>
	{
		if (!this.isConnected || !this.client)
		{
			throw new Error('ScyllaDB client not connected');
		}

		try
		{
			const result = await this.client.batch(queries, options);
			return result;
		}
		catch (error)
		{
			this.logger.error('Batch execution failed:', error);
			throw error;
		}
	}

	/**
   * Health check
   */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			await this.execute('SELECT now() FROM system.local');
			return true;
		}
		catch (error)
		{
			this.logger.error('ScyllaDB health check failed:', error);
			return false;
		}
	}

	/**
   * Get client instance
   */
	getClient(): Client | null
	{
		return this.client;
	}

	/**
   * Get mapper instance
   */
	getMapper(): mapping.Mapper | null
	{
		return this.mapper;
	}

	// CRUD Operations for Domain Analysis

	/**
   * Insert or update domain analysis data
   */
	async upsertDomainAnalysis(analysis: DomainAnalysis): Promise<void>
	{
		const data = analysis.toScyllaFormat();

		const query = `
      INSERT INTO domain_analysis (
        domain, global_rank, category, category_rank,
        performance_metrics, security_metrics, seo_metrics, technical_metrics,
        technologies, server_info, domain_age_days, registration_date,
        expiration_date, registrar, dns_records, screenshot_urls,
        subdomains, last_crawled, crawl_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

		const params = [
			data.domain,
			data.global_rank,
			data.category,
			data.category_rank,
			data.performance_metrics,
			data.security_metrics,
			data.seo_metrics,
			data.technical_metrics,
			data.technologies,
			data.server_info,
			data.domain_age_days,
			data.registration_date,
			data.expiration_date,
			data.registrar,
			data.dns_records,
			data.screenshot_urls,
			data.subdomains,
			data.last_crawled,
			data.crawl_status,
		];

		await this.execute(query, params);
		this.logger.info(`Domain analysis upserted for: ${analysis.domain}`);
	}

	/**
   * Get domain analysis by domain
   */
	async getDomainAnalysis(domain: string): Promise<DomainAnalysis | null>
	{
		const query = 'SELECT * FROM domain_analysis WHERE domain = ?';
		const result = await this.execute(query, [domain]);

		if (result.rows.length === 0)
		{
			return null;
		}

		const row = result.first();
		return this.mapRowToDomainAnalysis(row);
	}

	/**
   * Get multiple domain analyses
   */
	async getDomainAnalyses(domains: string[]): Promise<DomainAnalysis[]>
	{
		if (domains.length === 0) return [];

		const placeholders = domains.map(() => '?').join(',');
		const query = `SELECT * FROM domain_analysis WHERE domain IN (${placeholders})`;
		const result = await this.execute(query, domains);

		return result.rows.map(row => this.mapRowToDomainAnalysis(row));
	}

	/**
   * Delete domain analysis
   */
	async deleteDomainAnalysis(domain: string): Promise<void>
	{
		const query = 'DELETE FROM domain_analysis WHERE domain = ?';
		await this.execute(query, [domain]);
		this.logger.info(`Domain analysis deleted for: ${domain}`);
	}

	// CRUD Operations for Domain Rankings

	/**
   * Insert or update domain ranking
   */
	async upsertDomainRanking(ranking: DomainRanking): Promise<void>
	{
		const data = ranking.toScyllaFormat();

		const query = `
      INSERT INTO domain_rankings (
        ranking_type, rank, domain, overall_score, performance_score,
        security_score, seo_score, technical_score, backlink_score,
        traffic_estimate, last_updated
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

		const params = [
			data.ranking_type,
			data.rank,
			data.domain,
			data.overall_score,
			data.performance_score,
			data.security_score,
			data.seo_score,
			data.technical_score,
			data.backlink_score,
			data.traffic_estimate,
			data.last_updated,
		];

		await this.execute(query, params);
		this.logger.info(`Domain ranking upserted for: ${ranking.domain} (${ranking.rankingType})`);
	}

	/**
   * Get domain rankings by type
   */
	async getDomainRankings(rankingType: string, limit: number = 100, offset: number = 0): Promise<DomainRanking[]>
	{
		const query = `
      SELECT * FROM domain_rankings
      WHERE ranking_type = ?
      ORDER BY rank ASC
      LIMIT ?
    `;

		const result = await this.execute(query, [rankingType, limit]);
		return result.rows.map(row => this.mapRowToDomainRanking(row));
	}

	/**
   * Get domain ranking for specific domain and type
   */
	async getDomainRanking(domain: string, rankingType: string): Promise<DomainRanking | null>
	{
		const query = 'SELECT * FROM domain_rankings WHERE ranking_type = ? AND domain = ?';
		const result = await this.execute(query, [rankingType, domain]);

		if (result.rows.length === 0)
		{
			return null;
		}

		return this.mapRowToDomainRanking(result.first());
	}

	// CRUD Operations for Domain Crawl Jobs

	/**
   * Insert crawl job
   */
	async insertCrawlJob(job: DomainCrawlJob): Promise<void>
	{
		const query = `
      INSERT INTO domain_crawl_jobs (
        job_id, domain, crawl_type, priority, status, retry_count,
        scheduled_at, started_at, completed_at, error_message,
        user_agent, pages_to_crawl
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

		const params = [
			types.Uuid.fromString(job.jobId),
			job.domain,
			job.crawlType,
			job.priority,
			job.status,
			job.retryCount,
			job.scheduledAt,
			job.startedAt,
			job.completedAt,
			job.errorMessage,
			job.userAgent,
			job.pagesToCrawl,
		];

		await this.execute(query, params);
		this.logger.info(`Crawl job inserted: ${job.jobId} for domain: ${job.domain}`);
	}

	/**
   * Update crawl job
   */
	async updateCrawlJob(job: DomainCrawlJob): Promise<void>
	{
		const query = `
      UPDATE domain_crawl_jobs
      SET status = ?, retry_count = ?, started_at = ?, completed_at = ?, error_message = ?
      WHERE job_id = ?
    `;

		const params = [
			job.status,
			job.retryCount,
			job.startedAt,
			job.completedAt,
			job.errorMessage,
			types.Uuid.fromString(job.jobId),
		];

		await this.execute(query, params);
		this.logger.info(`Crawl job updated: ${job.jobId}`);
	}

	/**
   * Get crawl job by ID
   */
	async getCrawlJob(jobId: string): Promise<DomainCrawlJob | null>
	{
		const query = 'SELECT * FROM domain_crawl_jobs WHERE job_id = ?';
		const result = await this.execute(query, [types.Uuid.fromString(jobId)]);

		if (result.rows.length === 0)
		{
			return null;
		}

		return this.mapRowToCrawlJob(result.first());
	}

	/**
   * Get crawl jobs by domain
   */
	async getCrawlJobsByDomain(domain: string): Promise<DomainCrawlJob[]>
	{
		const query = 'SELECT * FROM domain_crawl_jobs WHERE domain = ? ALLOW FILTERING';
		const result = await this.execute(query, [domain]);

		return result.rows.map(row => this.mapRowToCrawlJob(row));
	}

	/**
   * Get pending crawl jobs
   */
	async getPendingCrawlJobs(limit: number = 50): Promise<DomainCrawlJob[]>
	{
		const query = 'SELECT * FROM domain_crawl_jobs WHERE status = ? ALLOW FILTERING LIMIT ?';
		const result = await this.execute(query, ['pending', limit]);

		return result.rows.map(row => this.mapRowToCrawlJob(row));
	}

	// Utility Methods

	/**
   * Map database row to DomainAnalysis object
   */
	private mapRowToDomainAnalysis(row: types.Row): DomainAnalysis
	{
		const performanceMetrics = row.performance_metrics || new Map();
		const securityMetrics = row.security_metrics || new Map();
		const seoMetrics = row.seo_metrics || new Map();
		const technicalMetrics = row.technical_metrics || new Map();

		return new DomainAnalysis({
			domain: row.domain,
			globalRank: row.global_rank,
			categoryRank: row.category_rank,
			category: row.category,
			performance: {
				loadTime: parseFloat(performanceMetrics.get('load_time') || '0'),
				firstContentfulPaint: parseFloat(performanceMetrics.get('fcp') || '0'),
				largestContentfulPaint: parseFloat(performanceMetrics.get('lcp') || '0'),
				cumulativeLayoutShift: parseFloat(performanceMetrics.get('cls') || '0'),
				firstInputDelay: parseFloat(performanceMetrics.get('fid') || '0'),
				speedIndex: parseFloat(performanceMetrics.get('speed_index') || '0'),
				score: parseFloat(performanceMetrics.get('score') || '0'),
			},
			security: {
				sslGrade: securityMetrics.get('ssl_grade') || '',
				securityHeaders: {},
				vulnerabilities: [],
				certificateInfo: {},
				score: parseFloat(securityMetrics.get('score') || '0'),
			},
			seo: {
				metaTags: {
					title: seoMetrics.get('title') || '',
					description: seoMetrics.get('description') || '',
				},
				structuredData: [],
				sitemap: {},
				robotsTxt: {},
				score: parseFloat(seoMetrics.get('score') || '0'),
			},
			technical: {
				technologies: Array.from(row.technologies || []),
				serverInfo: Object.fromEntries(row.server_info || new Map()),
				httpHeaders: {},
				pageSize: parseInt(technicalMetrics.get('page_size') || '0', 10),
				resourceCount: {},
				score: parseFloat(technicalMetrics.get('score') || '0'),
			},
			domainInfo: {
				age: row.domain_age_days || 0,
				registrationDate: row.registration_date,
				expirationDate: row.expiration_date,
				registrar: row.registrar || '',
				dnsRecords: Object.fromEntries(row.dns_records || new Map()),
			},
			screenshots: row.screenshot_urls || [],
			subdomains: Array.from(row.subdomains || []),
			lastCrawled: row.last_crawled,
			crawlStatus: row.crawl_status || 'pending',
		});
	}

	/**
   * Map database row to DomainRanking object
   */
	private mapRowToDomainRanking(row: types.Row): DomainRanking
	{
		return new DomainRanking({
			domain: row.domain,
			rankingType: row.ranking_type,
			rank: row.rank,
			overallScore: row.overall_score,
			performanceScore: row.performance_score,
			securityScore: row.security_score,
			seoScore: row.seo_score,
			technicalScore: row.technical_score,
			backlinkScore: row.backlink_score,
			trafficEstimate: row.traffic_estimate,
			lastUpdated: row.last_updated,
		});
	}

	/**
   * Map database row to DomainCrawlJob object
   */
	private mapRowToCrawlJob(row: types.Row): DomainCrawlJob
	{
		return new DomainCrawlJob({
			jobId: row.job_id.toString(),
			domain: row.domain,
			crawlType: row.crawl_type,
			priority: row.priority,
			status: row.status,
			retryCount: row.retry_count,
			scheduledAt: row.scheduled_at,
			startedAt: row.started_at,
			completedAt: row.completed_at,
			errorMessage: row.error_message,
			userAgent: row.user_agent,
			pagesToCrawl: row.pages_to_crawl || [],
		});
	}

	/**
   * Delay utility for retry logic
   */
	private delay(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}


export default ScyllaClient;
