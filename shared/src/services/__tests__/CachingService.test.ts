import {
	vi, describe, beforeEach, test, expect,
} from 'vitest';
import { CachingService } from '../CachingService';
import { RedisClientWrapper } from '../database/RedisClient';

// Mock Redis client
vi.mock('../database/RedisClient.js');

const MockedRedisClient = RedisClientWrapper as vi.MockedClass<typeof RedisClientWrapper>;

describe('CachingService', () =>
{
	let cachingService: CachingService;
	let mockRedisClient: vi.Mocked<RedisClientWrapper>;

	beforeEach(() =>
	{
		vi.clearAllMocks();

		mockRedisClient = new MockedRedisClient() as vi.Mocked<RedisClientWrapper>;
		MockedRedisClient.mockImplementation(() => mockRedisClient);

		// Mock Redis client methods
		mockRedisClient.get = vi.fn();
		mockRedisClient.set = vi.fn();
		mockRedisClient.del = vi.fn();
		mockRedisClient.exists = vi.fn();
		mockRedisClient.expire = vi.fn();
		mockRedisClient.ttl = vi.fn();

		cachingService = new CachingService();
	});

	describe('basic caching operations', () =>
	{
		test('should cache and retrieve data', async () =>
		{
			const testData = { domain: 'example.com', rank: 1 };
			const cacheKey = 'test:key';

			mockRedisClient.set.mockResolvedValue('OK');
			mockRedisClient.get.mockResolvedValue(JSON.stringify(testData));

			await cachingService.set(cacheKey, testData, 3600);
			const retrieved = await cachingService.get(cacheKey);

			expect(mockRedisClient.set).toHaveBeenCalledWith(
				cacheKey,
				JSON.stringify(testData),
				'EX',
				3600,
			);
			expect(retrieved).toEqual(testData);
		});

		test('should return null for non-existent keys', async () =>
		{
			mockRedisClient.get.mockResolvedValue(null);

			const result = await cachingService.get('non:existent:key');

			expect(result).toBeNull();
		});

		test('should delete cached data', async () =>
		{
			mockRedisClient.del.mockResolvedValue(1);

			const result = await cachingService.delete('test:key');

			expect(mockRedisClient.del).toHaveBeenCalledWith('test:key');
			expect(result).toBe(true);
		});

		test('should check if key exists', async () =>
		{
			mockRedisClient.exists.mockResolvedValue(1);

			const exists = await cachingService.exists('test:key');

			expect(mockRedisClient.exists).toHaveBeenCalledWith('test:key');
			expect(exists).toBe(true);
		});
	});

	describe('domain-specific caching', () =>
	{
		test('should cache domain ranking data', async () =>
		{
			const rankingData = {
				domain: 'example.com',
				globalRank: 1,
				categoryRank: 1,
				overallScore: 0.95,
				lastUpdated: new Date().toISOString(),
			};

			mockRedisClient.set.mockResolvedValue('OK');

			await cachingService.cacheDomainRanking('example.com', rankingData);

			expect(mockRedisClient.set).toHaveBeenCalledWith(
				'ranking:domain:example.com',
				JSON.stringify(rankingData),
				'EX',
				3600, // 1 hour default TTL
			);
		});

		test('should retrieve cached domain ranking', async () =>
		{
			const rankingData = {
				domain: 'example.com',
				globalRank: 1,
				categoryRank: 1,
				overallScore: 0.95,
			};

			mockRedisClient.get.mockResolvedValue(JSON.stringify(rankingData));

			const result = await cachingService.getDomainRanking('example.com');

			expect(mockRedisClient.get).toHaveBeenCalledWith('ranking:domain:example.com');
			expect(result).toEqual(rankingData);
		});

		test('should cache domain analysis data', async () =>
		{
			const analysisData = {
				domain: 'example.com',
				performanceScore: 0.85,
				securityScore: 0.90,
				seoScore: 0.80,
				lastAnalyzed: new Date().toISOString(),
			};

			mockRedisClient.set.mockResolvedValue('OK');

			await cachingService.cacheDomainAnalysis('example.com', analysisData);

			expect(mockRedisClient.set).toHaveBeenCalledWith(
				'analysis:example.com',
				JSON.stringify(analysisData),
				'EX',
				7200, // 2 hours default TTL
			);
		});
	});

	describe('search result caching', () =>
	{
		test('should cache search results', async () =>
		{
			const searchResults = {
				query: 'technology',
				results: [
					{ domain: 'tech1.com', rank: 1 },
					{ domain: 'tech2.com', rank: 2 },
				],
				totalResults: 2,
				timestamp: new Date().toISOString(),
			};

			mockRedisClient.set.mockResolvedValue('OK');

			await cachingService.cacheSearchResults('technology', searchResults);

			const expectedKey = cachingService.generateSearchKey('technology', {});

			expect(mockRedisClient.set).toHaveBeenCalledWith(
				expectedKey,
				JSON.stringify(searchResults),
				'EX',
				1800, // 30 minutes default TTL
			);
		});

		test('should generate consistent search keys', () =>
		{
			const filters = { category: 'technology', country: 'US' };

			const key1 = cachingService.generateSearchKey('test query', filters);
			const key2 = cachingService.generateSearchKey('test query', filters);

			expect(key1).toBe(key2);
			expect(key1).toContain('search:results:');
		});

		test('should handle complex search filters in key generation', () =>
		{
			const filters = {
				category: 'technology',
				country: 'US',
				minRank: 1,
				maxRank: 100,
				technologies: ['react', 'node.js'],
			};

			const key = cachingService.generateSearchKey('complex query', filters);

			expect(key).toContain('search:results:');
			expect(key.length).toBeGreaterThan(20); // Should be a reasonable hash length
		});
	});

	describe('cache invalidation', () =>
	{
		test('should invalidate domain-related caches', async () =>
		{
			mockRedisClient.del.mockResolvedValue(3);

			await cachingService.invalidateDomainCache('example.com');

			expect(mockRedisClient.del).toHaveBeenCalledWith(
				'ranking:domain:example.com',
				'analysis:example.com',
				'search:results:*example.com*',
			);
		});

		test('should invalidate category-based caches', async () =>
		{
			mockRedisClient.del.mockResolvedValue(5);

			await cachingService.invalidateCategoryCache('technology');

			expect(mockRedisClient.del).toHaveBeenCalledWith(
				'rankings:category:technology:*',
				'search:results:*category:technology*',
			);
		});

		test('should clear all search caches', async () =>
		{
			mockRedisClient.del.mockResolvedValue(10);

			await cachingService.clearSearchCache();

			expect(mockRedisClient.del).toHaveBeenCalledWith('search:results:*');
		});
	});

	describe('cache warming', () =>
	{
		test('should warm popular domain caches', async () =>
		{
			const popularDomains = ['google.com', 'facebook.com', 'amazon.com'];

			// Mock the data fetching function
			const mockDataFetcher = vi.fn().mockImplementation(domain => Promise.resolve({ domain, rank: 1, score: 0.95 }));

			mockRedisClient.set.mockResolvedValue('OK');

			await cachingService.warmPopularDomains(popularDomains, mockDataFetcher);

			expect(mockDataFetcher).toHaveBeenCalledTimes(3);
			expect(mockRedisClient.set).toHaveBeenCalledTimes(3);
		});

		test('should handle errors during cache warming', async () =>
		{
			const domains = ['example.com', 'failing-domain.com'];

			const mockDataFetcher = vi.fn()
				.mockResolvedValueOnce({ domain: 'example.com', rank: 1 })
				.mockRejectedValueOnce(new Error('Data fetch failed'));

			mockRedisClient.set.mockResolvedValue('OK');

			// Should not throw, but handle errors gracefully
			await expect(cachingService.warmPopularDomains(domains, mockDataFetcher))
				.resolves.not.toThrow();

			expect(mockDataFetcher).toHaveBeenCalledTimes(2);
			expect(mockRedisClient.set).toHaveBeenCalledTimes(1); // Only successful one
		});
	});

	describe('cache statistics', () =>
	{
		test('should track cache hit/miss statistics', async () =>
		{
			// Simulate cache hits and misses
			mockRedisClient.get
				.mockResolvedValueOnce('{"cached": "data"}') // hit
				.mockResolvedValueOnce(null) // miss
				.mockResolvedValueOnce('{"more": "data"}'); // hit

			await cachingService.get('key1');
			await cachingService.get('key2');
			await cachingService.get('key3');

			const stats = cachingService.getStatistics();

			expect(stats.hits).toBe(2);
			expect(stats.misses).toBe(1);
			expect(stats.hitRate).toBeCloseTo(0.67, 2);
		});

		test('should reset cache statistics', () =>
		{
			cachingService.resetStatistics();

			const stats = cachingService.getStatistics();

			expect(stats.hits).toBe(0);
			expect(stats.misses).toBe(0);
			expect(stats.hitRate).toBe(0);
		});
	});

	describe('TTL management', () =>
	{
		test('should set custom TTL for cached data', async () =>
		{
			const data = { test: 'data' };
			const customTTL = 7200; // 2 hours

			mockRedisClient.set.mockResolvedValue('OK');

			await cachingService.set('custom:ttl:key', data, customTTL);

			expect(mockRedisClient.set).toHaveBeenCalledWith(
				'custom:ttl:key',
				JSON.stringify(data),
				'EX',
				customTTL,
			);
		});

		test('should get remaining TTL for cached data', async () =>
		{
			mockRedisClient.ttl.mockResolvedValue(1800); // 30 minutes remaining

			const ttl = await cachingService.getTTL('test:key');

			expect(mockRedisClient.ttl).toHaveBeenCalledWith('test:key');
			expect(ttl).toBe(1800);
		});

		test('should extend TTL for existing cache entries', async () =>
		{
			mockRedisClient.expire.mockResolvedValue(1);

			const result = await cachingService.extendTTL('test:key', 3600);

			expect(mockRedisClient.expire).toHaveBeenCalledWith('test:key', 3600);
			expect(result).toBe(true);
		});
	});
});
