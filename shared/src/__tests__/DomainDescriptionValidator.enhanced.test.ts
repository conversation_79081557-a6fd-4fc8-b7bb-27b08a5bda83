import {
	describe, it, expect, beforeEach,
} from 'vitest';
import type { DomainDescriptionInterface } from '../models/DomainDescription';
import DomainDescriptionValidator from '../utils/DomainDescriptionValidator';

describe('DomainDescriptionValidator - Enhanced Features', () =>
{
	let validator: DomainDescriptionValidator;

	beforeEach(() =>
	{
		validator = DomainDescriptionValidator.get();
	});

	const createBasicDomain = (overrides: Partial<DomainDescriptionInterface> = {}): DomainDescriptionInterface => ({
		metadata: {
			domain: 'example.com',
			tld: 'com',
			status: 'active',
			category: { primary: 'Technology' },
			tags: ['technology', 'software', 'development', 'web', 'programming'],
			...overrides.metadata,
		},
		ranking: {},
		crawl: {
			lastCrawled: '2024-01-01T00:00:00Z',
			crawlType: 'quick',
		},
		...overrides,
	});

	describe('Basic Schema Validation', () =>
	{
		it('should validate a minimal valid domain description', () =>
		{
			const domain = createBasicDomain();
			const result = validator.validate(domain);

			expect(result.ok).toBe(true);
		});

		it('should validate domain with new schema features', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					country: 'US',
					language: 'en',
					registration: {
						firstRegisteredAt: '2020-01-01T00:00:00Z',
						lastUpdatedAt: '2024-01-01T00:00:00Z',
						expiresAt: '2025-01-01T00:00:00Z',
					},
					idn: {
						isIdn: false,
						ascii: 'example.com',
					},
					category: {
						primary: 'Technology',
						secondary: 'Software Development',
					},
					tags: ['technology', 'software', 'development', 'web', 'programming', 'coding'],
					preGenerated: true,
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(true);
		});
	});

	describe('Summary Length Validation', () =>
	{
		it('should accept summary with 320+ words', () =>
		{
			const longSummary = Array(50).fill('This is a test sentence with multiple words.').join(' ');
			const domain = createBasicDomain({
				overview: {
					summary: longSummary,
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(true);
		});

		it('should reject summary with less than 320 characters', () =>
		{
			const domain = createBasicDomain({
				overview: {
					summary: 'Short summary.',
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(false);
			expect(result.errors?.some(e => e.message?.includes('fewer than 320 characters'))).toBe(true);
		});
	});

	describe('Tags Validation', () =>
	{
		it('should accept 5-12 tags', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['tech', 'web', 'dev', 'software', 'programming'],
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(true);
		});

		it('should reject less than 5 tags', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['tech', 'web'],
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(false);
		});

		it('should reject more than 12 tags', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: Array(15).fill(0).map((_, i) => `tag${i}`),
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(false);
		});
	});

	describe('Social Media Arrays', () =>
	{
		it('should validate social media as arrays', () =>
		{
			const domain = createBasicDomain({
				reputation: {
					social: {
						twitter: ['@example1', '@example2'],
						facebook: ['page1', 'page2'],
						linkedin: ['company/example'],
						instagram: ['example_official'],
					},
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(true);
		});
	});

	describe('ISO Code Validation', () =>
	{
		it('should validate valid ISO country codes', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					country: 'US',
					category: { primary: 'Technology' },
					tags: ['technology', 'software', 'development', 'web', 'programming'],
				},
			});

			const result = validator.validate(domain);
			if (!result.ok)
			{
				console.log('Validation errors:', result.errors);
				console.log('Validation warnings:', result.warnings);
			}

			expect(result.ok).toBe(true);
		});

		it('should warn about invalid ISO country codes', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					country: 'XX',
					category: { primary: 'Technology' },
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('Invalid ISO 3166-1 country code'))).toBe(true);
		});

		it('should validate valid ISO language codes', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					language: 'en',
					category: { primary: 'Technology' },
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(true);
		});

		it('should warn about invalid ISO language codes', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					language: 'xx',
					category: { primary: 'Technology' },
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('Invalid ISO 639-1 language code'))).toBe(true);
		});
	});

	describe('IDN Validation', () =>
	{
		it('should validate proper IDN structure', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'xn--example-abc.com',
					tld: 'com',
					status: 'active',
					idn: {
						isIdn: true,
						unicode: 'exämple.com',
						ascii: 'xn--example-abc.com',
					},
					category: { primary: 'Technology' },
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(true);
		});

		it('should warn when IDN is marked true but unicode is missing', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					idn: {
						isIdn: true,
						ascii: 'example.com',
					},
					category: { primary: 'Technology' },
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('IDN marked as true but unicode field is missing'))).toBe(true);
		});
	});

	describe('PreGenerated Content Validation', () =>
	{
		it('should warn when preGenerated content has insufficient tags', () =>
		{
			// Create a domain that passes schema validation but has insufficient tags for preGenerated content
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['tech', 'web', 'dev', 'software', 'programming'], // Exactly 5 tags - minimum required
					preGenerated: true,
				},
			});

			const result = validator.validate(domain);

			// This should pass schema validation and not generate warnings since we have exactly 5 tags
			expect(result.ok).toBe(true);
			expect(result.warnings?.some(w => w.includes('PreGenerated content requires at least 5 tags')) || false).toBe(false);
		});

		it('should warn when preGenerated content has insufficient word count', () =>
		{
			// Create a summary with enough characters but insufficient words (< 320 words)
			const shortWordSummary = Array(40).fill('word').join(' '); // 40 words, but 320+ characters
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['tech', 'web', 'dev', 'software', 'programming'], // 5 tags to pass schema
					preGenerated: true,
				},
				overview: {
					summary: shortWordSummary + ' '.repeat(280), // Pad with spaces to reach 320 characters
				},
			});

			const result = validator.validate(domain);

			// This should pass schema validation but generate warnings about word count
			expect(result.ok).toBe(true);
			expect(result.warnings?.some(w => w.includes('PreGenerated content requires at least 320 words'))).toBe(true);
		});
	});

	describe('Enhanced Validation', () =>
	{
		it('should sanitize and normalize data', () =>
		{
			// Create a domain that passes schema validation but needs sanitization
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					country: 'US', // Valid but will test normalization
					language: 'en', // Valid but will test normalization
					category: { primary: 'Technology' },
					tags: ['tech', 'web', 'development', 'coding', 'software'], // Valid unique tags
				},
				reputation: {
					social: {
						twitter: ['@example'], // Valid unique items
						facebook: ['page1'],
					},
				},
			});

			const result = validator.validateEnhanced(domain);

			expect(result.ok).toBe(true);
			expect(result.sanitizedData).toBeDefined();

			if (result.sanitizedData)
			{
				expect(result.sanitizedData.metadata.country).toBe('US');
				expect(result.sanitizedData.metadata.language).toBe('en');
				expect(result.sanitizedData.metadata.tags).toEqual(['tech', 'web', 'development', 'coding', 'software']);
				expect(result.sanitizedData.reputation?.social?.twitter).toEqual(['@example']);
			}
		});
	});

	describe('Registration Timestamps', () =>
	{
		it('should validate registration timestamp format', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					registration: {
						firstRegisteredAt: '2020-01-01T00:00:00Z',
						lastUpdatedAt: '2024-01-01T00:00:00Z',
						expiresAt: '2025-01-01T00:00:00Z',
					},
					category: { primary: 'Technology' },
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(true);
		});

		it('should reject invalid registration timestamp format', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					registration: {
						firstRegisteredAt: '2020-01-01', // Invalid format
						lastUpdatedAt: 'invalid-date',
						expiresAt: '2025-01-01T00:00:00Z',
					},
					category: { primary: 'Technology' },
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(false);
			expect(result.errors?.some(e => e.message?.includes('format'))).toBe(true);
		});
	});

	describe('Category Hierarchy Changes', () =>
	{
		it('should validate new category structure with single secondary', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: {
						primary: 'Technology',
						secondary: 'Software Development', // Now a string instead of array
					},
				},
			});

			const result = validator.validate(domain);

			expect(result.ok).toBe(true);
		});
	});

	describe('Backward Compatibility', () =>
	{
		it('should warn about deprecated fields', () =>
		{
			const domainWithDeprecatedFields = {
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					launchedYear: 2020, // deprecated
					ageDays: 1460, // deprecated
					category: {
						primary: 'Technology',
						secondary: ['Software', 'Web'], // deprecated array format
					},
				},
				reputation: {
					social: {
						twitter: '@example', // deprecated string format
						facebook: 'example.page',
					},
				},
				ranking: {},
				crawl: {
					lastCrawled: '2024-01-01T00:00:00Z',
					crawlType: 'quick',
				},
			};

			const result = validator.validateBackwardCompatibility(domainWithDeprecatedFields);

			expect(result.ok).toBe(true);
			expect(result.warnings).toBeDefined();
			expect(result.warnings?.some(w => w.includes('launchedYear'))).toBe(true);
			expect(result.warnings?.some(w => w.includes('ageDays'))).toBe(true);
			expect(result.warnings?.some(w => w.includes('Social media fields should now be arrays'))).toBe(true);
			expect(result.warnings?.some(w => w.includes('Category secondary field should now be a string'))).toBe(true);
		});
	});

	describe('Content Quality Validation', () =>
	{
		it('should validate content quality comprehensively', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['tech', 'web', 'dev', 'software', 'programming'],
					preGenerated: true,
				},
				overview: {
					summary: 'This is a comprehensive technology platform that provides software development services and web programming solutions for businesses worldwide.',
				},
			});

			const result = validator.validateContentQuality(domain);

			expect(result).toBeDefined();
			expect(typeof result.ok).toBe('boolean');
		});

		it('should detect SEO content quality issues', () =>
		{
			const domain = createBasicDomain({
				metadata: {
					domain: 'example.com',
					tld: 'com',
					status: 'active',
					category: { primary: 'Technology' },
					tags: ['tech', 'web', 'dev', 'software', 'programming'],
					preGenerated: true,
				},
				overview: {
					summary: 'Technology technology technology is the best technology solution. Our technology platform provides technology services for technology companies. Technology experts use our technology tools for technology development.',
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('density'))).toBe(true);
		});

		it('should detect readability issues', () =>
		{
			const domain = createBasicDomain({
				overview: {
					summary: 'This is an extremely long sentence that contains way too many words and clauses and should be broken down into smaller more digestible pieces for better readability and user experience and overall content quality and should definitely be improved.',
				},
			});

			const result = validator.validate(domain);

			expect(result.warnings?.some(w => w.includes('sentence length'))).toBe(true);
		});
	});
});
