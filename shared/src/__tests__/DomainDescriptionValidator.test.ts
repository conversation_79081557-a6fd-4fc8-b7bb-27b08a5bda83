import { describe, it, expect } from 'vitest';
import DomainDescriptionValidator from '../utils/DomainDescriptionValidator';

const makeValid = () => ({
	metadata: {
		domain: 'example.com',
		tld: 'com',
		status: 'active',
		category: { primary: 'general' },
		tags: ['business', 'technology', 'web', 'internet', 'services'],
	},
	overview: {},
	technical: {},
	seo: {},
	ranking: {},
	crawl: { lastCrawled: new Date().toISOString(), crawlType: 'quick' },
});

const makeValidPreGenerated = () => ({
	metadata: {
		domain: 'example.com',
		tld: 'com',
		status: 'active',
		category: { primary: 'technology', secondary: 'software' },
		tags: ['business', 'technology', 'web', 'internet', 'services', 'software'],
		preGenerated: true,
		country: 'US',
		language: 'en',
		registration: {
			firstRegisteredAt: '2020-01-01T00:00:00.000Z',
			lastUpdatedAt: '2023-01-01T00:00:00.000Z',
			expiresAt: '2025-01-01T00:00:00.000Z',
		},
		idn: {
			isIdn: false,
			ascii: 'example.com',
		},
	},
	overview: {
		summary: 'This is a comprehensive domain description that contains more than three hundred and twenty words to meet the SEO optimization requirements for preGenerated content. The domain example.com represents a technology-focused website that provides various software services and solutions to businesses worldwide. The platform offers innovative tools and resources designed to help organizations streamline their operations and improve their digital presence. With a strong emphasis on user experience and cutting-edge technology, this domain serves as a hub for professionals seeking reliable software solutions. The website features detailed information about various products and services, including comprehensive documentation, tutorials, and support resources. Users can access a wide range of tools designed to enhance productivity and efficiency in their daily operations. The platform maintains high standards of security and performance, ensuring that all users have access to reliable and secure services. Regular updates and improvements are made to keep the platform current with the latest industry trends and technological advancements. The domain has established itself as a trusted resource in the technology sector, providing valuable insights and solutions to its growing user base.',
	},
	technical: {},
	seo: {},
	ranking: {},
	crawl: { lastCrawled: new Date().toISOString(), crawlType: 'quick' },
});

describe('DomainDescriptionValidator', () =>
{
	describe('Basic Validation', () =>
	{
		it('validates a correct object', () =>
		{
			const v = DomainDescriptionValidator.get();
			const res = v.validate(makeValid());

			expect(res.ok).toBe(true);
		});

		it('fails for missing required fields', () =>
		{
			const v = DomainDescriptionValidator.get();
			const res = v.validate({});

			expect(res.ok).toBe(false);
			expect(res.errors && res.errors.length).toBeGreaterThan(0);
		});

		it('fails for invalid domain format', () =>
		{
			const v = DomainDescriptionValidator.get();
			const bad = makeValid();
			bad.metadata.domain = 'not a domain';
			const res = v.validate(bad);

			expect(res.ok).toBe(false);
		});

		it('fails for missing tags', () =>
		{
			const v = DomainDescriptionValidator.get();
			const bad = makeValid();
			delete bad.metadata.tags;
			const res = v.validate(bad);

			expect(res.ok).toBe(false);
		});

		it('assert throws with errors', () =>
		{
			const v = DomainDescriptionValidator.get();

			expect(() => v.assert({})).toThrowError();
		});
	});

	describe('PreGenerated Content Validation', () =>
	{
		it('validates correct preGenerated content', () =>
		{
			const v = DomainDescriptionValidator.get();
			const res = v.validate(makeValidPreGenerated());

			expect(res.ok).toBe(true);
		});

		it('warns when preGenerated content lacks required tags', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValidPreGenerated();
			data.metadata.tags = ['one', 'two']; // Less than 5 tags
			const res = v.validate(data);

			expect(res.warnings).toContain('PreGenerated content requires at least 5 tags, found 2');
		});

		it('warns when preGenerated content has too many tags', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValidPreGenerated();
			data.metadata.tags = Array.from({ length: 15 }, (_, i) => `tag${i}`); // More than 12 tags
			const res = v.validate(data);

			expect(res.warnings).toContain('PreGenerated content allows maximum 12 tags, found 15');
		});

		it('warns when preGenerated content lacks summary', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValidPreGenerated();
			delete data.overview.summary;
			const res = v.validate(data);

			expect(res.warnings).toContain('PreGenerated content requires summary to be present');
		});

		it('warns when preGenerated summary is too short', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValidPreGenerated();
			data.overview.summary = 'This is a short summary with less than 320 words.';
			const res = v.validate(data);
			// Count words to see what the actual count is
			const wordCount = data.overview.summary.trim().split(/\s+/).filter(word => word.length > 0).length;

			expect(res.warnings).toContain(`PreGenerated content requires at least 320 words in summary, found ${wordCount}`);
		});

		it('warns when preGenerated content lacks primary category', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValidPreGenerated();
			delete data.metadata.category.primary;
			const res = v.validate(data);

			expect(res.warnings).toContain('PreGenerated content requires primary category to be present');
		});

		it('warns about SEO content quality issues in preGenerated content', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValidPreGenerated();
			// Create content with keyword over-optimization
			data.overview.summary = `Technology technology technology is the best technology solution. Our technology platform provides technology services for technology companies. Technology experts use our technology tools for technology development. Technology innovation drives our technology products forward. ${ Array(50).fill('Additional content to meet word count requirements.').join(' ')}`;
			const res = v.validate(data);

			expect(res.warnings?.some(w => w.includes('density'))).toBe(true);
		});

		it('warns about readability issues in all content', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			// Create content with long sentences
			const longSentence = 'This is an extremely long sentence that contains way too many words and clauses and should be broken down into smaller more digestible pieces for better readability and user experience and overall content quality.';
			data.overview = { summary: Array(20).fill(longSentence).join(' ') };
			const res = v.validate(data);

			expect(res.warnings?.some(w => w.includes('sentence length'))).toBe(true);
		});
	});

	describe('ISO Code Validation', () =>
	{
		it('warns for invalid country code', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.country = 'XX';
			const res = v.validate(data);

			expect(res.warnings).toContain('Invalid ISO 3166-1 country code: XX');
		});

		it('warns for invalid language code', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.language = 'xx';
			const res = v.validate(data);

			expect(res.warnings).toContain('Invalid ISO 639-1 language code: xx');
		});

		it('accepts valid country and language codes', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.country = 'US';
			data.metadata.language = 'en';
			const res = v.validate(data);

			expect(res.warnings?.some(w => w.includes('Invalid ISO'))).toBeFalsy();
		});
	});

	describe('Registration Timestamp Validation', () =>
	{
		it('warns for invalid firstRegisteredAt timestamp', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.registration = { firstRegisteredAt: 'invalid-date' };
			const res = v.validate(data);

			expect(res.warnings).toContain('Invalid firstRegisteredAt timestamp format: invalid-date');
		});

		it('warns for invalid lastUpdatedAt timestamp', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.registration = { lastUpdatedAt: '2023-13-45T25:70:80.000Z' };
			const res = v.validate(data);

			expect(res.warnings).toContain('Invalid lastUpdatedAt timestamp format: 2023-13-45T25:70:80.000Z');
		});

		it('accepts valid ISO timestamps', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.registration = {
				firstRegisteredAt: '2020-01-01T00:00:00.000Z',
				lastUpdatedAt: '2023-01-01T00:00:00.000Z',
				expiresAt: '2025-01-01T00:00:00.000Z',
			};
			const res = v.validate(data);

			expect(res.warnings?.some(w => w.includes('Invalid') && w.includes('timestamp'))).toBeFalsy();
		});
	});

	describe('IDN Validation', () =>
	{
		it('warns when IDN is true but unicode is missing', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.idn = { isIdn: true, ascii: 'example.com' };
			const res = v.validate(data);

			expect(res.warnings).toContain('IDN marked as true but unicode field is missing');
		});

		it('warns when IDN is true but ascii is missing', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.idn = { isIdn: true, unicode: 'éxample.com' };
			const res = v.validate(data);

			expect(res.warnings).toContain('IDN marked as true but ascii field is missing');
		});

		it('warns when IDN unicode and ascii are the same', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.idn = { isIdn: true, unicode: 'example.com', ascii: 'example.com' };
			const res = v.validate(data);

			expect(res.warnings).toContain('IDN unicode and ascii fields should be different when isIdn is true');
		});

		it('accepts valid IDN configuration', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.idn = { isIdn: true, unicode: 'éxample.com', ascii: 'xn--xample-9ua.com' };
			const res = v.validate(data);

			expect(res.warnings?.some(w => w.includes('IDN'))).toBeFalsy();
		});
	});

	describe('Social Arrays Validation', () =>
	{
		it('warns when social platform is not an array', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.reputation = { social: { twitter: 'not-an-array' as any } };
			const res = v.validate(data);

			expect(res.warnings).toContain('Social twitter field must be an array');
		});

		it('warns when social array contains invalid accounts', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.reputation = { social: { twitter: ['valid-account', '', null as any] } };
			const res = v.validate(data);

			expect(res.warnings?.some(w => w.includes('Social twitter contains invalid account'))).toBeTruthy();
		});

		it('accepts valid social arrays', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.reputation = {
				social: {
					twitter: ['@example'],
					facebook: ['example.page'],
					linkedin: ['company/example'],
					instagram: ['@example_official'],
				},
			};
			const res = v.validate(data);

			expect(res.warnings?.some(w => w.includes('Social'))).toBeFalsy();
		});
	});

	describe('Category Hierarchy Validation', () =>
	{
		it('warns when primary category is missing', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			delete data.metadata.category.primary;
			const res = v.validate(data);

			expect(res.warnings).toContain('Primary category is required');
		});

		it('warns when primary category is empty', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.category.primary = '';
			const res = v.validate(data);

			expect(res.warnings).toContain('Primary category must be a non-empty string');
		});

		it('warns when secondary category is not a string', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.category.secondary = 123 as any;
			const res = v.validate(data);

			expect(res.warnings).toContain('Secondary category must be a string');
		});

		it('warns when secondary category equals primary', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.category = { primary: 'technology', secondary: 'technology' };
			const res = v.validate(data);

			expect(res.warnings).toContain('Secondary category should be different from primary category');
		});
	});

	describe('Enhanced Validation', () =>
	{
		it('returns sanitized data for valid objects', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.country = 'us'; // lowercase
			data.metadata.language = 'EN'; // uppercase
			data.metadata.tags = ['tag1', 'tag2', 'tag1', ' tag3 ']; // duplicates and whitespace

			const res = v.validateEnhanced(data);

			expect(res.sanitizedData?.metadata.country).toBe('US');
			expect(res.sanitizedData?.metadata.language).toBe('en');
			expect(res.sanitizedData?.metadata.tags).toEqual(['tag1', 'tag2', 'tag3']);
		});

		it('sanitizes social arrays', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.reputation = {
				social: {
					twitter: ['@example', '', '@example', '@other'],
					facebook: ['page1', null as any, 'page2'],
				},
			};

			const res = v.validateEnhanced(data);

			expect(res.sanitizedData?.reputation?.social?.twitter).toEqual(['@example', '@other']);
			expect(res.sanitizedData?.reputation?.social?.facebook).toEqual(['page1', 'page2']);
		});
	});

	describe('Backward Compatibility', () =>
	{
		it('warns about deprecated launchedYear field', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = { ...makeValid(), metadata: { ...makeValid().metadata, launchedYear: 2020 } };
			const res = v.validateBackwardCompatibility(data);

			expect(res.warnings).toContain('Field "launchedYear" is deprecated, use "registration.firstRegisteredAt" instead');
		});

		it('warns about deprecated ageDays field', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = { ...makeValid(), metadata: { ...makeValid().metadata, ageDays: 365 } };
			const res = v.validateBackwardCompatibility(data);

			expect(res.warnings).toContain('Field "ageDays" is deprecated, calculate from "registration.firstRegisteredAt" instead');
		});

		it('warns about old social format', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.reputation = { social: { twitter: '@example' as any } };
			const res = v.validateBackwardCompatibility(data);

			expect(res.warnings).toContain('Social media fields should now be arrays instead of strings');
		});

		it('warns about old category secondary format', () =>
		{
			const v = DomainDescriptionValidator.get();
			const data = makeValid();
			data.metadata.category.secondary = ['sub1', 'sub2'] as unknown;
			const res = v.validateBackwardCompatibility(data);

			expect(res.warnings).toContain('Category secondary field should now be a string instead of an array');
		});
	});
});
