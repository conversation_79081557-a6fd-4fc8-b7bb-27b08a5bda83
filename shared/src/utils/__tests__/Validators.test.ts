import { describe, test, expect } from 'vitest';
import {
	validateDomain,
	validateUrl,
	validateEmail,
	sanitizeInput,
	validateRankingScore,
	validateDomainData,
	validateSearchQuery,
} from '../Validators';

describe('Validators', () =>
{
	describe('validateDomain', () =>
	{
		test('should validate correct domain names', () =>
		{
			const validDomains = [
				'example.com',
				'sub.example.com',
				'test-site.org',
				'my-domain.co.uk',
				'123domain.net',
			];

			validDomains.forEach((domain) =>
			{
				expect(validateDomain(domain)).toBe(true);
			});
		});

		test('should reject invalid domain names', () =>
		{
			const invalidDomains = [
				'',
				'invalid',
				'http://example.com',
				'example..com',
				'-example.com',
				'example-.com',
				'example.com-',
				'very-long-domain-name-that-exceeds-the-maximum-allowed-length-for-domain-names.com',
			];

			invalidDomains.forEach((domain) =>
			{
				expect(validateDomain(domain)).toBe(false);
			});
		});
	});

	describe('validateUrl', () =>
	{
		test('should validate correct URLs', () =>
		{
			const validUrls = [
				'https://example.com',
				'http://example.com',
				'https://sub.example.com/path',
				'https://example.com:8080/path?query=value',
				'https://example.com/path#fragment',
			];

			validUrls.forEach((url) =>
			{
				expect(validateUrl(url)).toBe(true);
			});
		});

		test('should reject invalid URLs', () =>
		{
			const invalidUrls = [
				'',
				'not-a-url',
				'ftp://example.com',
				'https://',
				'https://example',
				'javascript:alert(1)',
			];

			invalidUrls.forEach((url) =>
			{
				expect(validateUrl(url)).toBe(false);
			});
		});
	});

	describe('validateEmail', () =>
	{
		test('should validate correct email addresses', () =>
		{
			const validEmails = [
				'<EMAIL>',
				'<EMAIL>',
				'<EMAIL>',
				'<EMAIL>',
			];

			validEmails.forEach((email) =>
			{
				expect(validateEmail(email)).toBe(true);
			});
		});

		test('should reject invalid email addresses', () =>
		{
			const invalidEmails = [
				'',
				'invalid',
				'@example.com',
				'user@',
				'user@.com',
				'<EMAIL>',
			];

			invalidEmails.forEach((email) =>
			{
				expect(validateEmail(email)).toBe(false);
			});
		});
	});

	describe('sanitizeInput', () =>
	{
		test('should sanitize HTML and script tags', () =>
		{
			const maliciousInputs = [
				'<script>alert("xss")</script>',
				'<img src="x" onerror="alert(1)">',
				'<div onclick="alert(1)">content</div>',
				'javascript:alert(1)',
			];

			maliciousInputs.forEach((input) =>
			{
				const sanitized = sanitizeInput(input);

				expect(sanitized).not.toContain('<script>');
				expect(sanitized).not.toContain('javascript:');
				expect(sanitized).not.toContain('onerror');
				expect(sanitized).not.toContain('onclick');
			});
		});

		test('should preserve safe content', () =>
		{
			const safeInputs = [
				'Hello World',
				'example.com',
				'<EMAIL>',
				'This is a safe string with numbers 123',
			];

			safeInputs.forEach((input) =>
			{
				const sanitized = sanitizeInput(input);

				expect(sanitized).toBe(input);
			});
		});
	});

	describe('validateRankingScore', () =>
	{
		test('should validate scores within range', () =>
		{
			const validScores = [0, 0.5, 1, 0.123, 0.999];

			validScores.forEach((score) =>
			{
				expect(validateRankingScore(score)).toBe(true);
			});
		});

		test('should reject scores outside range', () =>
		{
			const invalidScores = [-1, 1.1, -0.1, 2, NaN, Infinity];

			invalidScores.forEach((score) =>
			{
				expect(validateRankingScore(score)).toBe(false);
			});
		});
	});

	describe('validateDomainData', () =>
	{
		test('should validate complete domain data', () =>
		{
			const validDomainData = {
				domain: 'example.com',
				category: 'technology',
				country: 'US',
				performanceMetrics: {
					loadTime: 1200,
					firstContentfulPaint: 800,
					largestContentfulPaint: 1500,
				},
				securityMetrics: {
					sslGrade: 'A',
					usesHttps: true,
				},
				seoMetrics: {
					metaTags: {
						title: 'Example Site',
						description: 'A test site',
					},
				},
			};

			expect(validateDomainData(validDomainData)).toBe(true);
		});

		test('should reject invalid domain data', () =>
		{
			const invalidDomainData = [
				{}, // Empty object
				{ domain: 'invalid-domain' }, // Invalid domain
				{ domain: 'example.com', category: '' }, // Empty category
				{ domain: 'example.com', performanceMetrics: { loadTime: -1 } }, // Invalid metrics
			];

			invalidDomainData.forEach((data) =>
			{
				expect(validateDomainData(data)).toBe(false);
			});
		});
	});

	describe('validateSearchQuery', () =>
	{
		test('should validate safe search queries', () =>
		{
			const validQueries = [
				'example.com',
				'technology websites',
				'best domains 2024',
				'site:example.com',
			];

			validQueries.forEach((query) =>
			{
				expect(validateSearchQuery(query)).toBe(true);
			});
		});

		test('should reject dangerous search queries', () =>
		{
			const dangerousQueries = [
				'<script>alert(1)</script>',
				'javascript:alert(1)',
				'SELECT * FROM domains',
				'DROP TABLE domains',
			];

			dangerousQueries.forEach((query) =>
			{
				expect(validateSearchQuery(query)).toBe(false);
			});
		});

		test('should reject overly long queries', () =>
		{
			const longQuery = 'a'.repeat(1001);

			expect(validateSearchQuery(longQuery)).toBe(false);
		});
	});
});
