#!/usr/bin/env node

/**
 * Database Migration Utility
 * Handles database schema migrations for ScyllaDB, MariaDB, and Manticore
 */

import { readFileSync, readdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { createHash } from 'crypto';
import cassandra from 'cassandra-driver';
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class DatabaseMigrator
{
	constructor()
	{
		this.scyllaClient = null;
		this.mariaClient = null;
		this.manticoreClient = null;
	}

	/**
	 * Initialize database connections
	 */
	async initialize()
	{
		try
		{
			// Initialize ScyllaDB connection
			this.scyllaClient = new cassandra.Client({
				contactPoints: (process.env.SCYLLA_HOSTS || 'localhost:9042').split(','),
				localDataCenter: 'datacenter1',
				keyspace: 'domain_ranking'
			});

			// Initialize MariaDB connection
			this.mariaClient = await mysql.createConnection({
				host: process.env.MARIA_HOST || 'localhost',
				port: process.env.MARIA_PORT || 3306,
				user: process.env.MARIA_USER || 'root',
				password: process.env.MARIA_PASSWORD || '',
				database: process.env.MARIA_DATABASE || 'domain_ranking'
			});

			// Initialize Manticore connection
			this.manticoreClient = await mysql.createConnection({
				host: process.env.MANTICORE_HOST || 'localhost',
				port: process.env.MANTICORE_PORT || 9306,
				user: '',
				password: ''
			});

			console.log('✅ Database connections initialized');
		}
		catch (error)
		{
			console.error('❌ Failed to initialize database connections:', error);
			throw error;
		}
	}

	/**
	 * Run ScyllaDB migrations
	 */
	async runScyllaMigrations()
	{
		console.log('🔄 Running ScyllaDB migrations...');

		const migrationsDir = join(__dirname, '../migrations');
		const migrationFiles = readdirSync(migrationsDir)
			.filter(file => file.endsWith('.cql'))
			.sort();

		for (const file of migrationFiles)
		{
			const migrationId = file.replace('.cql', '');
			const filePath = join(migrationsDir, file);

			// Check if migration already applied
			try
			{
				const result = await this.scyllaClient.execute(
					'SELECT * FROM schema_migrations WHERE migration_id = ?',
					[migrationId]
				);

				if (result.rows.length > 0)
				{
					console.log(`⏭️  Migration ${migrationId} already applied`);
					continue;
				}
			}
			catch (error)
			{
				// Migration table might not exist yet, continue
			}

			// Read and execute migration
			const migrationSQL = readFileSync(filePath, 'utf8');
			const checksum = createHash('sha256').update(migrationSQL).digest('hex');

			try
			{
				// Split by semicolon and execute each statement
				const statements = migrationSQL
					.split(';')
					.map(stmt => stmt.trim())
					.filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

				for (const statement of statements)
				{
					await this.scyllaClient.execute(statement);
				}

				console.log(`✅ Applied ScyllaDB migration: ${migrationId}`);
			}
			catch (error)
			{
				console.error(`❌ Failed to apply ScyllaDB migration ${migrationId}:`, error);
				throw error;
			}
		}
	}

	/**
	 * Run MariaDB migrations
	 */
	async runMariaMigrations()
	{
		console.log('🔄 Running MariaDB migrations...');

		// Create migrations table if it doesn't exist
		await this.mariaClient.execute(`
			CREATE TABLE IF NOT EXISTS schema_migrations (
				migration_id VARCHAR(100) PRIMARY KEY,
				migration_name VARCHAR(255),
				applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				checksum VARCHAR(64),
				success BOOLEAN DEFAULT TRUE
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
		`);

		const migrationsDir = join(__dirname, '../mariadb');
		const migrationFiles = readdirSync(migrationsDir)
			.filter(file => file.endsWith('.sql'))
			.sort();

		for (const file of migrationFiles)
		{
			const migrationId = file.replace('.sql', '');
			const filePath = join(migrationsDir, file);

			// Check if migration already applied
			const [rows] = await this.mariaClient.execute(
				'SELECT * FROM schema_migrations WHERE migration_id = ?',
				[migrationId]
			);

			if (rows.length > 0)
			{
				console.log(`⏭️  Migration ${migrationId} already applied`);
				continue;
			}

			// Read and execute migration
			const migrationSQL = readFileSync(filePath, 'utf8');
			const checksum = createHash('sha256').update(migrationSQL).digest('hex');

			try
			{
				// Execute the migration
				await this.mariaClient.execute(migrationSQL);

				// Record the migration
				await this.mariaClient.execute(
					'INSERT INTO schema_migrations (migration_id, migration_name, checksum) VALUES (?, ?, ?)',
					[migrationId, file, checksum]
				);

				console.log(`✅ Applied MariaDB migration: ${migrationId}`);
			}
			catch (error)
			{
				console.error(`❌ Failed to apply MariaDB migration ${migrationId}:`, error);
				throw error;
			}
		}
	}

	/**
	 * Run Manticore migrations
	 */
	async runManticoreMigrations()
	{
		console.log('🔄 Running Manticore migrations...');

		const migrationsDir = join(__dirname, '../manticore');
		const migrationFiles = readdirSync(migrationsDir)
			.filter(file => file.endsWith('.sql'))
			.sort();

		for (const file of migrationFiles)
		{
			const migrationId = file.replace('.sql', '');
			const filePath = join(migrationsDir, file);

			// Read and execute migration
			const migrationSQL = readFileSync(filePath, 'utf8');

			try
			{
				// Split by semicolon and execute each statement
				const statements = migrationSQL
					.split(';')
					.map(stmt => stmt.trim())
					.filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

				for (const statement of statements)
				{
					if (statement.toLowerCase().includes('create table'))
					{
						await this.manticoreClient.execute(statement);
					}
				}

				console.log(`✅ Applied Manticore migration: ${migrationId}`);
			}
			catch (error)
			{
				console.error(`❌ Failed to apply Manticore migration ${migrationId}:`, error);
				// Don't throw for Manticore as indexes might already exist
			}
		}
	}

	/**
	 * Load sample data
	 */
	async loadSampleData()
	{
		console.log('🔄 Loading sample data...');

		try
		{
			// Load domain sample data
			const domainsData = JSON.parse(
				readFileSync(join(__dirname, '../sample-data/domains.json'), 'utf8')
			);

			for (const domain of domainsData)
			{
				// Insert into ScyllaDB
				await this.scyllaClient.execute(`
					INSERT INTO domain_analysis (
						domain, global_rank, category, category_rank,
						performance_metrics, security_metrics, seo_metrics, technical_metrics,
						technologies, domain_age_days, registrar, overall_score,
						performance_score, security_score, seo_score, technical_score,
						backlink_score, traffic_estimate, last_crawled, crawl_status
					) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
				`, [
					domain.domain,
					domain.global_rank,
					domain.category,
					domain.category_rank,
					domain.performance_metrics,
					domain.security_metrics,
					domain.seo_metrics,
					domain.technical_metrics,
					domain.technologies,
					domain.domain_age_days,
					domain.registrar,
					domain.overall_score,
					domain.performance_score,
					domain.security_score,
					domain.seo_score,
					domain.technical_score,
					domain.backlink_score,
					domain.traffic_estimate,
					new Date(),
					'completed'
				]);

				// Insert ranking data
				await this.scyllaClient.execute(`
					INSERT INTO domain_rankings (
						ranking_type, rank, domain, overall_score,
						performance_score, security_score, seo_score,
						technical_score, backlink_score, traffic_estimate, last_updated
					) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
				`, [
					'global',
					domain.global_rank,
					domain.domain,
					domain.overall_score,
					domain.performance_score,
					domain.security_score,
					domain.seo_score,
					domain.technical_score,
					domain.backlink_score,
					domain.traffic_estimate,
					new Date()
				]);

				// Insert category ranking
				await this.scyllaClient.execute(`
					INSERT INTO domain_rankings (
						ranking_type, rank, domain, overall_score,
						performance_score, security_score, seo_score,
						technical_score, backlink_score, traffic_estimate, last_updated
					) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
				`, [
					`category:${domain.category}`,
					domain.category_rank,
					domain.domain,
					domain.overall_score,
					domain.performance_score,
					domain.security_score,
					domain.seo_score,
					domain.technical_score,
					domain.backlink_score,
					domain.traffic_estimate,
					new Date()
				]);
			}

			console.log(`✅ Loaded ${domainsData.length} sample domains`);
		}
		catch (error)
		{
			console.error('❌ Failed to load sample data:', error);
			throw error;
		}
	}

	/**
	 * Check migration status
	 */
	async checkStatus()
	{
		console.log('📊 Migration Status:');

		try
		{
			// Check ScyllaDB migrations
			const scyllaResult = await this.scyllaClient.execute(
				'SELECT migration_id, migration_name, applied_at FROM schema_migrations'
			);
			console.log('\n🗄️  ScyllaDB Migrations:');
			scyllaResult.rows.forEach((row) =>
			{
				console.log(`  ✅ ${row.migration_id} - ${row.migration_name} (${row.applied_at})`);
			});
		}
		catch (error)
		{
			console.log('  ❌ ScyllaDB migration table not found');
		}

		try
		{
			// Check MariaDB migrations
			const [mariaRows] = await this.mariaClient.execute(
				'SELECT migration_id, migration_name, applied_at FROM schema_migrations'
			);
			console.log('\n🗄️  MariaDB Migrations:');
			mariaRows.forEach((row) =>
			{
				console.log(`  ✅ ${row.migration_id} - ${row.migration_name} (${row.applied_at})`);
			});
		}
		catch (error)
		{
			console.log('  ❌ MariaDB migration table not found');
		}

		console.log('\n📈 Data Status:');
		try
		{
			const domainCount = await this.scyllaClient.execute(
				'SELECT COUNT(*) as count FROM domain_analysis'
			);
			console.log(`  📊 Domains in database: ${domainCount.rows[0].count}`);
		}
		catch (error)
		{
			console.log('  ❌ Could not count domains');
		}
	}

	/**
	 * Close database connections
	 */
	async close()
	{
		if (this.scyllaClient)
		{
			await this.scyllaClient.shutdown();
		}
		if (this.mariaClient)
		{
			await this.mariaClient.end();
		}
		if (this.manticoreClient)
		{
			await this.manticoreClient.end();
		}
	}
}

// Main execution
async function main()
{
	const migrator = new DatabaseMigrator();

	try
	{
		await migrator.initialize();

		const args = process.argv.slice(2);
		const command = args[0] || 'migrate';

		switch (command)
		{
			case 'migrate':
				await migrator.runScyllaMigrations();
				await migrator.runMariaMigrations();
				await migrator.runManticoreMigrations();
				console.log('✅ All migrations completed successfully');
				break;

			case 'sample-data':
				await migrator.loadSampleData();
				console.log('✅ Sample data loaded successfully');
				break;

			case 'status':
				await migrator.checkStatus();
				break;

			case 'init':
				await migrator.runScyllaMigrations();
				await migrator.runMariaMigrations();
				await migrator.runManticoreMigrations();
				await migrator.loadSampleData();
				console.log('✅ Database initialization completed successfully');
				break;

			default:
				console.log('Usage: node migrate.js [migrate|sample-data|status|init]');
				break;
		}
	}
	catch (error)
	{
		console.error('❌ Migration failed:', error);
		process.exit(1);
	}
	finally
	{
		await migrator.close();
	}
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`)
{
	main();
}

export { DatabaseMigrator };
