import Logger from '@shared';
import type { Logger as PinoLogger } from 'pino';
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { anthropic } from '@ai-sdk/anthropic';
import { google } from '@ai-sdk/google';

interface DomainContent
{
	domain: string;
	title?: string;
	description?: string;
	content?: string;
	aboutContent?: string;
	contactInfo?: string;
	technologies?: string[];
	industry?: string;
	language?: string;
}

interface GeneratedDescription
{
	shortDescription: string;
	longDescription: string;
	companyInfo: {
		name?: string;
		industry?: string;
		services?: string[];
		location?: string;
		founded?: string;
	};
	keyFeatures: string[];
	targetAudience: string;
	businessModel?: string;
	confidence: number;
	generatedAt: Date;
}

interface AIProviderConfig
{
	provider: 'openai' | 'anthropic' | 'google';
	model: string;
	apiKey: string;
	maxTokens?: number;
	temperature?: number;
}

/**
 * AI-powered domain description generator
 * Analyzes website content to generate human-readable descriptions and company information
 */
class DomainDescriptionGenerator
{
	private readonly logger: PinoLogger;

	private readonly providers: Map<string, AIProviderConfig>;

	private readonly defaultProvider: string;

	constructor(logger: PinoLogger, providers: AIProviderConfig[] = [])
	{
		this.logger = logger;
		this.providers = new Map();

		// Register providers
		providers.forEach((config) =>
		{
			this.providers.set(config.provider, config);
		});

		// Set default provider (prefer OpenAI, then Anthropic, then Google)
		this.defaultProvider = this.selectDefaultProvider();
	}

	/**
	 * Generate comprehensive domain description from website content
	 */
	async generateDescription(
		domainContent: DomainContent,
		providerName?: string,
	): Promise<GeneratedDescription>
	{
		this.logger.info({
			domain: domainContent.domain,
			provider: providerName || this.defaultProvider,
		}, 'Generating domain description');

		try
		{
			const provider = providerName || this.defaultProvider;
			const config = this.providers.get(provider);

			if (!config)
			{
				throw new Error(`AI provider '${provider}' not configured`);
			}

			const model = this.getModelInstance(config);
			const prompt = this.buildAnalysisPrompt(domainContent);

			const result = await generateText({
				model,
				prompt,
				maxTokens: config.maxTokens || 1000,
				temperature: config.temperature || 0.3,
			});

			const parsedResult = this.parseAIResponse(result.text);

			this.logger.info({
				domain: domainContent.domain,
				provider,
				confidence: parsedResult.confidence,
			}, 'Domain description generated successfully');

			return {
				...parsedResult,
				generatedAt: new Date(),
			};
		}
		catch (error)
		{
			this.logger.error({
				domain: domainContent.domain,
				error: error instanceof Error ? error.message : 'Unknown error',
			}, 'Domain description generation failed');

			// If it's a provider configuration error, re-throw it
			if (error instanceof Error && error.message.includes('AI provider') && error.message.includes('not configured'))
			{
				throw error;
			}

			// Return fallback description for other errors
			return this.generateFallbackDescription(domainContent);
		}
	}

	/**
	 * Generate descriptions for multiple domains in batch
	 */
	async batchGenerateDescriptions(
		domains: DomainContent[],
		providerName?: string,
		concurrency: number = 3,
	): Promise<Map<string, GeneratedDescription>>
	{
		const results = new Map<string, GeneratedDescription>();

		// Process in batches to avoid rate limits
		const batches = this.createBatches(domains, concurrency);

		for (const batch of batches)
		{
			const batchPromises = batch.map(async (domainContent) =>
			{
				try
				{
					const description = await this.generateDescription(domainContent, providerName);
					results.set(domainContent.domain, description);
				}
				catch (error)
				{
					this.logger.error({
						domain: domainContent.domain,
						error: error instanceof Error ? error.message : 'Unknown error',
					}, 'Batch description generation failed');

					results.set(domainContent.domain, this.generateFallbackDescription(domainContent));
				}
			});

			await Promise.all(batchPromises);

			// Add delay between batches to respect rate limits
			if (batches.indexOf(batch) < batches.length - 1)
			{
				await this.delay(1000);
			}
		}

		return results;
	}

	/**
	 * Extract company information from website content
	 */
	async extractCompanyInfo(
		domainContent: DomainContent,
		providerName?: string,
	): Promise<GeneratedDescription['companyInfo']>
	{
		try
		{
			const provider = providerName || this.defaultProvider;
			const config = this.providers.get(provider);

			if (!config)
			{
				throw new Error(`AI provider '${provider}' not configured`);
			}

			const model = this.getModelInstance(config);
			const prompt = this.buildCompanyExtractionPrompt(domainContent);

			const result = await generateText({
				model,
				prompt,
				maxTokens: 500,
				temperature: 0.1, // Lower temperature for factual extraction
			});

			return this.parseCompanyInfo(result.text);
		}
		catch (error)
		{
			this.logger.error({
				domain: domainContent.domain,
				error: error instanceof Error ? error.message : 'Unknown error',
			}, 'Company info extraction failed');

			return this.extractBasicCompanyInfo(domainContent);
		}
	}

	/**
	 * Check if AI services are available
	 */
	async isAvailable(providerName?: string): Promise<boolean>
	{
		try
		{
			const provider = providerName || this.defaultProvider;
			const config = this.providers.get(provider);

			if (!config)
			{
				return false;
			}

			// Test with a simple prompt
			const model = this.getModelInstance(config);
			await generateText({
				model,
				prompt: 'Test prompt',
				maxTokens: 10,
			});

			return true;
		}
		catch (error)
		{
			this.logger.warn({
				provider: providerName || this.defaultProvider,
				error: error instanceof Error ? error.message : 'Unknown error',
			}, 'AI service availability check failed');

			return false;
		}
	}

	/**
	 * Build comprehensive analysis prompt for AI
	 */
	private buildAnalysisPrompt(domainContent: DomainContent): string
	{
		return `Analyze the following website content and generate a comprehensive business description:

Domain: ${domainContent.domain}
Title: ${domainContent.title || 'Not available'}
Meta Description: ${domainContent.description || 'Not available'}
Technologies: ${domainContent.technologies?.join(', ') || 'Not available'}
Industry: ${domainContent.industry || 'Not available'}
Language: ${domainContent.language || 'Not available'}

Homepage Content:
${domainContent.content?.substring(0, 2000) || 'Not available'}

About Page Content:
${domainContent.aboutContent?.substring(0, 1000) || 'Not available'}

Contact Information:
${domainContent.contactInfo || 'Not available'}

Please provide a JSON response with the following structure:
{
  "shortDescription": "A concise 1-2 sentence description of what this business/website does",
  "longDescription": "A detailed 3-4 sentence description including services, target audience, and value proposition",
  "companyInfo": {
    "name": "Company name if identifiable",
    "industry": "Primary industry/sector",
    "services": ["List of main services/products"],
    "location": "Location if mentioned",
    "founded": "Founding year if mentioned"
  },
  "keyFeatures": ["List of 3-5 key features or services"],
  "targetAudience": "Primary target audience description",
  "businessModel": "Business model if identifiable (B2B, B2C, marketplace, etc.)",
  "confidence": 0.85
}

Focus on factual information from the content. If information is not available, use null or empty arrays. Set confidence between 0.0-1.0 based on content quality and completeness.`;
	}

	/**
	 * Build company information extraction prompt
	 */
	private buildCompanyExtractionPrompt(domainContent: DomainContent): string
	{
		return `Extract specific company information from this website content:

Domain: ${domainContent.domain}
Content: ${domainContent.content?.substring(0, 1500) || ''}
About: ${domainContent.aboutContent?.substring(0, 800) || ''}
Contact: ${domainContent.contactInfo || ''}

Extract and return JSON with:
{
  "name": "Exact company name",
  "industry": "Specific industry",
  "services": ["Specific services offered"],
  "location": "Physical location/address",
  "founded": "Year founded"
}

Only include information explicitly mentioned in the content. Use null for missing information.`;
	}

	/**
	 * Parse AI response into structured format
	 */
	private parseAIResponse(response: string): Omit<GeneratedDescription, 'generatedAt'>
	{
		try
		{
			// Try to extract JSON from response
			const jsonMatch = response.match(/\{[\s\S]*\}/);
			if (jsonMatch)
			{
				const parsed = JSON.parse(jsonMatch[0]);
				return {
					shortDescription: parsed.shortDescription || '',
					longDescription: parsed.longDescription || '',
					companyInfo: parsed.companyInfo || {},
					keyFeatures: parsed.keyFeatures || [],
					targetAudience: parsed.targetAudience || '',
					businessModel: parsed.businessModel || undefined,
					confidence: parsed.confidence || 0.5,
				};
			}

			// Fallback: parse unstructured response
			return this.parseUnstructuredResponse(response);
		}
		catch (error)
		{
			this.logger.warn({
				error: error instanceof Error ? error.message : 'Unknown error',
				response: response.substring(0, 200),
			}, 'Failed to parse AI response');

			return this.parseUnstructuredResponse(response);
		}
	}

	/**
	 * Parse unstructured AI response
	 */
	private parseUnstructuredResponse(response: string): Omit<GeneratedDescription, 'generatedAt'>
	{
		const lines = response.split('\n').filter(line => line.trim());

		return {
			shortDescription: lines[0] || 'AI-generated description not available',
			longDescription: lines.slice(0, 3).join(' ') || 'Detailed description not available',
			companyInfo: {},
			keyFeatures: [],
			targetAudience: 'General audience',
			confidence: 0.3,
		};
	}

	/**
	 * Parse company information from AI response
	 */
	private parseCompanyInfo(response: string): GeneratedDescription['companyInfo']
	{
		try
		{
			const jsonMatch = response.match(/\{[\s\S]*\}/);
			if (jsonMatch)
			{
				return JSON.parse(jsonMatch[0]);
			}
		}
		catch (error)
		{
			this.logger.warn({ error }, 'Failed to parse company info');
		}

		return {};
	}

	/**
	 * Generate fallback description when AI fails
	 */
	private generateFallbackDescription(domainContent: DomainContent): GeneratedDescription
	{
		const domain = domainContent.domain;
		const title = domainContent.title || domain;

		return {
			shortDescription: `${title} is a website providing online services and information.`,
			longDescription: `${title} operates as a web-based platform offering various services to its users. The website serves as a digital presence for the organization, providing information and potentially interactive features for visitors.`,
			companyInfo: {
				name: title !== domain ? title : undefined,
				industry: domainContent.industry,
			},
			keyFeatures: domainContent.technologies || [],
			targetAudience: 'General internet users',
			confidence: 0.2,
			generatedAt: new Date(),
		};
	}

	/**
	 * Extract basic company info without AI
	 */
	private extractBasicCompanyInfo(domainContent: DomainContent): GeneratedDescription['companyInfo']
	{
		return {
			name: domainContent.title,
			industry: domainContent.industry,
			services: [],
			location: undefined,
			founded: undefined,
		};
	}

	/**
	 * Get AI model instance based on provider config
	 */
	private getModelInstance(config: AIProviderConfig)
	{
		switch (config.provider)
		{
			case 'openai':
				return openai(config.model, {
					apiKey: config.apiKey,
				});

			case 'anthropic':
				return anthropic(config.model, {
					apiKey: config.apiKey,
				});

			case 'google':
				return google(config.model, {
					apiKey: config.apiKey,
				});

			default:
				throw new Error(`Unsupported AI provider: ${config.provider}`);
		}
	}

	/**
	 * Select default provider based on availability
	 */
	private selectDefaultProvider(): string
	{
		const preferredOrder = ['openai', 'anthropic', 'google'];

		for (const provider of preferredOrder)
		{
			if (this.providers.has(provider))
			{
				return provider;
			}
		}

		throw new Error('No AI providers configured');
	}

	/**
	 * Create batches for concurrent processing
	 */
	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}

	/**
	 * Delay execution for rate limiting
	 */
	private delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export { DomainDescriptionGenerator };
export type { DomainContent, GeneratedDescription, AIProviderConfig };
