import type { Logger } from '@shared';
import axios from 'axios';
import {
	vi, describe, beforeEach, it, expect,
} from 'vitest';
import { ImageProcessor } from '../ImageProcessor';

// Mock axios
vi.mock('axios');
const mockedAxios = axios as vi.Mocked<typeof axios>;

// Mock logger
const mockLogger: Logger = {
	info: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
	debug: vi.fn(),
};

describe('ImageProcessor', () =>
{
	let imageProcessor: ImageProcessor;

	beforeEach(() =>
	{
		imageProcessor = new ImageProcessor(mockLogger);
		vi.clearAllMocks();
	});

	describe('processScreenshot', () =>
	{
		it('should process screenshot with WebP format', async () =>
		{
			const mockImageUrl = 'https://example.com/screenshot.png';

			mockedAxios.head.mockResolvedValueOnce({
				headers: {
					'content-length': '150000',
					'x-image-width': '1920',
					'x-image-height': '1080',
				},
			});

			const result = await imageProcessor.processScreenshot(mockImageUrl);

			expect(result.format).toBe('webp');
			expect(result.optimized).toBe(true);
			expect(result.originalUrl).toBe(mockImageUrl);
			expect(result.size).toBe(150000);
			expect(result.width).toBe(1920);
			expect(result.height).toBe(1080);
		});

		it('should handle processing errors gracefully', async () =>
		{
			const mockImageUrl = 'https://example.com/invalid.png';

			mockedAxios.head.mockRejectedValueOnce(new Error('Network error'));

			const result = await imageProcessor.processScreenshot(mockImageUrl);

			expect(result.optimized).toBe(false);
			expect(result.url).toBe(mockImageUrl);
			expect(result.format).toBe('unknown');
		});
	});

	describe('processFavicon', () =>
	{
		it('should process favicon with PNG and WebP formats', async () =>
		{
			const mockImageUrl = 'https://example.com/favicon.ico';

			mockedAxios.head
				.mockResolvedValueOnce({
					headers: {
						'content-length': '5000',
						'x-image-width': '32',
						'x-image-height': '32',
					},
				})
				.mockResolvedValueOnce({
					headers: {
						'content-length': '3000',
						'x-image-width': '32',
						'x-image-height': '32',
					},
				});

			const results = await imageProcessor.processFavicon(mockImageUrl);

			expect(results).toHaveLength(2);
			expect(results[0].format).toBe('png');
			expect(results[1].format).toBe('webp');
			expect(results[0].optimized).toBe(true);
			expect(results[1].optimized).toBe(true);
		});

		it('should return single format when fallback is disabled', async () =>
		{
			const mockImageUrl = 'https://example.com/favicon.ico';

			mockedAxios.head.mockResolvedValueOnce({
				headers: {
					'content-length': '5000',
				},
			});

			const results = await imageProcessor.processFavicon(mockImageUrl, { fallback: false });

			expect(results).toHaveLength(1);
			expect(results[0].format).toBe('png');
		});
	});

	describe('processImage', () =>
	{
		it('should route to correct processor based on image type', async () =>
		{
			const mockImageUrl = 'https://example.com/image.jpg';

			mockedAxios.head.mockResolvedValue({
				headers: {
					'content-length': '50000',
				},
			});

			// Test screenshot routing
			const screenshotResult = await imageProcessor.processImage(mockImageUrl, 'screenshot');

			expect(Array.isArray(screenshotResult)).toBe(false);
			expect((screenshotResult as any).format).toBe('webp');

			// Test favicon routing
			const faviconResult = await imageProcessor.processImage(mockImageUrl, 'favicon');

			expect(Array.isArray(faviconResult)).toBe(true);

			// Test content routing
			const contentResult = await imageProcessor.processImage(mockImageUrl, 'content');

			expect(Array.isArray(contentResult)).toBe(false);
			expect((contentResult as any).format).toBe('webp');
		});

		it('should throw error for unsupported image type', async () =>
		{
			const mockImageUrl = 'https://example.com/image.jpg';

			await expect(
				imageProcessor.processImage(mockImageUrl, 'unsupported' as any),
			).rejects.toThrow('Unsupported image type: unsupported');
		});
	});

	describe('validateImageUrl', () =>
	{
		it('should validate supported image formats', async () =>
		{
			mockedAxios.head.mockResolvedValueOnce({
				headers: {
					'content-type': 'image/jpeg',
				},
			});

			const isValid = await imageProcessor.validateImageUrl('https://example.com/image.jpg');

			expect(isValid).toBe(true);
		});

		it('should reject unsupported formats', async () =>
		{
			mockedAxios.head.mockResolvedValueOnce({
				headers: {
					'content-type': 'text/html',
				},
			});

			const isValid = await imageProcessor.validateImageUrl('https://example.com/page.html');

			expect(isValid).toBe(false);
		});

		it('should handle network errors', async () =>
		{
			mockedAxios.head.mockRejectedValueOnce(new Error('Network error'));

			const isValid = await imageProcessor.validateImageUrl('https://example.com/image.jpg');

			expect(isValid).toBe(false);
		});
	});

	describe('batchProcessImages', () =>
	{
		it('should process multiple images concurrently', async () =>
		{
			const images = [
				{ url: 'https://example.com/screenshot.png', type: 'screenshot' as const },
				{ url: 'https://example.com/favicon.ico', type: 'favicon' as const },
				{ url: 'https://example.com/content.jpg', type: 'content' as const },
			];

			mockedAxios.head.mockResolvedValue({
				headers: {
					'content-length': '10000',
				},
			});

			const results = await imageProcessor.batchProcessImages(images);

			expect(results).toHaveLength(3);
			expect(Array.isArray(results[1])).toBe(true); // favicon returns array
			expect(Array.isArray(results[0])).toBe(false); // screenshot returns single
			expect(Array.isArray(results[2])).toBe(false); // content returns single
		});

		it('should handle individual processing failures', async () =>
		{
			const images = [
				{ url: 'https://example.com/valid.png', type: 'screenshot' as const },
				{ url: 'https://example.com/invalid.png', type: 'screenshot' as const },
			];

			mockedAxios.head
				.mockResolvedValueOnce({
					headers: { 'content-length': '10000' },
				})
				.mockRejectedValueOnce(new Error('Network error'));

			const results = await imageProcessor.batchProcessImages(images);

			expect(results).toHaveLength(2);
			expect((results[0] as any).optimized).toBe(true);
			expect((results[1] as any).optimized).toBe(false);
		});
	});
});
