import {
	describe, test, it, expect, beforeEach, afterEach, vi,
} from 'vitest';
import axios from 'axios';
import PerformanceAuditor, { PerformanceAuditResult } from '../PerformanceAuditor';

// Mock axios
vi.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock shared dependencies
vi.mock('@shared', () => ({
	Logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			debug: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
		})),
	},
	Config: {
		get: vi.fn((key: string) =>
		{
			if (key === 'IMAGE_PROXY_URL') return 'http://image-proxy:8080';
			return undefined;
		}),
	},
}));

describe('PerformanceAuditor', () =>
{
	let performanceAuditor: PerformanceAuditor;

	beforeEach(() =>
	{
		performanceAuditor = new PerformanceAuditor();
		vi.clearAllMocks();
	});

	describe('auditPerformance', () =>
	{
		it('should successfully audit performance for a domain', async () =>
		{
			// Mock successful Core Web Vitals response
			const mockMetricsResponse = {
				status: 200,
				data: {
					loadTime: 2500,
					firstContentfulPaint: 1200,
					largestContentfulPaint: 2000,
					cumulativeLayoutShift: 0.05,
					firstInputDelay: 50,
					speedIndex: 1800,
					resourceCount: 25,
				},
			};

			// Mock successful resource analysis response
			const mockResourceResponse = {
				status: 200,
				data: {
					totalRequests: 25,
					totalSize: 1024000,
					resourceTypes: {
						img: { count: 10, size: 512000 },
						script: { count: 8, size: 256000 },
						css: { count: 3, size: 128000 },
						other: { count: 4, size: 128000 },
					},
					largestResources: [
						{
							url: 'https://example.com/large-image.jpg', type: 'img', size: 200000, loadTime: 500,
						},
						{
							url: 'https://example.com/app.js', type: 'script', size: 150000, loadTime: 300,
						},
					],
				},
			};

			// Mock successful network analysis response
			const mockNetworkResponse = {
				status: 200,
				data: {
					connectionType: '4g',
					rtt: 50,
					downlink: 10,
					effectiveType: '4g',
				},
			};

			// Setup axios mocks for the three API calls
			mockedAxios.post
				.mockResolvedValueOnce(mockMetricsResponse) // Core Web Vitals
				.mockResolvedValueOnce(mockResourceResponse) // Resource analysis
				.mockResolvedValueOnce(mockNetworkResponse); // Network analysis

			const result = await performanceAuditor.auditPerformance('example.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('example.com');
			expect(result.error).toBeUndefined();

			// Check Core Web Vitals
			expect(result.metrics.loadTime).toBe(2500);
			expect(result.metrics.firstContentfulPaint).toBe(1200);
			expect(result.metrics.largestContentfulPaint).toBe(2000);
			expect(result.metrics.cumulativeLayoutShift).toBe(0.05);
			expect(result.metrics.firstInputDelay).toBe(50);
			expect(result.metrics.speedIndex).toBe(1800);
			expect(result.metrics.score).toBeGreaterThan(0);

			// Check resource analysis
			expect(result.resourceAnalysis.totalRequests).toBe(25);
			expect(result.resourceAnalysis.totalSize).toBe(1024000);
			expect(result.resourceAnalysis.resourceTypes).toHaveProperty('img');
			expect(result.resourceAnalysis.largestResources).toHaveLength(2);

			// Check network analysis
			expect(result.networkAnalysis.connectionType).toBe('4g');
			expect(result.networkAnalysis.rtt).toBe(50);

			// Check recommendations
			expect(Array.isArray(result.recommendations)).toBe(true);

			// Check audit details
			expect(result.auditDetails.auditTime).toBeGreaterThan(0);
			expect(result.auditDetails.viewport).toEqual({ width: 1920, height: 1080 });

			// Verify axios calls
			expect(mockedAxios.post).toHaveBeenCalledTimes(3);
			expect(mockedAxios.post).toHaveBeenCalledWith(
				'http://browserless:3000/function',
				expect.objectContaining({
					url: 'https://example.com',
				}),
				expect.any(Object),
			);
		});

		it('should handle browserless service errors gracefully', async () =>
		{
			// Mock browserless service error
			mockedAxios.post.mockRejectedValue(new Error('Browserless service unavailable'));

			const result = await performanceAuditor.auditPerformance('example.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('example.com');
			expect(result.error).toBeDefined();
			expect(result.metrics.score).toBeGreaterThanOrEqual(0);
			expect(result.auditDetails.auditTime).toBeGreaterThanOrEqual(0);
		});

		it('should handle partial data collection failures', async () =>
		{
			// Mock successful Core Web Vitals but failed resource analysis
			const mockMetricsResponse = {
				status: 200,
				data: {
					loadTime: 2500,
					firstContentfulPaint: 1200,
					largestContentfulPaint: 2000,
					cumulativeLayoutShift: 0.05,
					firstInputDelay: 50,
					speedIndex: 1800,
				},
			};

			mockedAxios.post
				.mockResolvedValueOnce(mockMetricsResponse) // Core Web Vitals success
				.mockRejectedValueOnce(new Error('Resource analysis failed')) // Resource analysis failure
				.mockRejectedValueOnce(new Error('Network analysis failed')); // Network analysis failure

			const result = await performanceAuditor.auditPerformance('example.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('example.com');

			// Should have some error indication when parts fail
			if (result.error)
			{
				expect(result.error).toContain('failed');
			}

			// Core Web Vitals should still be available
			expect(result.metrics.loadTime).toBe(2500);
			expect(result.metrics.firstContentfulPaint).toBe(1200);
		});
	});

	describe('auditMobilePerformance', () =>
	{
		it('should audit mobile performance with mobile viewport', async () =>
		{
			// Mock successful mobile performance audit
			const mockResponse = {
				status: 200,
				data: {
					loadTime: 3500,
					firstContentfulPaint: 1800,
					largestContentfulPaint: 2800,
					cumulativeLayoutShift: 0.08,
					firstInputDelay: 80,
					speedIndex: 2200,
				},
			};

			mockedAxios.post.mockResolvedValue(mockResponse);

			const result = await performanceAuditor.auditMobilePerformance('example.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('example.com');
			expect(result.auditDetails.viewport).toEqual({ width: 375, height: 667 });
			expect(result.auditDetails.userAgent).toContain('iPhone');
			expect(result.metrics.loadTime).toBe(3500);
		});
	});

	describe('batchAuditPerformance', () =>
	{
		it('should audit multiple domains with controlled concurrency', async () =>
		{
			const domains = ['example1.com', 'example2.com', 'example3.com'];

			// Mock successful responses for all domains
			const mockResponse = {
				status: 200,
				data: {
					loadTime: 2000,
					firstContentfulPaint: 1000,
					largestContentfulPaint: 1500,
					cumulativeLayoutShift: 0.03,
					firstInputDelay: 30,
					speedIndex: 1200,
				},
			};

			mockedAxios.post.mockResolvedValue(mockResponse);

			const results = await performanceAuditor.batchAuditPerformance(domains, {
				concurrency: 2,
			});

			expect(results).toHaveLength(3);
			expect(results.every(r => r.domain && !r.error)).toBe(true);

			// Should have made multiple calls (3 domains × 3 API calls each)
			expect(mockedAxios.post).toHaveBeenCalledTimes(9);
		});

		it('should handle individual domain failures in batch processing', async () =>
		{
			const domains = ['good-domain.com', 'bad-domain.com'];

			mockedAxios.post
				.mockResolvedValueOnce({ status: 200, data: { loadTime: 2000 } }) // good-domain success
				.mockResolvedValueOnce({ status: 200, data: { totalRequests: 20 } }) // good-domain success
				.mockResolvedValueOnce({ status: 200, data: { connectionType: '4g' } }) // good-domain success
				.mockRejectedValueOnce(new Error('Domain not accessible')); // bad-domain failure

			const results = await performanceAuditor.batchAuditPerformance(domains);

			expect(results).toHaveLength(2);
			expect(results[0].error).toBeUndefined();
			expect(results[1].error).toContain('Domain not accessible');
		});
	});

	describe('getBrowserlessStatus', () =>
	{
		it('should return browserless service status', async () =>
		{
			const mockPressureResponse = {
				status: 200,
				data: { pressure: { cpu: 0.3, memory: 0.5 } },
			};

			const mockStatsResponse = {
				status: 200,
				data: { stats: { sessions: 2, maxSessions: 10 } },
			};

			mockedAxios.get
				.mockResolvedValueOnce(mockPressureResponse)
				.mockResolvedValueOnce(mockStatsResponse);

			const status = await performanceAuditor.getBrowserlessStatus();

			expect(status.available).toBe(true);
			expect(status.pressure).toBeDefined();
			expect(status.stats).toBeDefined();
		});

		it('should handle browserless service unavailability', async () =>
		{
			mockedAxios.get.mockRejectedValue(new Error('Service unavailable'));

			const status = await performanceAuditor.getBrowserlessStatus();

			expect(status.available).toBe(false);
			expect(status.pressure).toBeUndefined();
			expect(status.stats).toBeUndefined();
		});
	});

	describe('validateCapability', () =>
	{
		it('should validate performance auditing capabilities', async () =>
		{
			// Mock browserless status check
			mockedAxios.get.mockResolvedValue({
				status: 200,
				data: { available: true },
			});

			// Mock capability test
			mockedAxios.post.mockResolvedValue({
				status: 200,
				data: {
					navigationAPI: true,
					paintAPI: true,
					resourceAPI: true,
					memoryAPI: true,
				},
			});

			const capability = await performanceAuditor.validateCapability();

			expect(capability.browserlessAvailable).toBe(true);
			expect(capability.performanceAPISupported).toBe(true);
		});

		it('should handle capability validation failures', async () =>
		{
			mockedAxios.get.mockRejectedValue(new Error('Service check failed'));
			mockedAxios.post.mockRejectedValue(new Error('API test failed'));

			const capability = await performanceAuditor.validateCapability();

			expect(capability.browserlessAvailable).toBe(false);
			expect(capability.performanceAPISupported).toBe(false);
			expect(capability.error).toBeDefined();
		});
	});

	describe('performance scoring', () =>
	{
		it('should calculate performance scores correctly', async () =>
		{
			// Test with good performance metrics
			const goodMetricsResponse = {
				status: 200,
				data: {
					loadTime: 1500,
					firstContentfulPaint: 800,
					largestContentfulPaint: 1200,
					cumulativeLayoutShift: 0.05,
					firstInputDelay: 50,
					speedIndex: 1000,
				},
			};

			mockedAxios.post.mockResolvedValue(goodMetricsResponse);

			const result = await performanceAuditor.auditPerformance('fast-site.com');

			expect(result.metrics.score).toBeGreaterThan(0.8); // Should get high score for good metrics
		});

		it('should generate appropriate recommendations', async () =>
		{
			// Test with poor performance metrics
			const poorMetricsResponse = {
				status: 200,
				data: {
					loadTime: 8000,
					firstContentfulPaint: 4000,
					largestContentfulPaint: 6000,
					cumulativeLayoutShift: 0.5,
					firstInputDelay: 500,
					speedIndex: 5000,
				},
			};

			const largeResourceResponse = {
				status: 200,
				data: {
					totalRequests: 150,
					totalSize: 5 * 1024 * 1024, // 5MB
					resourceTypes: {
						img: { count: 50, size: 3 * 1024 * 1024 },
						script: { count: 30, size: 1024 * 1024 },
					},
					largestResources: [],
				},
			};

			mockedAxios.post
				.mockResolvedValueOnce(poorMetricsResponse)
				.mockResolvedValueOnce(largeResourceResponse)
				.mockResolvedValueOnce({ status: 200, data: {} });

			const result = await performanceAuditor.auditPerformance('slow-site.com');

			expect(result.recommendations.length).toBeGreaterThan(0);
			expect(result.recommendations.some(r => r.includes('Largest Contentful Paint'))).toBe(true);
			expect(result.recommendations.some(r => r.includes('Layout Shift'))).toBe(true);
			expect(result.recommendations.some(r => r.includes('page size'))).toBe(true);
		});
	});
});
