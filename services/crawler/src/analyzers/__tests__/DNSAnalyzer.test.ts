import {
	describe, test, it, expect, beforeEach, afterEach, vi,
} from 'vitest';
import DNSAnalyzer from '../DNSAnalyzer';

// Mock the Logger
vi.mock('@shared', () => ({
	Logger: {
		getLogger: () => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
}));

describe('DNSAnalyzer', () =>
{
	let analyzer: DNSAnalyzer;

	beforeEach(() =>
	{
		analyzer = new DNSAnalyzer();
	});

	describe('analyzeDNSRecords', () =>
	{
		it('should analyze DNS records for a valid domain', async () =>
		{
			const result = await analyzer.analyzeDNSRecords('google.com');

			expect(result).toHaveProperty('domain', 'google.com');
			expect(result).toHaveProperty('records');
			expect(result).toHaveProperty('ipAddresses');
			expect(result).toHaveProperty('nameServers');
			expect(result).toHaveProperty('mailServers');
			expect(result).toHaveProperty('lastAnalyzed');
		});

		it('should detect IPv4 and IPv6 addresses', async () =>
		{
			const result = await analyzer.analyzeDNSRecords('google.com');

			expect(result.ipAddresses).toHaveProperty('ipv4');
			expect(result.ipAddresses).toHaveProperty('ipv6');
			expect(Array.isArray(result.ipAddresses.ipv4)).toBe(true);
			expect(Array.isArray(result.ipAddresses.ipv6)).toBe(true);
		});

		it('should detect Cloudflare usage', async () =>
		{
			const result = await analyzer.analyzeDNSRecords('cloudflare.com');

			expect(result).toHaveProperty('hasCloudflare');
			expect(typeof result.hasCloudflare).toBe('boolean');
		});

		it('should handle domains with no DNS records', async () =>
		{
			const result = await analyzer.analyzeDNSRecords('nonexistent-domain-test.com');

			expect(result.domain).toBe('nonexistent-domain-test.com');
			expect(result.errors.length).toBeGreaterThan(0);
		});
	});

	describe('utility methods', () =>
	{
		it('should check if domain supports IPv6', () =>
		{
			const mockResult = {
				domain: 'test.com',
				records: {
					A: [], AAAA: ['2001:db8::1'], MX: [], CNAME: [], TXT: [], NS: [], SOA: null,
				},
				ipAddresses: { ipv4: [], ipv6: ['2001:db8::1'] },
				nameServers: [],
				mailServers: [],
				txtRecords: [],
				hasCloudflare: false,
				hasCDN: false,
				dnsProvider: null,
				analysisTime: 100,
				errors: [],
				lastAnalyzed: new Date().toISOString(),
			};

			const supportsIPv6 = analyzer.supportsIPv6(mockResult);

			expect(supportsIPv6).toBe(true);
		});

		it('should check if domain has email configuration', () =>
		{
			const mockResult = {
				domain: 'test.com',
				records: {
					A: [], AAAA: [], MX: [{ priority: 10, exchange: 'mail.test.com' }], CNAME: [], TXT: [], NS: [], SOA: null,
				},
				ipAddresses: { ipv4: [], ipv6: [] },
				nameServers: [],
				mailServers: ['mail.test.com'],
				txtRecords: [],
				hasCloudflare: false,
				hasCDN: false,
				dnsProvider: null,
				analysisTime: 100,
				errors: [],
				lastAnalyzed: new Date().toISOString(),
			};

			const hasEmail = analyzer.hasEmailConfiguration(mockResult);

			expect(hasEmail).toBe(true);
		});
	});
});
