import type { Logger, DomainContent, AIProviderConfig } from '@shared';
import { generateText } from 'ai';
import {
	vi, describe, beforeEach, it, expect,
} from 'vitest';
import { DomainDescriptionGenerator } from '../DomainDescriptionGenerator';

// Mock AI SDK
vi.mock('ai', () => ({
	generateText: vi.fn(),
}));

vi.mock('@ai-sdk/openai', () => ({
	openai: vi.fn().mockReturnValue('openai-model'),
}));

vi.mock('@ai-sdk/anthropic', () => ({
	anthropic: vi.fn().mockReturnValue('anthropic-model'),
}));

vi.mock('@ai-sdk/google', () => ({
	google: vi.fn().mockReturnValue('google-model'),
}));

const mockedGenerateText = generateText as vi.MockedFunction<typeof generateText>;

// Mock logger
const mockLogger: Logger = {
	info: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
	debug: vi.fn(),
};

describe('DomainDescriptionGenerator', () =>
{
	let generator: DomainDescriptionGenerator;
	let mockProviders: AIProviderConfig[];

	beforeEach(() =>
	{
		mockProviders = [
			{
				provider: 'openai',
				model: 'gpt-3.5-turbo',
				apiKey: 'test-key',
				maxTokens: 1000,
				temperature: 0.3,
			},
			{
				provider: 'anthropic',
				model: 'claude-3-haiku',
				apiKey: 'test-key-2',
			},
		];

		generator = new DomainDescriptionGenerator(mockLogger, mockProviders);
		vi.clearAllMocks();
	});

	describe('generateDescription', () =>
	{
		const mockDomainContent: DomainContent = {
			domain: 'example.com',
			title: 'Example Company',
			description: 'A sample company website',
			content: 'We provide excellent services to our customers...',
			aboutContent: 'Founded in 2020, we are a leading provider...',
			contactInfo: 'Contact <NAME_EMAIL>',
			technologies: ['React', 'Node.js'],
			industry: 'Technology',
			language: 'en',
		};

		it('should generate description successfully with structured response', async () =>
		{
			const mockAIResponse = {
				text: JSON.stringify({
					shortDescription: 'Example Company provides technology services',
					longDescription: 'Example Company is a leading technology provider founded in 2020...',
					companyInfo: {
						name: 'Example Company',
						industry: 'Technology',
						services: ['Web Development', 'Consulting'],
						founded: '2020',
					},
					keyFeatures: ['React Development', 'Node.js Services'],
					targetAudience: 'Businesses seeking technology solutions',
					businessModel: 'B2B',
					confidence: 0.9,
				}),
			};

			mockedGenerateText.mockResolvedValueOnce(mockAIResponse);

			const result = await generator.generateDescription(mockDomainContent);

			expect(result.shortDescription).toBe('Example Company provides technology services');
			expect(result.companyInfo.name).toBe('Example Company');
			expect(result.confidence).toBe(0.9);
			expect(result.generatedAt).toBeInstanceOf(Date);
		});

		it('should handle unstructured AI response', async () =>
		{
			const mockAIResponse = {
				text: 'Example Company is a technology provider.\nThey offer web development services.\nFounded in 2020.',
			};

			mockedGenerateText.mockResolvedValueOnce(mockAIResponse);

			const result = await generator.generateDescription(mockDomainContent);

			expect(result.shortDescription).toBe('Example Company is a technology provider.');
			expect(result.confidence).toBe(0.3);
		});

		it('should return fallback description on AI failure', async () =>
		{
			mockedGenerateText.mockRejectedValueOnce(new Error('API Error'));

			const result = await generator.generateDescription(mockDomainContent);

			expect(result.shortDescription).toContain('Example Company');
			expect(result.confidence).toBe(0.2);
			expect(result.companyInfo.name).toBe('Example Company');
		});

		it('should use specified provider', async () =>
		{
			const mockAIResponse = {
				text: JSON.stringify({
					shortDescription: 'Test description',
					confidence: 0.8,
				}),
			};

			mockedGenerateText.mockResolvedValueOnce(mockAIResponse);

			await generator.generateDescription(mockDomainContent, 'anthropic');

			expect(mockedGenerateText).toHaveBeenCalledWith(
				expect.objectContaining({
					model: 'anthropic-model',
				}),
			);
		});

		it('should throw error for unconfigured provider', async () =>
		{
			await expect(
				generator.generateDescription(mockDomainContent, 'unconfigured'),
			).rejects.toThrow("AI provider 'unconfigured' not configured");
		});
	});

	describe('extractCompanyInfo', () =>
	{
		it('should extract company information', async () =>
		{
			const mockDomainContent: DomainContent = {
				domain: 'techcorp.com',
				content: 'TechCorp was founded in 2015 and provides software solutions...',
				aboutContent: 'We are located in San Francisco and specialize in AI development...',
			};

			const mockAIResponse = {
				text: JSON.stringify({
					name: 'TechCorp',
					industry: 'Software Development',
					services: ['AI Development', 'Software Solutions'],
					location: 'San Francisco',
					founded: '2015',
				}),
			};

			mockedGenerateText.mockResolvedValueOnce(mockAIResponse);

			const result = await generator.extractCompanyInfo(mockDomainContent);

			expect(result.name).toBe('TechCorp');
			expect(result.founded).toBe('2015');
			expect(result.location).toBe('San Francisco');
		});

		it('should handle extraction failure gracefully', async () =>
		{
			const mockDomainContent: DomainContent = {
				domain: 'example.com',
				title: 'Example Site',
			};

			mockedGenerateText.mockRejectedValueOnce(new Error('API Error'));

			const result = await generator.extractCompanyInfo(mockDomainContent);

			expect(result.name).toBe('Example Site');
		});
	});

	describe('batchGenerateDescriptions', () =>
	{
		it('should process multiple domains', async () =>
		{
			const domains: DomainContent[] = [
				{ domain: 'site1.com', title: 'Site 1' },
				{ domain: 'site2.com', title: 'Site 2' },
			];

			mockedGenerateText
				.mockResolvedValueOnce({
					text: JSON.stringify({
						shortDescription: 'Site 1 description',
						confidence: 0.8,
					}),
				})
				.mockResolvedValueOnce({
					text: JSON.stringify({
						shortDescription: 'Site 2 description',
						confidence: 0.7,
					}),
				});

			const results = await generator.batchGenerateDescriptions(domains);

			expect(results.size).toBe(2);
			expect(results.get('site1.com')?.shortDescription).toBe('Site 1 description');
			expect(results.get('site2.com')?.shortDescription).toBe('Site 2 description');
		});

		it('should handle individual failures in batch', async () =>
		{
			const domains: DomainContent[] = [
				{ domain: 'good.com', title: 'Good Site' },
				{ domain: 'bad.com', title: 'Bad Site' },
			];

			mockedGenerateText
				.mockResolvedValueOnce({
					text: JSON.stringify({
						shortDescription: 'Good site description',
						confidence: 0.8,
					}),
				})
				.mockRejectedValueOnce(new Error('API Error'));

			const results = await generator.batchGenerateDescriptions(domains);

			expect(results.size).toBe(2);
			expect(results.get('good.com')?.shortDescription).toBe('Good site description');
			expect(results.get('bad.com')?.confidence).toBe(0.2); // Fallback
		});
	});

	describe('isAvailable', () =>
	{
		it('should return true when AI service is available', async () =>
		{
			mockedGenerateText.mockResolvedValueOnce({ text: 'test' });

			const isAvailable = await generator.isAvailable();

			expect(isAvailable).toBe(true);
		});

		it('should return false when AI service is unavailable', async () =>
		{
			mockedGenerateText.mockRejectedValueOnce(new Error('Service unavailable'));

			const isAvailable = await generator.isAvailable();

			expect(isAvailable).toBe(false);
		});
	});

	describe('constructor', () =>
	{
		it('should throw error when no providers configured', () =>
		{
			expect(() =>
			{
				new DomainDescriptionGenerator(mockLogger, []);
			}).toThrow('No AI providers configured');
		});

		it('should select default provider correctly', () =>
		{
			const providers: AIProviderConfig[] = [
				{ provider: 'google', model: 'gemini-pro', apiKey: 'key1' },
				{ provider: 'openai', model: 'gpt-3.5-turbo', apiKey: 'key2' },
			];

			const gen = new DomainDescriptionGenerator(mockLogger, providers);

			// OpenAI should be preferred over Google
			expect((gen as any).defaultProvider).toBe('openai');
		});
	});
});
