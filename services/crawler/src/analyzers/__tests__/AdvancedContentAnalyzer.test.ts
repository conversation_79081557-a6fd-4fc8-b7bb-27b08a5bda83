import {
	describe, it, expect, beforeEach, vi,
} from 'vitest';
import AdvancedContentAnalyzer, { PageContent, ContentQualityMetrics } from '../AdvancedContentAnalyzer';

// Mock the Logger
vi.mock('@shared', () => ({
	Logger: {
		getLogger: () => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
}));

// Mock HTTP requests
vi.mock('https', () => ({
	request: vi.fn(),
}));

vi.mock('http', () => ({
	request: vi.fn(),
}));

describe('AdvancedContentAnalyzer', () =>
{
	let analyzer: AdvancedContentAnalyzer;

	beforeEach(() =>
	{
		analyzer = new AdvancedContentAnalyzer();
		vi.clearAllMocks();
	});

	describe('analyzeContent', () =>
	{
		it('should return proper structure for analysis result', async () =>
		{
			// Mock successful HTTP response
			const mockResponse = {
				statusCode: 200,
				headers: { 'content-type': 'text/html' },
				body: `
					<!DOCTYPE html>
					<html lang="en">
					<head>
						<title>Test Page</title>
						<meta name="description" content="Test description">
					</head>
					<body>
						<h1>Main Heading</h1>
						<p>This is test content with multiple words to analyze readability and content quality.</p>
					</body>
					</html>
				`,
			};

			// Mock the private makeRequest method
			const makeRequestSpy = vi.spyOn(analyzer as any, 'makeRequest')
				.mockResolvedValue(mockResponse);

			const result = await analyzer.analyzeContent('example.com');

			expect(result).toHaveProperty('domain', 'example.com');
			expect(result).toHaveProperty('pages');
			expect(result).toHaveProperty('contentQuality');
			expect(result).toHaveProperty('languageDetection');
			expect(result).toHaveProperty('contentMetrics');
			expect(result).toHaveProperty('technicalSEO');
			expect(result).toHaveProperty('lastAnalyzed');

			makeRequestSpy.mockRestore();
		});

		it('should handle analysis errors gracefully', async () =>
		{
			// Mock failed HTTP response
			const makeRequestSpy = vi.spyOn(analyzer as any, 'makeRequest')
				.mockRejectedValue(new Error('Network error'));

			const result = await analyzer.analyzeContent('nonexistent.com');

			expect(result.domain).toBe('nonexistent.com');
			expect(result.error).toBeDefined();
			expect(result.error).toBeTruthy();

			makeRequestSpy.mockRestore();
		});
	});

	describe('content quality calculation', () =>
	{
		it('should calculate content quality metrics correctly', () =>
		{
			const mockPages: PageContent[] = [
				{
					url: 'https://example.com',
					title: 'Test Page',
					description: 'Test description',
					content: 'This is a test content with sufficient length to analyze quality metrics properly.',
					headings: ['Main Heading', 'Sub Heading'],
					metaKeywords: ['test', 'content'],
					wordCount: 15,
					readabilityScore: 65,
					language: 'en',
					contentHash: 'abc123',
				},
				{
					url: 'https://example.com/about',
					title: 'About Page',
					description: 'About us description',
					content: 'About us content with different information and varied structure for analysis.',
					headings: ['About Us', 'Our Mission'],
					metaKeywords: ['about', 'company'],
					wordCount: 12,
					readabilityScore: 70,
					language: 'en',
					contentHash: 'def456',
				},
			];

			const quality = (analyzer as any).calculateContentQuality(mockPages);

			expect(quality).toHaveProperty('overallScore');
			expect(quality).toHaveProperty('contentLength');
			expect(quality).toHaveProperty('readabilityScore');
			expect(quality).toHaveProperty('mediaRichness');
			expect(quality).toHaveProperty('structuralQuality');
			expect(quality).toHaveProperty('uniqueness');
			expect(quality).toHaveProperty('freshness');

			expect(quality.overallScore).toBeGreaterThan(0);
			expect(quality.overallScore).toBeLessThanOrEqual(1);
		});

		it('should score content length appropriately', () =>
		{
			// Test different content lengths
			const shortContent = (analyzer as any).scoreContentLength(50);
			const optimalContent = (analyzer as any).scoreContentLength(800);
			const longContent = (analyzer as any).scoreContentLength(5000);

			expect(shortContent).toBeLessThan(optimalContent);
			expect(optimalContent).toBeGreaterThan(0.7);
			expect(longContent).toBeLessThan(optimalContent);
		});

		it('should score readability appropriately', () =>
		{
			// Test different readability scores
			const difficultText = (analyzer as any).scoreReadability(30);
			const optimalText = (analyzer as any).scoreReadability(65);
			const easyText = (analyzer as any).scoreReadability(90);

			expect(optimalText).toBeGreaterThan(difficultText);
			expect(optimalText).toBeGreaterThan(easyText);
			expect(optimalText).toBe(1.0);
		});
	});

	describe('language detection', () =>
	{
		it('should detect primary language correctly', () =>
		{
			const mockPages: PageContent[] = [
				{
					url: 'https://example.com',
					title: 'Test Page',
					description: 'Test description',
					content: 'English content',
					headings: [],
					metaKeywords: [],
					wordCount: 2,
					readabilityScore: 65,
					language: 'en',
					contentHash: 'abc123',
				},
				{
					url: 'https://example.com/about',
					title: 'About Page',
					description: 'About description',
					content: 'More English content',
					headings: [],
					metaKeywords: [],
					wordCount: 3,
					readabilityScore: 70,
					language: 'en',
					contentHash: 'def456',
				},
			];

			const languageDetection = (analyzer as any).detectLanguage(mockPages);

			expect(languageDetection).toHaveProperty('primary', 'en');
			expect(languageDetection).toHaveProperty('confidence', 1);
			expect(languageDetection).toHaveProperty('alternatives');
			expect(Array.isArray(languageDetection.alternatives)).toBe(true);
		});

		it('should handle mixed language content', () =>
		{
			const mockPages: PageContent[] = [
				{
					url: 'https://example.com',
					title: 'Test Page',
					description: 'Test description',
					content: 'English content',
					headings: [],
					metaKeywords: [],
					wordCount: 2,
					readabilityScore: 65,
					language: 'en',
					contentHash: 'abc123',
				},
				{
					url: 'https://example.com/es',
					title: 'Página en Español',
					description: 'Descripción en español',
					content: 'Contenido en español',
					headings: [],
					metaKeywords: [],
					wordCount: 3,
					readabilityScore: 70,
					language: 'es',
					contentHash: 'def456',
				},
			];

			const languageDetection = (analyzer as any).detectLanguage(mockPages);

			expect(languageDetection.primary).toBeDefined();
			expect(languageDetection.confidence).toBeGreaterThan(0);
			expect(languageDetection.alternatives.length).toBeGreaterThan(0);
		});
	});

	describe('readability calculation', () =>
	{
		it('should calculate Flesch Reading Ease score', () =>
		{
			const simpleText = 'This is simple text. It has short sentences. Easy to read.';
			const complexText = 'This is a significantly more complex sentence with multiple subordinate clauses, extensive vocabulary, and intricate grammatical structures that substantially increase the difficulty of comprehension for average readers.';

			const simpleScore = (analyzer as any).calculateReadabilityScore(simpleText);
			const complexScore = (analyzer as any).calculateReadabilityScore(complexText);

			expect(simpleScore).toBeGreaterThan(complexScore);
			expect(simpleScore).toBeGreaterThan(0);
			expect(simpleScore).toBeLessThanOrEqual(100);
			expect(complexScore).toBeGreaterThanOrEqual(0);
			expect(complexScore).toBeLessThanOrEqual(100);
		});

		it('should handle empty text', () =>
		{
			const score = (analyzer as any).calculateReadabilityScore('');

			expect(score).toBe(0);
		});
	});

	describe('syllable counting', () =>
	{
		it('should count syllables approximately correctly', () =>
		{
			const syllableCount1 = (analyzer as any).countSyllables('hello world');
			const syllableCount2 = (analyzer as any).countSyllables('beautiful');
			const syllableCount3 = (analyzer as any).countSyllables('a');

			expect(syllableCount1).toBeGreaterThan(0);
			expect(syllableCount2).toBeGreaterThan(syllableCount3);
			expect(syllableCount3).toBe(1);
		});
	});

	describe('content metrics calculation', () =>
	{
		it('should calculate comprehensive content metrics', () =>
		{
			const mockPages: PageContent[] = [
				{
					url: 'https://example.com',
					title: 'Test Page',
					description: 'Test description',
					content: 'Test content',
					headings: ['H1', 'H2'],
					metaKeywords: ['test'],
					wordCount: 10,
					readabilityScore: 65,
					language: 'en',
					contentHash: 'abc123',
				},
			];

			const metrics = (analyzer as any).calculateContentMetrics(mockPages);

			expect(metrics).toHaveProperty('totalWordCount', 10);
			expect(metrics).toHaveProperty('averageReadability', 65);
			expect(metrics).toHaveProperty('duplicateContentPercentage');
			expect(metrics).toHaveProperty('mediaToTextRatio');
			expect(metrics).toHaveProperty('headingStructureScore');
			expect(metrics).toHaveProperty('internalLinkDensity');

			expect(metrics.duplicateContentPercentage).toBe(0);
			expect(metrics.headingStructureScore).toBeGreaterThan(0);
		});
	});

	describe('technical SEO analysis', () =>
	{
		it('should analyze technical SEO aspects', () =>
		{
			const mockPages: PageContent[] = [
				{
					url: 'https://example.com',
					title: 'Test Page',
					description: 'Test description with sufficient length',
					content: 'Test content',
					headings: ['H1'],
					metaKeywords: ['test'],
					wordCount: 10,
					readabilityScore: 65,
					language: 'en',
					contentHash: 'abc123',
				},
			];

			const seoAnalysis = (analyzer as any).analyzeTechnicalSEO(mockPages);

			expect(seoAnalysis).toHaveProperty('hasStructuredData');
			expect(seoAnalysis).toHaveProperty('structuredDataTypes');
			expect(seoAnalysis).toHaveProperty('hasCanonicalTags');
			expect(seoAnalysis).toHaveProperty('hasMetaDescriptions');
			expect(seoAnalysis).toHaveProperty('hasAltTags');
			expect(seoAnalysis).toHaveProperty('imageOptimization');

			expect(seoAnalysis.hasMetaDescriptions).toBe(true);
			expect(Array.isArray(seoAnalysis.structuredDataTypes)).toBe(true);
		});
	});

	describe('content uniqueness calculation', () =>
	{
		it('should detect duplicate content', () =>
		{
			const uniquePages: PageContent[] = [
				{
					url: 'https://example.com/1',
					title: 'Page 1',
					description: 'Description 1',
					content: 'Unique content 1',
					headings: [],
					metaKeywords: [],
					wordCount: 3,
					readabilityScore: 65,
					language: 'en',
					contentHash: 'hash1',
				},
				{
					url: 'https://example.com/2',
					title: 'Page 2',
					description: 'Description 2',
					content: 'Unique content 2',
					headings: [],
					metaKeywords: [],
					wordCount: 3,
					readabilityScore: 65,
					language: 'en',
					contentHash: 'hash2',
				},
			];

			const duplicatePages: PageContent[] = [
				...uniquePages,
				{
					url: 'https://example.com/3',
					title: 'Page 3',
					description: 'Description 3',
					content: 'Duplicate content',
					headings: [],
					metaKeywords: [],
					wordCount: 2,
					readabilityScore: 65,
					language: 'en',
					contentHash: 'hash1', // Same hash as first page
				},
			];

			const uniquenessUnique = (analyzer as any).calculateContentUniqueness(uniquePages);
			const uniquenessDuplicate = (analyzer as any).calculateContentUniqueness(duplicatePages);

			expect(uniquenessUnique).toBe(1.0);
			expect(uniquenessDuplicate).toBeLessThan(1.0);
		});
	});
});
