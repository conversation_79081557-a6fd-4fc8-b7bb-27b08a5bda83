import {
	describe, it, expect, beforeEach, vi,
} from 'vitest';
import { RedisClientWrapper, ScyllaClient } from '@shared';
import FaviconCollector, { FaviconCollectionResult } from '../FaviconCollector';

// Mock the database clients for testing
vi.mock('@shared', () => ({
	Logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		})),
	},
	RedisClientWrapper: vi.fn().mockImplementation(() => ({
		connect: vi.fn(),
		disconnect: vi.fn(),
		get: vi.fn(),
		set: vi.fn(),
		del: vi.fn(),
		getClient: vi.fn().mockReturnValue({ keys: vi.fn().mockResolvedValue([]) }),
	})),
	ScyllaClient: vi.fn().mockImplementation(() => ({
		connect: vi.fn(),
		disconnect: vi.fn(),
		execute: vi.fn(),
		getClient: vi.fn().mockReturnValue({}),
	})),
	CacheManager: vi.fn().mockImplementation(() => ({})),
}));

describe('FaviconCollector', () =>
{
	let faviconCollector: FaviconCollector;
	let mockRedisClient: jest.Mocked<RedisClientWrapper>;
	let mockScyllaClient: jest.Mocked<ScyllaClient>;

	beforeEach(() =>
	{
		// Create mock instances
		mockRedisClient = new RedisClientWrapper() as jest.Mocked<RedisClientWrapper>;
		mockScyllaClient = new ScyllaClient() as jest.Mocked<ScyllaClient>;

		// Reset mocks
		vi.clearAllMocks();

		faviconCollector = new FaviconCollector(mockRedisClient, mockScyllaClient);
	});

	describe('collectFavicon', () =>
	{
		it('should collect favicon from DuckDuckGo API for well-known domains', async () =>
		{
			const result = await faviconCollector.collectFavicon('google.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('google.com');
			expect(result.lastCollected).toBeDefined();
			expect(typeof result.faviconFound).toBe('boolean');
			expect(result.sources).toBeDefined();
			expect(result.sources.duckduckgo).toBeDefined();
			expect(result.sources.direct).toBeDefined();
			expect(result.sources.html).toBeDefined();

			if (result.faviconFound)
			{
				expect(result.faviconUrl).toBeDefined();
				expect(result.faviconSize).toBeGreaterThan(0);
				expect(result.faviconType).toBeDefined();
				expect(result.fallbackUsed).toBeDefined();
			}
		}, 15000);

		it('should handle domains with direct favicon.ico', async () =>
		{
			const result = await faviconCollector.collectFavicon('github.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('github.com');
			expect(result.lastCollected).toBeDefined();

			// GitHub should have a favicon available through one of the methods
			if (result.faviconFound)
			{
				expect(result.faviconUrl).toBeDefined();
				expect(result.faviconSize).toBeGreaterThan(0);
				expect(['duckduckgo', 'direct', 'html']).toContain(result.fallbackUsed);
			}
		}, 15000);

		it('should handle domains without favicon gracefully', async () =>
		{
			// Using a domain that likely doesn't have a favicon
			const result = await faviconCollector.collectFavicon('nonexistent-domain-12345.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('nonexistent-domain-12345.com');
			expect(result.faviconFound).toBe(false);
			expect(result.faviconUrl).toBeUndefined();
			expect(result.fallbackUsed).toBeNull();
			expect(result.lastCollected).toBeDefined();
		}, 15000);

		it('should handle invalid domains gracefully', async () =>
		{
			const result = await faviconCollector.collectFavicon('invalid..domain');

			expect(result).toBeDefined();
			expect(result.domain).toBe('invalid..domain');
			expect(result.faviconFound).toBe(false);
			expect(result.lastCollected).toBeDefined();
		}, 15000);

		it('should track which sources were attempted', async () =>
		{
			const result = await faviconCollector.collectFavicon('example.com');

			expect(result).toBeDefined();
			expect(result.sources).toBeDefined();
			expect(typeof result.sources.duckduckgo).toBe('boolean');
			expect(typeof result.sources.direct).toBe('boolean');
			expect(typeof result.sources.html).toBe('boolean');
		}, 15000);

		it('should handle timeout scenarios', async () =>
		{
			// Test with a domain that might be slow to respond
			const result = await faviconCollector.collectFavicon('httpbin.org');

			expect(result).toBeDefined();
			expect(result.domain).toBe('httpbin.org');
			expect(result.lastCollected).toBeDefined();

			// Should complete within reasonable time even if some methods fail
		}, 20000);

		it('should prefer DuckDuckGo API when available', async () =>
		{
			const result = await faviconCollector.collectFavicon('stackoverflow.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('stackoverflow.com');

			if (result.faviconFound && result.fallbackUsed === 'duckduckgo')
			{
				expect(result.sources.duckduckgo).toBe(true);
				expect(result.faviconUrl).toContain('icons.duckduckgo.com');
			}
		}, 15000);

		it('should extract favicon from HTML when other methods fail', async () =>
		{
			// Test with a domain that might not be in DuckDuckGo but has HTML favicon
			const result = await faviconCollector.collectFavicon('reddit.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('reddit.com');

			if (result.faviconFound)
			{
				expect(result.faviconUrl).toBeDefined();
				expect(['duckduckgo', 'direct', 'html']).toContain(result.fallbackUsed);
			}
		}, 15000);

		it('should handle HTTPS and HTTP fallback correctly', async () =>
		{
			const result = await faviconCollector.collectFavicon('wikipedia.org');

			expect(result).toBeDefined();
			expect(result.domain).toBe('wikipedia.org');

			if (result.faviconFound)
			{
				expect(result.faviconUrl).toBeDefined();

				// Should prefer HTTPS when available
				if (result.fallbackUsed === 'direct')
				{
					expect(result.faviconUrl).toMatch(/^https?:\/\//);
				}
			}
		}, 15000);

		it('should validate favicon size and type', async () =>
		{
			const result = await faviconCollector.collectFavicon('amazon.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('amazon.com');

			if (result.faviconFound)
			{
				expect(result.faviconSize).toBeGreaterThan(0);
				expect(result.faviconType).toBeDefined();
				expect(result.faviconType).toMatch(/^image\//);
			}
		}, 15000);
	});

	describe('error handling', () =>
	{
		it('should handle network errors gracefully', async () =>
		{
			const result = await faviconCollector.collectFavicon('localhost:99999');

			expect(result).toBeDefined();
			expect(result.domain).toBe('localhost:99999');
			expect(result.faviconFound).toBe(false);
			expect(result.lastCollected).toBeDefined();
		}, 15000);

		it('should handle malformed URLs gracefully', async () =>
		{
			const result = await faviconCollector.collectFavicon('not-a-valid-domain');

			expect(result).toBeDefined();
			expect(result.domain).toBe('not-a-valid-domain');
			expect(result.faviconFound).toBe(false);
			expect(result.lastCollected).toBeDefined();
		}, 15000);
	});

	describe('performance', () =>
	{
		it('should complete favicon collection within reasonable time', async () =>
		{
			const startTime = Date.now();
			const result = await faviconCollector.collectFavicon('cloudflare.com');
			const endTime = Date.now();

			expect(result).toBeDefined();
			expect(endTime - startTime).toBeLessThan(20000); // Should complete within 20 seconds
		}, 25000);

		it('should handle multiple concurrent requests', async () =>
		{
			const domains = ['google.com', 'github.com', 'stackoverflow.com'];
			const promises = domains.map(domain => faviconCollector.collectFavicon(domain));

			const results = await Promise.all(promises);

			expect(results).toHaveLength(3);

			results.forEach((result, index) =>
			{
				expect(result.domain).toBe(domains[index]);
				expect(result.lastCollected).toBeDefined();
			});
		}, 30000);
	});

	describe('data structure validation', () =>
	{
		it('should return properly structured result object', async () =>
		{
			const result = await faviconCollector.collectFavicon('example.com');

			// Validate required fields
			expect(result).toHaveProperty('domain');
			expect(result).toHaveProperty('faviconFound');
			expect(result).toHaveProperty('sources');
			expect(result).toHaveProperty('fallbackUsed');
			expect(result).toHaveProperty('lastCollected');

			// Validate sources object structure
			expect(result.sources).toHaveProperty('duckduckgo');
			expect(result.sources).toHaveProperty('direct');
			expect(result.sources).toHaveProperty('html');

			// Validate data types
			expect(typeof result.domain).toBe('string');
			expect(typeof result.faviconFound).toBe('boolean');
			expect(typeof result.sources.duckduckgo).toBe('boolean');
			expect(typeof result.sources.direct).toBe('boolean');
			expect(typeof result.sources.html).toBe('boolean');
			expect(typeof result.lastCollected).toBe('string');

			// Validate optional fields when present
			if (result.faviconUrl)
			{
				expect(typeof result.faviconUrl).toBe('string');
				expect(result.faviconUrl).toMatch(/^https?:\/\//);
			}

			if (result.faviconSize)
			{
				expect(typeof result.faviconSize).toBe('number');
				expect(result.faviconSize).toBeGreaterThan(0);
			}

			if (result.faviconType)
			{
				expect(typeof result.faviconType).toBe('string');
			}

			if (result.fallbackUsed)
			{
				expect(['duckduckgo', 'direct', 'html']).toContain(result.fallbackUsed);
			}
		}, 15000);
	});

	describe('caching functionality', () =>
	{
		it('should use cached result when available', async () =>
		{
			const cachedResult: FaviconCollectionResult = {
				domain: 'cached-domain.com',
				faviconFound: true,
				faviconUrl: 'https://icons.duckduckgo.com/ip3/cached-domain.com.ico',
				faviconSize: 1024,
				faviconType: 'image/x-icon',
				sources: { duckduckgo: true, direct: false, html: false },
				fallbackUsed: 'duckduckgo',
				lastCollected: '2024-01-01T00:00:00.000Z',
			};

			// Mock cache hit
			mockRedisClient.get.mockResolvedValue(cachedResult);

			const result = await faviconCollector.collectFavicon('cached-domain.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('cached-domain.com');
			expect(result.faviconFound).toBe(true);
			expect(result.faviconUrl).toBe('https://icons.duckduckgo.com/ip3/cached-domain.com.ico');
			expect(mockRedisClient.get).toHaveBeenCalledWith('favicon:cached-domain.com');
		});

		it('should cache successful favicon collection', async () =>
		{
			// Mock cache miss
			mockRedisClient.get.mockResolvedValue(null);

			const result = await faviconCollector.collectFavicon('example.com');

			// Should attempt to cache the result
			expect(mockRedisClient.set).toHaveBeenCalled();
			expect(mockScyllaClient.execute).toHaveBeenCalled();
		});

		it('should cache negative results to avoid repeated attempts', async () =>
		{
			// Mock cache miss
			mockRedisClient.get.mockResolvedValue(null);

			const result = await faviconCollector.collectFavicon('nonexistent-favicon-domain.com');

			// Should cache even negative results
			expect(mockRedisClient.set).toHaveBeenCalled();
			expect(result.faviconFound).toBe(false);
		});

		it('should bypass cache when useCache is false', async () =>
		{
			const cachedResult: FaviconCollectionResult = {
				domain: 'cached-domain.com',
				faviconFound: true,
				faviconUrl: 'https://icons.duckduckgo.com/ip3/cached-domain.com.ico',
				faviconSize: 1024,
				faviconType: 'image/x-icon',
				sources: { duckduckgo: true, direct: false, html: false },
				fallbackUsed: 'duckduckgo',
				lastCollected: '2024-01-01T00:00:00.000Z',
			};

			// Mock cache hit
			mockRedisClient.get.mockResolvedValue(cachedResult);

			const result = await faviconCollector.collectFavicon('cached-domain.com', false);

			// Should not check cache when useCache is false
			expect(mockRedisClient.get).not.toHaveBeenCalled();
		});

		it('should handle cache errors gracefully', async () =>
		{
			// Mock cache error
			mockRedisClient.get.mockRejectedValue(new Error('Redis connection failed'));

			const result = await faviconCollector.collectFavicon('example.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('example.com');
			// Should continue with normal collection even if cache fails
		});

		it('should get favicon cache statistics', async () =>
		{
			// Mock Redis keys response
			const mockKeys = ['favicon:domain1.com', 'favicon:domain2.com', 'favicon:domain3.com'];
			mockRedisClient.getClient.mockReturnValue({
				keys: vi.fn().mockResolvedValue(mockKeys),
			} as any);

			// Mock cached results
			mockRedisClient.get
				.mockResolvedValueOnce({ faviconFound: true } as FaviconCollectionResult)
				.mockResolvedValueOnce({ faviconFound: false } as FaviconCollectionResult)
				.mockResolvedValueOnce({ faviconFound: true } as FaviconCollectionResult);

			const stats = await faviconCollector.getFaviconCacheStats();

			expect(stats).toBeDefined();
			expect(stats.totalCached).toBe(3);
			expect(typeof stats.successfulCached).toBe('number');
			expect(typeof stats.failedCached).toBe('number');
		});
	});

	describe('storage functionality', () =>
	{
		it('should store favicon results in ScyllaDB', async () =>
		{
			// Mock cache miss
			mockRedisClient.get.mockResolvedValue(null);

			const result = await faviconCollector.collectFavicon('example.com');

			// Should attempt to store in ScyllaDB
			expect(mockScyllaClient.execute).toHaveBeenCalled();
		});

		it('should retrieve stored favicon from ScyllaDB', async () =>
		{
			const mockRow = {
				technical_metrics: new Map([
					['favicon_url', 'https://example.com/favicon.ico'],
					['favicon_found', 'true'],
					['favicon_size', '1024'],
					['favicon_type', 'image/x-icon'],
					['favicon_source', 'direct'],
					['favicon_last_collected', '2024-01-01T00:00:00.000Z'],
				]),
			};

			mockScyllaClient.execute.mockResolvedValue({
				rows: [mockRow],
				first: () => mockRow,
			} as any);

			const result = await faviconCollector.getStoredFavicon('example.com');

			expect(result).toBeDefined();
			expect(result?.domain).toBe('example.com');
			expect(result?.faviconFound).toBe(true);
			expect(result?.faviconUrl).toBe('https://example.com/favicon.ico');
			expect(result?.faviconSize).toBe(1024);
			expect(result?.faviconType).toBe('image/x-icon');
			expect(result?.fallbackUsed).toBe('direct');
		});

		it('should handle storage errors gracefully', async () =>
		{
			// Mock cache miss
			mockRedisClient.get.mockResolvedValue(null);
			// Mock storage error
			mockScyllaClient.execute.mockRejectedValue(new Error('ScyllaDB connection failed'));

			const result = await faviconCollector.collectFavicon('example.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('example.com');
			// Should continue even if storage fails
		});

		it('should return null for non-existent stored favicon', async () =>
		{
			mockScyllaClient.execute.mockResolvedValue({
				rows: [],
				first: () => null,
			} as any);

			const result = await faviconCollector.getStoredFavicon('nonexistent.com');

			expect(result).toBeNull();
		});
	});

	describe('cache invalidation', () =>
	{
		it('should invalidate favicon cache for domain', async () =>
		{
			await faviconCollector.invalidateFaviconCache('example.com');

			expect(mockRedisClient.del).toHaveBeenCalledWith('favicon:example.com');
		});

		it('should handle cache invalidation errors gracefully', async () =>
		{
			mockRedisClient.del.mockRejectedValue(new Error('Redis delete failed'));

			// Should not throw error
			await expect(faviconCollector.invalidateFaviconCache('example.com')).resolves.not.toThrow();
		});
	});

	describe('integration with database clients', () =>
	{
		it('should initialize database connections when needed', async () =>
		{
			// Mock clients as not connected initially
			mockRedisClient.getClient.mockReturnValue(null);
			mockScyllaClient.getClient.mockReturnValue(null);

			await faviconCollector.collectFavicon('example.com');

			// Should attempt to connect to databases
			expect(mockScyllaClient.connect).toHaveBeenCalled();
		});

		it('should handle database connection failures gracefully', async () =>
		{
			mockScyllaClient.connect.mockRejectedValue(new Error('Connection failed'));
			mockScyllaClient.getClient.mockReturnValue(null);

			const result = await faviconCollector.collectFavicon('example.com');

			expect(result).toBeDefined();
			expect(result.domain).toBe('example.com');
			// Should continue even if database connection fails
		});
	});
});
