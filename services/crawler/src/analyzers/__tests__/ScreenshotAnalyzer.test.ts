import {
	describe, it, expect, beforeEach, afterEach, vi,
} from 'vitest';
import axios from 'axios';
import { Logger, Config } from '@shared';
import ScreenshotAnalyzer, { ScreenshotResult } from '../ScreenshotAnalyzer';

// Mock dependencies
vi.mock('axios');
vi.mock('@shared', () => ({
	Logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			debug: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
		})),
	},
	Config: {
		get: vi.fn(),
	},
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedConfig = Config as jest.Mocked<typeof Config>;

describe('ScreenshotAnalyzer', () =>
{
	let analyzer: ScreenshotAnalyzer;
	const mockDomain = 'example.com';

	beforeEach(() =>
	{
		vi.clearAllMocks();

		// Mock environment variables
		process.env.BROWSERLESS_URL = 'http://test-browserless:3000';
		mockedConfig.get.mockReturnValue('http://test-imageproxy:8080');

		analyzer = new ScreenshotAnalyzer();
	});

	afterEach(() =>
	{
		delete process.env.BROWSERLESS_URL;
	});

	describe('captureScreenshots', () =>
	{
		it('should capture both desktop and mobile screenshots successfully', async () =>
		{
			// Mock successful browserless responses
			const mockScreenshotBuffer = Buffer.from('fake-screenshot-data');
			mockedAxios.post
				.mockResolvedValueOnce({
					status: 200,
					data: mockScreenshotBuffer,
				})
				.mockResolvedValueOnce({
					status: 200,
					data: mockScreenshotBuffer,
				});

			// Mock image proxy optimization
			mockedAxios.get.mockResolvedValue({
				status: 200,
				data: Buffer.from('optimized-screenshot-data'),
			});

			const result = await analyzer.captureScreenshots(mockDomain);

			expect(result).toBeDefined();
			expect(result.domain).toBe(mockDomain);
			expect(result.screenshots.desktop).toBeDefined();
			expect(result.screenshots.mobile).toBeDefined();
			expect(result.screenshots.desktop.width).toBe(1920);
			expect(result.screenshots.desktop.height).toBe(1080);
			expect(result.screenshots.mobile.width).toBe(375);
			expect(result.screenshots.mobile.height).toBe(667);
			expect(result.captureTime).toBeGreaterThan(0);
			expect(result.metadata).toBeDefined();
			expect(result.metadata.captureSettings).toBeDefined();
			expect(result.metadata.optimization).toBeDefined();
		});

		it('should handle browserless service failure gracefully', async () =>
		{
			// Mock browserless failure
			mockedAxios.post.mockRejectedValue(new Error('Browserless service unavailable'));

			const result = await analyzer.captureScreenshots(mockDomain);

			expect(result).toBeDefined();
			expect(result.domain).toBe(mockDomain);
			expect(result.screenshots.desktop.error).toBeDefined();
			expect(result.screenshots.mobile.error).toBeDefined();
			expect(result.captureTime).toBeGreaterThanOrEqual(0);
			expect(result.error).toBe('Both desktop and mobile screenshot capture failed');
		});

		it('should handle partial failure (one viewport succeeds, one fails)', async () =>
		{
			// Mock desktop success, mobile failure
			const mockScreenshotBuffer = Buffer.from('fake-screenshot-data');
			mockedAxios.post
				.mockResolvedValueOnce({
					status: 200,
					data: mockScreenshotBuffer,
				})
				.mockRejectedValueOnce(new Error('Mobile screenshot failed'));

			// Mock image proxy optimization for successful screenshot
			mockedAxios.get.mockResolvedValue({
				status: 200,
				data: Buffer.from('optimized-screenshot-data'),
			});

			const result = await analyzer.captureScreenshots(mockDomain);

			expect(result).toBeDefined();
			expect(result.domain).toBe(mockDomain);
			expect(result.screenshots.desktop.url).toBeDefined();
			expect(result.screenshots.desktop.error).toBeUndefined();
			expect(result.screenshots.mobile.error).toBeDefined();
			expect(result.captureTime).toBeGreaterThanOrEqual(0);
		});
	});

	describe('captureCustomScreenshot', () =>
	{
		it('should capture screenshot with custom viewport and options', async () =>
		{
			const customViewport = { width: 1366, height: 768 };
			const customOptions = {
				fullPage: true,
				quality: 95,
				format: 'jpeg' as const,
				timeout: 45000,
			};

			const mockScreenshotBuffer = Buffer.from('custom-screenshot-data');
			mockedAxios.post.mockResolvedValue({
				status: 200,
				data: mockScreenshotBuffer,
			});

			const result = await analyzer.captureCustomScreenshot(
				mockDomain,
				customViewport,
				customOptions,
			);

			expect(result).toBeDefined();
			expect(result.url).toBeDefined();
			expect(result.size).toBe(mockScreenshotBuffer.length);
			expect(mockedAxios.post).toHaveBeenCalledWith(
				'http://test-browserless:3000/screenshot',
				expect.objectContaining({
					url: `https://${mockDomain}`,
					options: expect.objectContaining({
						viewport: customViewport,
						fullPage: true,
						type: 'jpeg',
						quality: 95,
					}),
					gotoOptions: expect.objectContaining({
						timeout: 45000,
					}),
				}),
				expect.any(Object),
			);
		});
	});

	describe('validateCapability', () =>
	{
		it('should validate browserless and image proxy availability', async () =>
		{
			// Mock successful responses
			mockedAxios.get.mockResolvedValue({ status: 200, data: {} });
			mockedAxios.head.mockResolvedValue({ status: 200 });

			const result = await analyzer.validateCapability();

			expect(result).toBeDefined();
			expect(result.browserlessAvailable).toBe(true);
			expect(result.imageProxyAvailable).toBe(true);
			expect(result.browserlessStatus).toBeDefined();
		});

		it('should handle service unavailability', async () =>
		{
			// Mock service failures
			mockedAxios.get.mockRejectedValue(new Error('Service unavailable'));
			mockedAxios.head.mockRejectedValue(new Error('Service unavailable'));

			const result = await analyzer.validateCapability();

			expect(result).toBeDefined();
			expect(result.browserlessAvailable).toBe(false);
			expect(result.imageProxyAvailable).toBe(false);
		});
	});

	describe('batchCaptureScreenshots', () =>
	{
		it('should capture screenshots for multiple domains with controlled concurrency', async () =>
		{
			const domains = ['example1.com', 'example2.com', 'example3.com'];
			const mockScreenshotBuffer = Buffer.from('batch-screenshot-data');

			// Mock successful responses for all domains
			mockedAxios.post.mockResolvedValue({
				status: 200,
				data: mockScreenshotBuffer,
			});

			mockedAxios.get.mockResolvedValue({
				status: 200,
				data: Buffer.from('optimized-batch-data'),
			});

			const results = await analyzer.batchCaptureScreenshots(domains, {
				concurrency: 2,
			});

			expect(results).toHaveLength(3);
			expect(results.every(r => r.domain && r.screenshots)).toBe(true);
			expect(results.every(r => !r.error)).toBe(true);
		});

		it('should handle mixed success/failure in batch processing', async () =>
		{
			const domains = ['success.com', 'failure.com'];
			const mockScreenshotBuffer = Buffer.from('batch-screenshot-data');

			// Mock success for first domain, failure for second
			mockedAxios.post
				.mockResolvedValueOnce({
					status: 200,
					data: mockScreenshotBuffer,
				})
				.mockResolvedValueOnce({
					status: 200,
					data: mockScreenshotBuffer,
				})
				.mockRejectedValueOnce(new Error('Screenshot failed'))
				.mockRejectedValueOnce(new Error('Screenshot failed'));

			mockedAxios.get.mockResolvedValue({
				status: 200,
				data: Buffer.from('optimized-data'),
			});

			const results = await analyzer.batchCaptureScreenshots(domains);

			expect(results).toHaveLength(2);
			expect(results[0].error).toBeUndefined();
			expect(results[1].error).toBe('Both desktop and mobile screenshot capture failed');
		});
	});

	describe('getBrowserlessStatus', () =>
	{
		it('should get browserless service status and metrics', async () =>
		{
			const mockPressureData = { cpu: 0.5, memory: 0.3 };
			const mockStatsData = { sessions: 2, queued: 0 };

			mockedAxios.get
				.mockResolvedValueOnce({
					status: 200,
					data: mockPressureData,
				})
				.mockResolvedValueOnce({
					status: 200,
					data: mockStatsData,
				});

			const result = await analyzer.getBrowserlessStatus();

			expect(result).toBeDefined();
			expect(result.available).toBe(true);
			expect(result.pressure).toEqual(mockPressureData);
			expect(result.stats).toEqual(mockStatsData);
		});

		it('should handle browserless service unavailability', async () =>
		{
			mockedAxios.get.mockRejectedValue(new Error('Service unavailable'));

			const result = await analyzer.getBrowserlessStatus();

			expect(result).toBeDefined();
			expect(result.available).toBe(false);
			expect(result.pressure).toBeUndefined();
			expect(result.stats).toBeUndefined();
		});
	});
});
