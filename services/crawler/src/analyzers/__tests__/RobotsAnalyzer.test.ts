import {
	describe, test, it, expect, beforeEach, afterEach, vi,
} from 'vitest';
import RobotsAnalyzer from '../RobotsAnalyzer';

// Mock the Logger
vi.mock('@shared', () => ({
	Logger: {
		getLogger: () => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
	Constants: {
		CRAWL: {
			TIMEOUT: 30000,
			USER_AGENTS: [
				'Mozilla/5.0 (compatible; DomainRankingBot/1.0)',
			],
		},
	},
}));

describe('RobotsAnalyzer', () =>
{
	let analyzer: RobotsAnalyzer;

	beforeEach(() =>
	{
		analyzer = new RobotsAnalyzer();
	});

	describe('analyzeRobotsTxt', () =>
	{
		it('should analyze robots.txt for a valid domain', async () =>
		{
			const result = await analyzer.analyzeRobotsTxt('example.com');

			expect(result).toHaveProperty('domain', 'example.com');
			expect(result).toHaveProperty('exists');
			expect(result).toHaveProperty('accessible');
			expect(result).toHaveProperty('sitemaps');
			expect(result).toHaveProperty('complianceStatus');
			expect(result).toHaveProperty('lastAnalyzed');
		});

		it('should handle domains without robots.txt', async () =>
		{
			const result = await analyzer.analyzeRobotsTxt('nonexistent-domain-test.com');

			expect(result.domain).toBe('nonexistent-domain-test.com');
			expect(result.exists).toBe(false);
			expect(result.accessible).toBe(false);
		});

		it('should validate compliance status', async () =>
		{
			const result = await analyzer.analyzeRobotsTxt('google.com');

			expect(result.complianceStatus).toHaveProperty('isCompliant');
			expect(result.complianceStatus).toHaveProperty('violations');
			expect(result.complianceStatus).toHaveProperty('warnings');
			expect(Array.isArray(result.complianceStatus.violations)).toBe(true);
			expect(Array.isArray(result.complianceStatus.warnings)).toBe(true);
		});
	});
});
