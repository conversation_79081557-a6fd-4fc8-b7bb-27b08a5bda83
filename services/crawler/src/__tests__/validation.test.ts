import {
	describe, it, expect, vi, beforeEach, afterEach,
} from 'vitest';

// Import after mocks
import CrawlerService from '../index';

vi.mock('xss', () => ({ default: (s: string) => s }));

vi.mock('../utils/DomainDescriptionBuilder', () => ({
	buildDomainDescription: vi.fn(() => ({ metadata: { domain: 'bad domain' } })),
}));

vi.mock('@shared', async (orig) =>
{
	const mod = await orig();
	return {
		...mod,
		DomainDescriptionValidator: {
			get: () => new (class
			{
				validate() { return { ok: false, errors: [{ instancePath: '/metadata/domain', message: 'invalid' }] } }

				assert() { throw new Error('DomainDescription validation failed') }
			})(),
		},
	};
});

const OLD = process.env.VALIDATE_DESCRIPTIONS;

describe('crawler validation gate', () =>
{
	beforeEach(() =>
	{
		vi.restoreAllMocks();
	});

	afterEach(() =>
	{
		process.env.VALIDATE_DESCRIPTIONS = OLD;
	});

	it('throws when flag on and invalid desc', async () =>
	{
		process.env.VALIDATE_DESCRIPTIONS = '1';
		const crawler = new CrawlerService();
		vi.spyOn(crawler as any, 'crawlDomain').mockResolvedValue({
			domain: 'x', crawlType: 'basic', timestamp: '', status: 'completed', data: { basicInfo: { domain: 'x', accessible: true, responseTime: 1 } },
		});

		await expect(crawler.handleDomainCrawlJob({ domain: 'x', crawlType: 'basic', priority: 'low' } as any, {})).rejects.toThrow();
	});

	it('does not throw when flag off', async () =>
	{
		process.env.VALIDATE_DESCRIPTIONS = '0';
		const crawler = new CrawlerService();
		vi.spyOn(crawler as any, 'crawlDomain').mockResolvedValue({
			domain: 'x', crawlType: 'basic', timestamp: '', status: 'completed', data: { basicInfo: { domain: 'x', accessible: true, responseTime: 1 } },
		});
		vi.spyOn(crawler as any, 'storeAnalysisResult').mockResolvedValue(undefined);
		vi.spyOn((crawler as any).jobQueue, 'publishRankingUpdateJob').mockResolvedValue(undefined as any);

		await expect(crawler.handleDomainCrawlJob({ domain: 'x', crawlType: 'basic', priority: 'low' } as any, {})).resolves.toBeUndefined();
	});
});
