import type { Logger, ComprehensiveDomainData } from '@shared';
import {
	vi, describe, beforeEach, it, expect,
} from 'vitest';
import { DomainDataValidator } from '../DomainDataValidator';
import { DataAvailability, DataPriority, CollectionPhase } from '../ComprehensiveDomainSchema';

// Mock logger
const mockLogger: Logger = {
	info: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
	debug: vi.fn(),
};

describe('DomainDataValidator', () =>
{
	let validator: DomainDataValidator;

	beforeEach(() =>
	{
		validator = new DomainDataValidator(mockLogger);
		vi.clearAllMocks();
	});

	describe('validateDomainData', () =>
	{
		it('should validate complete domain data successfully', async () =>
		{
			const validData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
			};

			const result = await validator.validateDomainData(validData);

			expect(result.valid).toBe(true);
			expect(result.errors.length).toBe(0);
			expect(result.fieldValidation.size).toBeGreaterThan(0);
		});

		it('should detect validation errors', async () =>
		{
			const invalidData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'invalid-domain', // Missing TLD
					normalizedDomain: 'invalid-domain',
					rootDomain: 'invalid-domain',
					tld: '',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
			};

			const result = await validator.validateDomainData(invalidData);

			expect(result.valid).toBe(false);
			expect(result.errors.length).toBeGreaterThan(0);
		});
	});

	describe('checkDataCompleteness', () =>
	{
		it('should check completeness for empty data', async () =>
		{
			const emptyData = await validator.createEmptyDomainData('example.com');
			const report = await validator.checkDataCompleteness(emptyData);

			expect(report.domain).toBe('example.com');
			expect(report.overallCompleteness).toBe(0);
			expect(report.missingCriticalFields.length).toBeGreaterThan(0);
			expect(report.recommendations.length).toBeGreaterThan(0);
		});

		it('should check completeness for partial data', async () =>
		{
			const partialData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
			};

			const report = await validator.checkDataCompleteness(partialData);

			expect(report.domain).toBe('example.com');
			expect(report.overallCompleteness).toBeGreaterThan(0);
			expect(report.overallCompleteness).toBeLessThan(100);
		});
	});

	describe('getPriorityMissingFields', () =>
	{
		it('should identify missing fields by priority', async () =>
		{
			const emptyData = await validator.createEmptyDomainData('example.com');
			const priorityFields = await validator.getPriorityMissingFields(emptyData);

			expect(priorityFields.critical.length).toBeGreaterThan(0);
			expect(priorityFields.high.length).toBeGreaterThan(0);
			expect(priorityFields.medium.length).toBeGreaterThan(0);
		});

		it('should return empty arrays when all data is available', async () =>
		{
			// This would be a fully populated domain data object
			const completeData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
				// Add more complete sections as needed for testing
			};

			const priorityFields = await validator.getPriorityMissingFields(completeData);

			// With only identification filled, other critical fields should still be missing
			expect(priorityFields.critical.length).toBeGreaterThan(0);
		});
	});

	describe('createEmptyDomainData', () =>
	{
		it('should create empty domain data structure', async () =>
		{
			const domain = 'test.com';
			const emptyData = await validator.createEmptyDomainData(domain);

			expect(emptyData.identification.domain).toBe(domain);
			expect(emptyData.identification.rootDomain).toBe(domain);
			expect(emptyData.identification.tld).toBe('com');
			expect(emptyData.crawlInfo.success).toBe(false);
			expect(emptyData.ranking.scores.overall).toBe(0);
		});

		it('should handle subdomains correctly', async () =>
		{
			const domain = 'blog.example.com';
			const emptyData = await validator.createEmptyDomainData(domain);

			expect(emptyData.identification.domain).toBe(domain);
			expect(emptyData.identification.rootDomain).toBe('example.com');
			expect(emptyData.identification.tld).toBe('com');
		});
	});

	describe('assessDataQuality', () =>
	{
		it('should assess quality for empty data', async () =>
		{
			const emptyData = await validator.createEmptyDomainData('example.com');
			const assessment = await validator.assessDataQuality(emptyData);

			expect(assessment.domain).toBe('example.com');
			expect(assessment.overallQuality).toBeLessThan(50); // Should be low for empty data
			expect(assessment.completeness.overall).toBe(0);
			expect(assessment.validation.valid).toBe(true); // Empty data might still be valid
			expect(assessment.recommendations.length).toBeGreaterThan(0);
			expect(assessment.nextActions.length).toBeGreaterThan(0);
		});

		it('should assess quality for partial data', async () =>
		{
			const partialData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
			};

			const assessment = await validator.assessDataQuality(partialData);

			expect(assessment.domain).toBe('example.com');
			expect(assessment.overallQuality).toBeGreaterThan(0);
			expect(assessment.completeness.overall).toBeGreaterThan(0);
		});
	});

	describe('isReadyForRanking', () =>
	{
		it('should return false for empty data', async () =>
		{
			const emptyData = await validator.createEmptyDomainData('example.com');
			const readiness = await validator.isReadyForRanking(emptyData);

			expect(readiness.ready).toBe(false);
			expect(readiness.missingCriticalFields.length).toBeGreaterThan(0);
			expect(readiness.minimumDataPresent).toBe(false);
			expect(readiness.blockers.length).toBeGreaterThan(0);
		});

		it('should identify specific ranking blockers', async () =>
		{
			const partialData: Partial<ComprehensiveDomainData> = {
				// Missing critical fields for ranking
			};

			const readiness = await validator.isReadyForRanking(partialData);

			expect(readiness.ready).toBe(false);
			expect(readiness.blockers).toContain('Domain identification missing');
			expect(readiness.blockers).toContain('Performance data missing');
			expect(readiness.blockers).toContain('Security data missing');
		});

		it('should return true when minimum data is available', async () =>
		{
			const minimalData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
				performance: {
					coreWebVitals: {
						largestContentfulPaint: 2500,
						firstInputDelay: 100,
						cumulativeLayoutShift: 0.1,
						firstContentfulPaint: 1500,
						timeToInteractive: 3000,
						totalBlockingTime: 200,
					},
					loadTimes: {
						domContentLoaded: 1000,
						fullyLoaded: 3000,
						firstByte: 500,
						startRender: 1200,
						speedIndex: 2000,
						visualComplete: 2800,
					},
					resources: {
						totalRequests: 50,
						totalSize: 1000000,
						htmlSize: 50000,
						cssSize: 100000,
						jsSize: 200000,
						imageSize: 500000,
						fontSize: 50000,
						otherSize: 100000,
						compressionRatio: 0.7,
					},
					network: {
						rtt: 50,
						downlink: 10,
						saveData: false,
					},
					mobile: {
						score: 85,
						usability: 90,
						loadTime: 3000,
						interactivity: 80,
					},
					budgetCompliance: {
						overall: true,
						javascript: true,
						css: true,
						images: false,
						fonts: true,
					},
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_2_MODERATE,
					},
				},
				security: {
					ssl: {
						grade: 'A',
						issuer: {
							commonName: 'Test CA',
							organization: 'Test Org',
							country: 'US',
						},
						subject: {
							commonName: 'example.com',
							alternativeNames: ['www.example.com'],
						},
						validFrom: new Date(),
						validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
						daysUntilExpiry: 365,
						keySize: 2048,
						signatureAlgorithm: 'SHA256withRSA',
						certificateChain: [],
						ocspStapling: false,
						hsts: {
							enabled: true,
						},
						vulnerabilities: [],
					},
					headers: {
						strictTransportSecurity: true,
						contentSecurityPolicy: {
							present: true,
							directives: {},
							violations: [],
						},
						xFrameOptions: 'DENY',
						xContentTypeOptions: true,
						referrerPolicy: 'strict-origin-when-cross-origin',
						permissionsPolicy: {},
						crossOriginEmbedderPolicy: '',
						crossOriginOpenerPolicy: '',
						crossOriginResourcePolicy: '',
					},
					vulnerabilities: [],
					thirdParty: {
						trackers: [],
						socialWidgets: [],
						advertisingNetworks: [],
						analyticsProviders: [],
					},
					privacy: {
						cookieConsent: true,
						gdprCompliant: true,
						ccpaCompliant: false,
						privacyPolicyPresent: true,
						dataProcessingTransparency: 0.8,
					},
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.HIGH,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
			};

			const readiness = await validator.isReadyForRanking(minimalData);

			expect(readiness.blockers.length).toBe(0);
			// Note: ready might still be false due to other missing critical fields
		});
	});

	describe('generateCollectionPlan', () =>
	{
		it('should generate collection plan for empty data', async () =>
		{
			const emptyData = await validator.createEmptyDomainData('example.com');
			const plan = await validator.generateCollectionPlan(emptyData);

			expect(plan.domain).toBe('example.com');
			expect(plan.phases.length).toBeGreaterThan(0);
			expect(plan.estimatedDuration).toBeGreaterThan(0);
			expect(plan.priority).toBe('critical'); // Should be critical for empty data
		});

		it('should include Phase 1 for critical missing fields', async () =>
		{
			const emptyData = await validator.createEmptyDomainData('example.com');
			const plan = await validator.generateCollectionPlan(emptyData);

			const phase1 = plan.phases.find(p => p.phase === CollectionPhase.PHASE_1_BASIC);

			expect(phase1).toBeDefined();
			expect(phase1?.fields.length).toBeGreaterThan(0);
			expect(phase1?.modules).toContain('dns');
			expect(phase1?.modules).toContain('ssl');
		});

		it('should adjust resource requirements based on phases', async () =>
		{
			const emptyData = await validator.createEmptyDomainData('example.com');
			const plan = await validator.generateCollectionPlan(emptyData);

			// Should start with low/medium resources for Phase 1
			expect(['low', 'medium']).toContain(plan.resourceRequirements.cpu);
			expect(['low', 'medium']).toContain(plan.resourceRequirements.memory);
		});

		it('should set correct dependencies between phases', async () =>
		{
			const emptyData = await validator.createEmptyDomainData('example.com');
			const plan = await validator.generateCollectionPlan(emptyData);

			const phase2 = plan.phases.find(p => p.phase === CollectionPhase.PHASE_2_MODERATE);
			if (phase2)
			{
				expect(phase2.dependencies).toContain(CollectionPhase.PHASE_1_BASIC);
			}

			const phase3 = plan.phases.find(p => p.phase === CollectionPhase.PHASE_3_ADVANCED);
			if (phase3)
			{
				expect(phase3.dependencies).toContain(CollectionPhase.PHASE_1_BASIC);
				expect(phase3.dependencies).toContain(CollectionPhase.PHASE_2_MODERATE);
			}
		});
	});
});
