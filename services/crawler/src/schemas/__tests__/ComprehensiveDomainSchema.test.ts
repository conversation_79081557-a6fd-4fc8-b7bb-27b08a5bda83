import type { Logger } from '@shared';
import {
	vi, describe, beforeEach, it, expect,
} from 'vitest';
import {
	DomainDataTracker,
	DataAvailability,
	DataPriority,
	CollectionPhase,
	type ComprehensiveDomainData,
} from '../ComprehensiveDomainSchema';

// Mock logger
const mockLogger: Logger = {
	info: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
	debug: vi.fn(),
};

describe('DomainDataTracker', () =>
{
	let tracker: DomainDataTracker;

	beforeEach(() =>
	{
		tracker = new DomainDataTracker(mockLogger);
		vi.clearAllMocks();
	});

	describe('createEmptyDomainData', () =>
	{
		it('should create empty domain data structure with correct metadata', () =>
		{
			const domain = 'example.com';
			const emptyData = tracker.createEmptyDomainData(domain);

			expect(emptyData.identification.domain).toBe(domain);
			expect(emptyData.identification.normalizedDomain).toBe(domain.toLowerCase());
			expect(emptyData.identification.rootDomain).toBe(domain);
			expect(emptyData.identification.tld).toBe('com');
			expect(emptyData.identification._metadata.availability).toBe(DataAvailability.MISSING);
			expect(emptyData.identification._metadata.priority).toBe(DataPriority.CRITICAL);
		});

		it('should handle subdomain correctly', () =>
		{
			const domain = 'blog.example.com';
			const emptyData = tracker.createEmptyDomainData(domain);

			expect(emptyData.identification.domain).toBe(domain);
			expect(emptyData.identification.rootDomain).toBe('example.com');
			expect(emptyData.identification.tld).toBe('com');
		});

		it('should initialize all required sections with metadata', () =>
		{
			const domain = 'test.com';
			const emptyData = tracker.createEmptyDomainData(domain);

			// Check that all main sections exist with metadata
			expect(emptyData.identification._metadata).toBeDefined();
			expect(emptyData.dns._metadata).toBeDefined();
			expect(emptyData.whois._metadata).toBeDefined();
			expect(emptyData.performance._metadata).toBeDefined();
			expect(emptyData.security._metadata).toBeDefined();
			expect(emptyData.seo._metadata).toBeDefined();
			expect(emptyData.technology._metadata).toBeDefined();
			expect(emptyData.visual._metadata).toBeDefined();
			expect(emptyData.content._metadata).toBeDefined();
			expect(emptyData.ranking._metadata).toBeDefined();
			expect(emptyData.crawlInfo._metadata).toBeDefined();
		});

		it('should set correct priorities for different sections', () =>
		{
			const domain = 'test.com';
			const emptyData = tracker.createEmptyDomainData(domain);

			expect(emptyData.identification._metadata.priority).toBe(DataPriority.CRITICAL);
			expect(emptyData.dns._metadata.priority).toBe(DataPriority.HIGH);
			expect(emptyData.performance._metadata.priority).toBe(DataPriority.CRITICAL);
			expect(emptyData.security._metadata.priority).toBe(DataPriority.HIGH);
			expect(emptyData.seo._metadata.priority).toBe(DataPriority.HIGH);
			expect(emptyData.technology._metadata.priority).toBe(DataPriority.MEDIUM);
			expect(emptyData.visual._metadata.priority).toBe(DataPriority.MEDIUM);
			expect(emptyData.content._metadata.priority).toBe(DataPriority.MEDIUM);
		});

		it('should set correct collection phases', () =>
		{
			const domain = 'test.com';
			const emptyData = tracker.createEmptyDomainData(domain);

			expect(emptyData.identification._metadata.phase).toBe(CollectionPhase.PHASE_1_BASIC);
			expect(emptyData.dns._metadata.phase).toBe(CollectionPhase.PHASE_1_BASIC);
			expect(emptyData.performance._metadata.phase).toBe(CollectionPhase.PHASE_2_MODERATE);
			expect(emptyData.visual._metadata.phase).toBe(CollectionPhase.PHASE_2_MODERATE);
			expect(emptyData.content._metadata.phase).toBe(CollectionPhase.PHASE_2_MODERATE);
		});
	});

	describe('trackDataAvailability', () =>
	{
		it('should track availability for empty domain data', () =>
		{
			const domain = 'example.com';
			const emptyData = tracker.createEmptyDomainData(domain);

			const report = tracker.trackDataAvailability(emptyData);

			expect(report.domain).toBe(domain);
			expect(report.overallCompleteness).toBe(0);
			expect(report.missingCriticalFields.length).toBeGreaterThan(0);
			expect(report.fieldStatus.size).toBeGreaterThan(0);
		});

		it('should track availability for partially filled data', () =>
		{
			const partialData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
				dns: {
					a: ['***********'],
					aaaa: [],
					mx: [],
					cname: [],
					txt: [],
					ns: ['ns1.example.com'],
					srv: [],
					caa: [],
					dnsSecEnabled: false,
					responseTime: 50,
					authoritative: true,
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.HIGH,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
			};

			const report = tracker.trackDataAvailability(partialData);

			expect(report.domain).toBe('example.com');
			expect(report.overallCompleteness).toBeGreaterThan(0);
			expect(report.overallCompleteness).toBeLessThan(100);
		});

		it('should calculate phase completeness correctly', () =>
		{
			const partialData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
			};

			const report = tracker.trackDataAvailability(partialData);

			expect(report.phaseCompleteness[CollectionPhase.PHASE_1_BASIC]).toBeGreaterThan(0);
			expect(report.phaseCompleteness[CollectionPhase.PHASE_2_MODERATE]).toBe(0);
			expect(report.phaseCompleteness[CollectionPhase.PHASE_3_ADVANCED]).toBe(0);
		});

		it('should identify missing critical fields', () =>
		{
			const partialData: Partial<ComprehensiveDomainData> = {
				// Missing identification (critical)
				dns: {
					a: ['***********'],
					aaaa: [],
					mx: [],
					cname: [],
					txt: [],
					ns: [],
					srv: [],
					caa: [],
					dnsSecEnabled: false,
					responseTime: 0,
					authoritative: false,
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.HIGH,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
			};

			const report = tracker.trackDataAvailability(partialData);

			expect(report.missingCriticalFields.length).toBeGreaterThan(0);
			expect(report.missingCriticalFields).toContain('identification');
		});

		it('should generate appropriate recommendations', () =>
		{
			const emptyData = tracker.createEmptyDomainData('example.com');
			const report = tracker.trackDataAvailability(emptyData);

			expect(report.recommendations.length).toBeGreaterThan(0);
			expect(report.recommendations.some(r => r.includes('Critical fields missing'))).toBe(true);
		});
	});

	describe('validateDomainData', () =>
	{
		it('should validate domain with valid data', () =>
		{
			const validData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'example.com',
					normalizedDomain: 'example.com',
					rootDomain: 'example.com',
					tld: 'com',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
				performance: {
					coreWebVitals: {
						largestContentfulPaint: 2500, // Valid LCP
						firstInputDelay: 100,
						cumulativeLayoutShift: 0.1,
						firstContentfulPaint: 1500,
						timeToInteractive: 3000,
						totalBlockingTime: 200,
					},
					loadTimes: {
						domContentLoaded: 1000,
						fullyLoaded: 3000,
						firstByte: 500,
						startRender: 1200,
						speedIndex: 2000,
						visualComplete: 2800,
					},
					resources: {
						totalRequests: 50,
						totalSize: 1000000,
						htmlSize: 50000,
						cssSize: 100000,
						jsSize: 200000,
						imageSize: 500000,
						fontSize: 50000,
						otherSize: 100000,
						compressionRatio: 0.7,
					},
					network: {
						rtt: 50,
						downlink: 10,
						saveData: false,
					},
					mobile: {
						score: 85,
						usability: 90,
						loadTime: 3000,
						interactivity: 80,
					},
					budgetCompliance: {
						overall: true,
						javascript: true,
						css: true,
						images: false,
						fonts: true,
					},
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_2_MODERATE,
					},
				},
			};

			const result = tracker.validateDomainData(validData);

			expect(result.valid).toBe(true);
			expect(result.errors.length).toBe(0);
		});

		it('should detect validation errors', () =>
		{
			const invalidData: Partial<ComprehensiveDomainData> = {
				identification: {
					domain: 'invalid-domain', // Invalid format
					normalizedDomain: 'invalid-domain',
					rootDomain: 'invalid-domain',
					tld: '',
					isWildcard: false,
					aliases: [],
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
				performance: {
					coreWebVitals: {
						largestContentfulPaint: 15000, // Invalid LCP (too high)
						firstInputDelay: 100,
						cumulativeLayoutShift: 0.1,
						firstContentfulPaint: 1500,
						timeToInteractive: 3000,
						totalBlockingTime: 200,
					},
					loadTimes: {
						domContentLoaded: 1000,
						fullyLoaded: 3000,
						firstByte: 500,
						startRender: 1200,
						speedIndex: 2000,
						visualComplete: 2800,
					},
					resources: {
						totalRequests: 50,
						totalSize: 1000000,
						htmlSize: 50000,
						cssSize: 100000,
						jsSize: 200000,
						imageSize: 500000,
						fontSize: 50000,
						otherSize: 100000,
						compressionRatio: 0.7,
					},
					network: {
						rtt: 50,
						downlink: 10,
						saveData: false,
					},
					mobile: {
						score: 85,
						usability: 90,
						loadTime: 3000,
						interactivity: 80,
					},
					budgetCompliance: {
						overall: true,
						javascript: true,
						css: true,
						images: false,
						fonts: true,
					},
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.CRITICAL,
						phase: CollectionPhase.PHASE_2_MODERATE,
					},
				},
			};

			const result = tracker.validateDomainData(invalidData);

			expect(result.valid).toBe(false);
			expect(result.errors.length).toBeGreaterThan(0);
		});
	});

	describe('getPriorityMissingFields', () =>
	{
		it('should identify critical missing fields', () =>
		{
			const partialData: Partial<ComprehensiveDomainData> = {
				// Missing identification and performance (both critical)
				dns: {
					a: ['***********'],
					aaaa: [],
					mx: [],
					cname: [],
					txt: [],
					ns: [],
					srv: [],
					caa: [],
					dnsSecEnabled: false,
					responseTime: 0,
					authoritative: false,
					_metadata: {
						availability: DataAvailability.AVAILABLE,
						priority: DataPriority.HIGH,
						phase: CollectionPhase.PHASE_1_BASIC,
					},
				},
			};

			const priorityFields = tracker.getPriorityMissingFields(partialData);

			expect(priorityFields.critical.length).toBeGreaterThan(0);
			expect(priorityFields.critical).toContain('identification');
		});

		it('should categorize missing fields by priority', () =>
		{
			const emptyData = tracker.createEmptyDomainData('example.com');
			const priorityFields = tracker.getPriorityMissingFields(emptyData);

			expect(priorityFields.critical.length).toBeGreaterThan(0);
			expect(priorityFields.high.length).toBeGreaterThan(0);
			expect(priorityFields.medium.length).toBeGreaterThan(0);
		});
	});
});
