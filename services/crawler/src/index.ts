import {
	<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>fig, Constants, DomainDescriptionValidator,
} from '@shared';
import RobotsAnalyzer, { RobotsAnalysisResult } from './analyzers/RobotsAnalyzer';
import DNSAnalyzer, { DNSAnalysisResult } from './analyzers/DNSAnalyzer';
import SSLAnalyzer, { SSLAnalysisResult } from './analyzers/SSLAnalyzer';
import HomepageAnalyzer, { HomepageAnalysisResult } from './analyzers/HomepageAnalyzer';
import FaviconCollector, { FaviconCollectionResult } from './analyzers/FaviconCollector';
import DomainInfoAnalyzer, { DomainInfoAnalysisResult } from './analyzers/DomainInfoAnalyzer';
import ScreenshotAnalyzer, { ScreenshotResult } from './analyzers/ScreenshotAnalyzer';
import PerformanceAuditor, { PerformanceAuditResult } from './analyzers/PerformanceAuditor';
import AdvancedContentAnalyzer, { AdvancedContentAnalysisResult } from './analyzers/AdvancedContentAnalyzer';
import { ModuleRegistry, ModuleExecutionResult } from './core/ModuleRegistry';
import { DataCollectionOrchestrator, DataCollectionRequest, DataCollectionResponse } from './core/DataCollectionOrchestrator';
import {
	SelectiveDataCollectionService, CollectionProfile, BatchCollectionRequest, BatchCollectionResult,
} from './core/SelectiveDataCollectionService';
import RobotsModule from './modules/RobotsModule';
import DNSModule from './modules/DNSModule';
import SSLModule from './modules/SSLModule';
import DomainInfoModule from './modules/DomainInfoModule';
import HomepageModule from './modules/HomepageModule';
import FaviconModule from './modules/FaviconModule';
import ScreenshotModule from './modules/ScreenshotModule';
import PerformanceModule from './modules/PerformanceModule';
import AdvancedContentModule from './modules/AdvancedContentModule';

const logger = Logger.getLogger('Crawler');
const config = Config;

interface CrawlJobData
{
	domain: string;
	crawlType: string;
	priority: string;
}

interface AnalysisResult
{
	domain: string;
	crawlType: string;
	timestamp: string;
	status: string;
	data: {
		basicInfo: {
			domain: string;
			accessible: boolean;
			responseTime: number;
		};
		robotsAnalysis?: RobotsAnalysisResult;
		dnsAnalysis?: DNSAnalysisResult;
		sslAnalysis?: SSLAnalysisResult;
		homepageAnalysis?: HomepageAnalysisResult;
		faviconCollection?: FaviconCollectionResult;
		domainInfo?: DomainInfoAnalysisResult;
		screenshotResult?: ScreenshotResult;
		performanceAudit?: PerformanceAuditResult;
	};
}

interface HealthCheck
{
	service: string;
	status: string;
	databases: any;
	timestamp: string;
}

/**
 * Crawler Service
 * Handles domain crawling, analysis, and data collection using modular architecture
 */
class CrawlerService
{
	private dbManager: DatabaseManager;

	private jobQueue: JobQueue;

	private moduleRegistry: ModuleRegistry;

	private dataCollectionOrchestrator: DataCollectionOrchestrator;

	private selectiveDataService: SelectiveDataCollectionService;

	private robotsAnalyzer: RobotsAnalyzer;

	private dnsAnalyzer: DNSAnalyzer;

	private sslAnalyzer: SSLAnalyzer;

	private homepageAnalyzer: HomepageAnalyzer;

	private faviconCollector: FaviconCollector;

	private domainInfoAnalyzer: DomainInfoAnalyzer;

	private isRunning: boolean;

	constructor()
	{
		this.dbManager = new DatabaseManager();
		this.jobQueue = new JobQueue();
		this.moduleRegistry = new ModuleRegistry();
		this.robotsAnalyzer = new RobotsAnalyzer();
		this.dnsAnalyzer = new DNSAnalyzer();
		this.sslAnalyzer = new SSLAnalyzer();
		this.homepageAnalyzer = new HomepageAnalyzer();
		this.faviconCollector = new FaviconCollector();
		this.domainInfoAnalyzer = new DomainInfoAnalyzer();
		this.isRunning = false;

		// Initialize after moduleRegistry is set up
		this.dataCollectionOrchestrator = new DataCollectionOrchestrator(this.moduleRegistry);
		this.selectiveDataService = new SelectiveDataCollectionService(this.moduleRegistry);
	}

	/**
	 * Initialize the crawler service
	 */
	async initialize(): Promise<void>
	{
		try
		{
			logger.info('Initializing Crawler Service...');

			// Validate configuration
			config.validate();

			// Initialize database connections
			await this.dbManager.initialize();

			// Initialize job queue
			await this.jobQueue.initialize();

			// Initialize module registry
			await this.initializeModuleRegistry();

			// Setup job consumers
			await this.setupJobConsumers();

			logger.info('Crawler Service initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Crawler Service:', error);
			throw error;
		}
	}

	/**
	 * Initialize module registry with all data collection modules
	 */
	private async initializeModuleRegistry(): Promise<void>
	{
		try
		{
			// Register all data collection modules
			this.moduleRegistry.registerModule('robots', new RobotsModule());
			this.moduleRegistry.registerModule('dns', new DNSModule());
			this.moduleRegistry.registerModule('ssl', new SSLModule());
			this.moduleRegistry.registerModule('domainInfo', new DomainInfoModule());
			this.moduleRegistry.registerModule('homepage', new HomepageModule());
			this.moduleRegistry.registerModule('favicon', new FaviconModule());
			this.moduleRegistry.registerModule('screenshot', new ScreenshotModule());
			this.moduleRegistry.registerModule('performance', new PerformanceModule());
			this.moduleRegistry.registerModule('advanced-content', new AdvancedContentModule());

			// Validate module configuration
			const validation = this.moduleRegistry.validateConfiguration();
			if (!validation.valid)
			{
				throw new Error(`Module configuration validation failed: ${validation.errors.join(', ')}`);
			}

			logger.info('Module registry initialized successfully', {
				modules: this.moduleRegistry.getModuleNames(),
				stats: this.moduleRegistry.getModuleStats(),
			});
		}
		catch (error)
		{
			logger.error('Failed to initialize module registry:', error);
			throw error;
		}
	}

	/**
	 * Setup job queue consumers
	 */
	async setupJobConsumers(): Promise<void>
	{
		// Domain crawl job consumer
		await this.jobQueue.createConsumer(
			Constants.JOB_QUEUES.DOMAIN_CRAWL,
			this.handleDomainCrawlJob.bind(this),
			{ concurrency: config.get('CRAWL_CONCURRENT_REQUESTS', 5) },
		);

		logger.info('Job consumers set up successfully');
	}

	/**
	 * Handle domain crawl job
	 */
	async handleDomainCrawlJob(jobData: CrawlJobData, message: any): Promise<void>
	{
		const { domain, crawlType } = jobData;

		logger.info(`Processing crawl job for domain: ${domain}, type: ${crawlType}`);

		try
		{
			// Placeholder for actual crawling logic
			const analysisResult = await this.crawlDomain(domain, crawlType);

			// Optional: validate DomainDescription shape before persisting
			if (process.env.VALIDATE_DESCRIPTIONS === '1')
			{
				const { buildDomainDescription } = await import('./utils/DomainDescriptionBuilder');
				const validator = DomainDescriptionValidator.get();
				const desc = buildDomainDescription(analysisResult);
				validator.assert(desc);
			}

			// Store results in database
			await this.storeAnalysisResult(analysisResult);

			// Trigger ranking update
			await this.jobQueue.publishRankingUpdateJob(domain);

			logger.info(`Crawl job completed for domain: ${domain}`);
		}
		catch (error)
		{
			logger.error(`Crawl job failed for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Crawl domain and collect data using modular architecture
	 */
	async crawlDomain(domain: string, crawlType: string): Promise<AnalysisResult>
	{
		logger.info(`Starting ${crawlType} crawl for domain: ${domain}`);
		const startTime = Date.now();

		const analysisResult: AnalysisResult = {
			domain,
			crawlType,
			timestamp: new Date().toISOString(),
			status: 'in_progress',
			data: {
				basicInfo: {
					domain,
					accessible: false,
					responseTime: 0,
				},
			},
		};

		try
		{
			// Determine which data collection modules to run based on crawl type
			const modules = this.getDataCollectionModules(crawlType);

			// Run data collection modules in parallel for efficiency
			const results = await this.runDataCollectionModules(domain, modules);

			// Merge results into analysis result
			Object.assign(analysisResult.data, results);

			// Update basic info based on analysis results
			const hasValidDNS = results.dnsAnalysis?.records.A.length > 0 || results.dnsAnalysis?.records.AAAA.length > 0;
			const isAccessible = results.robotsAnalysis?.accessible ||
				results.robotsAnalysis?.exists ||
				results.homepageAnalysis?.accessible ||
				hasValidDNS;

			analysisResult.data.basicInfo.accessible = isAccessible;
			analysisResult.data.basicInfo.responseTime = Date.now() - startTime;

			// Validate data completeness
			const completeness = this.validateDataCompleteness(analysisResult.data, modules);
			logger.info(`Data completeness for ${domain}: ${completeness.percentage}%`, completeness.missing);

			// Set status based on analysis success
			analysisResult.status = 'completed';

			logger.info(`Crawl completed successfully for domain: ${domain}`);
			return analysisResult;
		}
		catch (error)
		{
			logger.error(`Crawl failed for domain: ${domain}:`, error);

			analysisResult.status = 'failed';
			analysisResult.data.basicInfo.responseTime = Date.now() - startTime;

			return analysisResult;
		}
	}

	/**
	 * Get data collection modules based on crawl type
	 */
	private getDataCollectionModules(crawlType: string): string[]
	{
		const moduleMap: Record<string, string[]> = {
			basic: ['robots', 'dns'],
			standard: ['robots', 'dns', 'ssl', 'domainInfo'],
			full: ['robots', 'dns', 'ssl', 'domainInfo', 'homepage', 'favicon', 'screenshot'],
			priority: ['robots', 'dns', 'ssl', 'domainInfo', 'homepage'],
			light: ['robots', 'dns'],
			visual: ['robots', 'dns', 'ssl', 'homepage', 'screenshot'], // New crawl type for visual analysis
			advanced: ['robots', 'dns', 'ssl', 'domainInfo', 'homepage', 'favicon', 'screenshot'], // Advanced crawl with screenshots
		};

		return moduleMap[crawlType] || moduleMap.standard;
	}

	/**
	 * Run data collection modules using the new modular architecture
	 */
	private async runDataCollectionModules(domain: string, modules: string[]): Promise<Partial<AnalysisResult['data']>>
	{
		try
		{
			// Use selective data collection service for intelligent collection
			const profileName = this.mapCrawlTypeToProfile(modules);
			const collectionResponse = await this.selectiveDataService.collectDomain(domain, profileName);

			// Convert selective collection response to analysis result format
			const results: Partial<AnalysisResult['data']> = {};

			// Map module results to analysis result structure
			const executionResult = collectionResponse.execution;

			if (executionResult.results.robots?.success)
			{
				results.robotsAnalysis = executionResult.results.robots.data;
			}

			if (executionResult.results.dns?.success)
			{
				results.dnsAnalysis = executionResult.results.dns.data;
			}

			if (executionResult.results.ssl?.success)
			{
				results.sslAnalysis = executionResult.results.ssl.data;
			}

			if (executionResult.results.domainInfo?.success)
			{
				results.domainInfo = executionResult.results.domainInfo.data;
			}

			if (executionResult.results.homepage?.success)
			{
				results.homepageAnalysis = executionResult.results.homepage.data;
			}

			if (executionResult.results.favicon?.success)
			{
				results.faviconCollection = executionResult.results.favicon.data;
			}

			if (executionResult.results.screenshot?.success)
			{
				results.screenshotResult = executionResult.results.screenshot.data;
			}

			// Log enhanced execution statistics from selective collection
			logger.info(`Selective data collection completed for ${domain}`, {
				totalTime: collectionResponse.metadata.totalDuration,
				successCount: executionResult.successCount,
				failureCount: executionResult.failureCount,
				completeness: collectionResponse.validation.completenessScore,
				missingCritical: collectionResponse.validation.missingCritical,
				recommendations: collectionResponse.recommendations.length,
				resourceUsage: collectionResponse.metadata.resourceUsage,
			});

			// Log validation issues and recommendations
			if (collectionResponse.validation.missingCritical.length > 0)
			{
				logger.warn(`Critical data missing for ${domain}:`, collectionResponse.validation.missingCritical);
			}

			if (collectionResponse.recommendations.length > 0)
			{
				logger.info(`Collection recommendations for ${domain}:`,
					collectionResponse.recommendations.map(r => `${r.type}: ${r.reason}`));
			}

			// Log any failed modules with enhanced error information
			Object.entries(executionResult.results).forEach(([moduleName, result]) =>
			{
				if (!result.success)
				{
					logger.warn(`Module ${moduleName} failed for ${domain}: ${result.error}`);
				}
			});

			return results;
		}
		catch (error)
		{
			logger.error(`Selective data collection failed for ${domain}:`, error);

			// Fallback to direct module registry execution
			return await this.runDataCollectionModulesRegistry(domain, modules);
		}
	}

	/**
	 * Map crawl type modules to collection profile
	 */
	private mapCrawlTypeToProfile(modules: string[]): string
	{
		const moduleSet = new Set(modules);

		// Quick profile for basic modules
		if (moduleSet.size <= 2 && moduleSet.has('dns') && moduleSet.has('robots'))
		{
			return 'quick';
		}

		// Security profile for security-focused crawls
		if (moduleSet.has('ssl') && moduleSet.has('robots') && !moduleSet.has('favicon'))
		{
			return 'security';
		}

		// Performance profile for performance-focused crawls
		if (moduleSet.has('homepage') && moduleSet.has('dns') && moduleSet.size <= 3)
		{
			return 'performance';
		}

		// Complete profile for full crawls
		if (moduleSet.size >= 5)
		{
			return 'complete';
		}

		// Default to standard profile
		return 'standard';
	}

	/**
	 * Run data collection modules using direct module registry (fallback)
	 */
	private async runDataCollectionModulesRegistry(domain: string, modules: string[]): Promise<Partial<AnalysisResult['data']>>
	{
		try
		{
			// Execute modules using the module registry directly
			const executionResult = await this.moduleRegistry.executeModules(domain, modules);

			// Convert module execution results to analysis result format
			const results: Partial<AnalysisResult['data']> = {};

			// Map module results to analysis result structure
			if (executionResult.results.robots?.success)
			{
				results.robotsAnalysis = executionResult.results.robots.data;
			}

			if (executionResult.results.dns?.success)
			{
				results.dnsAnalysis = executionResult.results.dns.data;
			}

			if (executionResult.results.ssl?.success)
			{
				results.sslAnalysis = executionResult.results.ssl.data;
			}

			if (executionResult.results.domainInfo?.success)
			{
				results.domainInfo = executionResult.results.domainInfo.data;
			}

			if (executionResult.results.homepage?.success)
			{
				results.homepageAnalysis = executionResult.results.homepage.data;
			}

			if (executionResult.results.favicon?.success)
			{
				results.faviconCollection = executionResult.results.favicon.data;
			}

			if (executionResult.results.screenshot?.success)
			{
				results.screenshotResult = executionResult.results.screenshot.data;
			}

			// Log execution statistics
			logger.info(`Module registry execution completed for ${domain}`, {
				totalTime: executionResult.totalExecutionTime,
				successCount: executionResult.successCount,
				failureCount: executionResult.failureCount,
				completeness: executionResult.completeness.percentage,
			});

			// Log any failed modules
			Object.entries(executionResult.results).forEach(([moduleName, result]) =>
			{
				if (!result.success)
				{
					logger.warn(`Module ${moduleName} failed for ${domain}: ${result.error}`);
				}
			});

			return results;
		}
		catch (error)
		{
			logger.error(`Module registry execution failed for ${domain}:`, error);

			// Final fallback to legacy method
			return await this.runDataCollectionModulesLegacy(domain, modules);
		}
	}

	/**
	 * Legacy data collection method (fallback)
	 */
	private async runDataCollectionModulesLegacy(domain: string, modules: string[]): Promise<Partial<AnalysisResult['data']>>
	{
		const results: Partial<AnalysisResult['data']> = {};

		// Create promises for each module
		const modulePromises: Promise<void>[] = [];

		if (modules.includes('robots'))
		{
			modulePromises.push(
				this.robotsAnalyzer.analyzeRobotsTxt(domain)
					.then((result) => { results.robotsAnalysis = result })
					.catch(error => logger.warn(`Robots analysis failed for ${domain}:`, error)),
			);
		}

		if (modules.includes('dns'))
		{
			modulePromises.push(
				this.dnsAnalyzer.analyzeDNSRecords(domain)
					.then((result) => { results.dnsAnalysis = result })
					.catch(error => logger.warn(`DNS analysis failed for ${domain}:`, error)),
			);
		}

		if (modules.includes('ssl'))
		{
			modulePromises.push(
				this.sslAnalyzer.analyzeSSL(domain)
					.then((result) => { results.sslAnalysis = result })
					.catch(error => logger.warn(`SSL analysis failed for ${domain}:`, error)),
			);
		}

		if (modules.includes('domainInfo'))
		{
			modulePromises.push(
				this.domainInfoAnalyzer.analyzeDomainInfo(domain)
					.then((result) => { results.domainInfo = result })
					.catch(error => logger.warn(`Domain info analysis failed for ${domain}:`, error)),
			);
		}

		if (modules.includes('homepage'))
		{
			modulePromises.push(
				this.homepageAnalyzer.analyzeHomepage(domain)
					.then((result) => { results.homepageAnalysis = result })
					.catch(error => logger.warn(`Homepage analysis failed for ${domain}:`, error)),
			);
		}

		if (modules.includes('favicon'))
		{
			modulePromises.push(
				this.faviconCollector.collectFavicon(domain)
					.then((result) => { results.faviconCollection = result })
					.catch(error => logger.warn(`Favicon collection failed for ${domain}:`, error)),
			);
		}

		if (modules.includes('screenshot'))
		{
			const screenshotAnalyzer = new ScreenshotAnalyzer();
			modulePromises.push(
				screenshotAnalyzer.captureScreenshots(domain)
					.then((result) => { results.screenshotResult = result })
					.catch(error => logger.warn(`Screenshot capture failed for ${domain}:`, error)),
			);
		}

		// Wait for all modules to complete
		await Promise.allSettled(modulePromises);

		return results;
	}

	/**
	 * Validate data completeness and identify missing data
	 */
	private validateDataCompleteness(data: AnalysisResult['data'], expectedModules: string[]): {
		percentage: number;
		missing: string[];
		available: string[];
	}
	{
		const available: string[] = [];
		const missing: string[] = [];

		const moduleDataMap = {
			robots: data.robotsAnalysis,
			dns: data.dnsAnalysis,
			ssl: data.sslAnalysis,
			domainInfo: data.domainInfo,
			homepage: data.homepageAnalysis,
			favicon: data.faviconCollection,
			screenshot: data.screenshotResult,
		};

		expectedModules.forEach((module) =>
		{
			if (moduleDataMap[module as keyof typeof moduleDataMap])
			{
				available.push(module);
			}
			else
			{
				missing.push(module);
			}
		});

		const percentage = Math.round((available.length / expectedModules.length) * 100);

		return { percentage, missing, available };
	}

	/**
	 * Store analysis result in database
	 */
	async storeAnalysisResult(result: AnalysisResult): Promise<void>
	{
		try
		{
			logger.info(`Storing analysis result for domain: ${result.domain}`);

			const scylla = this.dbManager.getScyllaClient();

			// Store robots.txt analysis if available
			if (result.data.robotsAnalysis)
			{
				await this.storeRobotsAnalysis(result.data.robotsAnalysis);
			}

			// Store DNS analysis if available
			if (result.data.dnsAnalysis)
			{
				await this.storeDNSAnalysis(result.data.dnsAnalysis);
			}

			// Store SSL analysis if available
			if (result.data.sslAnalysis)
			{
				await this.storeSSLAnalysis(result.data.sslAnalysis);
			}

			// Store homepage analysis if available
			if (result.data.homepageAnalysis)
			{
				await this.storeHomepageAnalysis(result.data.homepageAnalysis);
			}

			// Store favicon collection if available
			if (result.data.faviconCollection)
			{
				await this.storeFaviconCollection(result.data.faviconCollection);
			}

			// Store domain info if available
			if (result.data.domainInfo)
			{
				await this.storeDomainInfo(result.data.domainInfo);
			}

			// Store screenshot result if available
			if (result.data.screenshotResult)
			{
				await this.storeScreenshotResult(result.data.screenshotResult);
			}

			// Store performance audit if available
			if (result.data.performanceAudit)
			{
				await this.storePerformanceAudit(result.data.performanceAudit);
			}

			// Store basic domain analysis data
			const insertQuery = `
        INSERT INTO domain_analysis (
          domain,
          last_crawled,
          crawl_status,
          crawl_duration_ms,
          seo_metrics
        ) VALUES (?, ?, ?, ?, ?)
      `;

			const seoMetrics = {
				...(result.data.robotsAnalysis ? {
					robots_txt_exists: result.data.robotsAnalysis.exists,
					robots_txt_accessible: result.data.robotsAnalysis.accessible,
					robots_txt_compliant: result.data.robotsAnalysis.complianceStatus.isCompliant,
					sitemap_urls: result.data.robotsAnalysis.sitemaps,
				} : {}),
				...(result.data.dnsAnalysis ? {
					has_email_config: this.dnsAnalyzer.hasEmailConfiguration(result.data.dnsAnalysis),
					supports_ipv6: this.dnsAnalyzer.supportsIPv6(result.data.dnsAnalysis),
					dns_provider: result.data.dnsAnalysis.dnsProvider,
					has_cdn: result.data.dnsAnalysis.hasCDN,
				} : {}),
			};

			await scylla.execute(insertQuery, [
				result.domain,
				new Date(),
				result.status,
				result.data.basicInfo.responseTime,
				seoMetrics,
			]);

			logger.info(`Successfully stored analysis result for domain: ${result.domain}`);
		}
		catch (error)
		{
			logger.error('Failed to store analysis result:', error);
			throw error;
		}
	}

	/**
	 * Store robots.txt analysis in database
	 */
	private async storeRobotsAnalysis(robotsAnalysis: RobotsAnalysisResult): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			// Store robots.txt analysis in a dedicated table
			const insertRobotsQuery = `
        INSERT INTO robots_analysis (
          domain,
          robots_txt_url,
          exists,
          accessible,
          content,
          rules,
          sitemaps,
          crawl_delay,
          is_compliant,
          violations,
          warnings,
          last_analyzed,
          error_message
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

			await scylla.execute(insertRobotsQuery, [
				robotsAnalysis.domain,
				robotsAnalysis.robotsTxtUrl,
				robotsAnalysis.exists,
				robotsAnalysis.accessible,
				robotsAnalysis.content,
				JSON.stringify(robotsAnalysis.rules),
				robotsAnalysis.sitemaps,
				robotsAnalysis.crawlDelay,
				robotsAnalysis.complianceStatus.isCompliant,
				robotsAnalysis.complianceStatus.violations,
				robotsAnalysis.complianceStatus.warnings,
				robotsAnalysis.lastAnalyzed,
				robotsAnalysis.error || null,
			]);

			logger.info(`Successfully stored robots.txt analysis for domain: ${robotsAnalysis.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store robots.txt analysis for domain: ${robotsAnalysis.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store DNS analysis in database
	 */
	private async storeDNSAnalysis(dnsAnalysis: DNSAnalysisResult): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			// Update domain_analysis table with DNS information
			const updateDomainQuery = `
        UPDATE domain_analysis
        SET
          dns_records = ?,
          server_info = ?,
          technical_metrics = ?
        WHERE domain = ?
      `;

			// Prepare DNS records for storage
			const dnsRecords = this.dnsAnalyzer.getDNSRecordSummary(dnsAnalysis);

			// Prepare server info
			const serverInfo = {
				primary_ip: this.dnsAnalyzer.getPrimaryIP(dnsAnalysis),
				ipv4_addresses: dnsAnalysis.ipAddresses.ipv4.join(','),
				ipv6_addresses: dnsAnalysis.ipAddresses.ipv6.join(','),
				supports_ipv6: this.dnsAnalyzer.supportsIPv6(dnsAnalysis).toString(),
				has_email: this.dnsAnalyzer.hasEmailConfiguration(dnsAnalysis).toString(),
				dns_provider: dnsAnalysis.dnsProvider || 'unknown',
				has_cloudflare: dnsAnalysis.hasCloudflare.toString(),
				has_cdn: dnsAnalysis.hasCDN.toString(),
			};

			// Prepare technical metrics
			const technicalMetrics = {
				dns_analysis_time: dnsAnalysis.analysisTime.toString(),
				dns_errors: dnsAnalysis.errors.length.toString(),
				name_servers_count: dnsAnalysis.nameServers.length.toString(),
				mail_servers_count: dnsAnalysis.mailServers.length.toString(),
				txt_records_count: dnsAnalysis.txtRecords.length.toString(),
			};

			await scylla.execute(updateDomainQuery, [
				dnsRecords,
				serverInfo,
				technicalMetrics,
				dnsAnalysis.domain,
			]);

			// Store detailed DNS analysis in a separate table if it doesn't exist
			const insertDNSQuery = `
        INSERT INTO dns_analysis (
          domain,
          a_records,
          aaaa_records,
          mx_records,
          cname_records,
          txt_records,
          ns_records,
          soa_record,
          primary_ip,
          supports_ipv6,
          has_email_config,
          has_cloudflare,
          has_cdn,
          dns_provider,
          analysis_time_ms,
          errors,
          last_analyzed
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

			await scylla.execute(insertDNSQuery, [
				dnsAnalysis.domain,
				dnsAnalysis.records.A,
				dnsAnalysis.records.AAAA,
				dnsAnalysis.records.MX.map(mx => `${mx.priority} ${mx.exchange}`),
				dnsAnalysis.records.CNAME,
				dnsAnalysis.records.TXT,
				dnsAnalysis.records.NS,
				dnsAnalysis.records.SOA ? JSON.stringify(dnsAnalysis.records.SOA) : null,
				this.dnsAnalyzer.getPrimaryIP(dnsAnalysis),
				this.dnsAnalyzer.supportsIPv6(dnsAnalysis),
				this.dnsAnalyzer.hasEmailConfiguration(dnsAnalysis),
				dnsAnalysis.hasCloudflare,
				dnsAnalysis.hasCDN,
				dnsAnalysis.dnsProvider,
				dnsAnalysis.analysisTime,
				dnsAnalysis.errors,
				dnsAnalysis.lastAnalyzed,
			]);

			logger.info(`Successfully stored DNS analysis for domain: ${dnsAnalysis.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store DNS analysis for domain: ${dnsAnalysis.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store SSL analysis in database
	 */
	private async storeSSLAnalysis(sslAnalysis: SSLAnalysisResult): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const insertSSLQuery = `
        INSERT INTO ssl_analysis (
          domain,
          has_ssl,
          ssl_grade,
          certificate_issuer,
          certificate_subject,
          certificate_valid_from,
          certificate_valid_to,
          days_until_expiry,
          certificate_key_size,
          signature_algorithm,
          protocols,
          cipher_suites,
          vulnerabilities,
          has_hsts,
          hsts_max_age,
          hsts_include_subdomains,
          last_analyzed,
          error_message
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

			await scylla.execute(insertSSLQuery, [
				sslAnalysis.domain,
				sslAnalysis.hasSSL,
				sslAnalysis.grade,
				sslAnalysis.certificate?.issuer || null,
				sslAnalysis.certificate?.subject || null,
				sslAnalysis.certificate?.validFrom || null,
				sslAnalysis.certificate?.validTo || null,
				sslAnalysis.certificate?.daysUntilExpiry || null,
				sslAnalysis.certificate?.keySize || null,
				sslAnalysis.certificate?.signatureAlgorithm || null,
				sslAnalysis.protocols,
				sslAnalysis.cipherSuites,
				sslAnalysis.vulnerabilities,
				sslAnalysis.securityHeaders.hsts,
				sslAnalysis.securityHeaders.hstsMaxAge || null,
				sslAnalysis.securityHeaders.hstsIncludeSubdomains || null,
				sslAnalysis.lastAnalyzed,
				sslAnalysis.error || null,
			]);

			logger.info(`Successfully stored SSL analysis for domain: ${sslAnalysis.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store SSL analysis for domain: ${sslAnalysis.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store homepage analysis in database
	 */
	private async storeHomepageAnalysis(homepageAnalysis: HomepageAnalysisResult): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const insertHomepageQuery = `
        INSERT INTO homepage_analysis (
          domain,
          url,
          accessible,
          status_code,
          response_time,
          redirects,
          final_url,
          content_type,
          content_length,
          meta_title,
          meta_description,
          meta_keywords,
          meta_author,
          meta_viewport,
          meta_charset,
          meta_robots,
          canonical_url,
          og_title,
          og_description,
          og_image,
          twitter_card,
          detected_server,
          detected_framework,
          detected_cms,
          analytics_tools,
          libraries,
          load_time,
          ttfb,
          has_h1,
          has_h2,
          image_count,
          link_count,
          word_count,
          content_language,
          last_analyzed,
          error_message
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

			await scylla.execute(insertHomepageQuery, [
				homepageAnalysis.domain,
				homepageAnalysis.url,
				homepageAnalysis.accessible,
				homepageAnalysis.statusCode || null,
				homepageAnalysis.responseTime,
				homepageAnalysis.redirects,
				homepageAnalysis.finalUrl,
				homepageAnalysis.contentType || null,
				homepageAnalysis.contentLength || null,
				homepageAnalysis.metaTags.title || null,
				homepageAnalysis.metaTags.description || null,
				homepageAnalysis.metaTags.keywords || null,
				homepageAnalysis.metaTags.author || null,
				homepageAnalysis.metaTags.viewport || null,
				homepageAnalysis.metaTags.charset || null,
				homepageAnalysis.metaTags.robots || null,
				homepageAnalysis.metaTags.canonical || null,
				homepageAnalysis.metaTags.ogTitle || null,
				homepageAnalysis.metaTags.ogDescription || null,
				homepageAnalysis.metaTags.ogImage || null,
				homepageAnalysis.metaTags.twitterCard || null,
				homepageAnalysis.technologies.server || null,
				homepageAnalysis.technologies.framework || null,
				homepageAnalysis.technologies.cms || null,
				homepageAnalysis.technologies.analytics,
				homepageAnalysis.technologies.libraries,
				homepageAnalysis.performance.loadTime,
				homepageAnalysis.performance.ttfb,
				homepageAnalysis.content.hasH1,
				homepageAnalysis.content.hasH2,
				homepageAnalysis.content.imageCount,
				homepageAnalysis.content.linkCount,
				homepageAnalysis.content.wordCount,
				homepageAnalysis.content.language || null,
				homepageAnalysis.lastAnalyzed,
				homepageAnalysis.error || null,
			]);

			logger.info(`Successfully stored homepage analysis for domain: ${homepageAnalysis.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store homepage analysis for domain: ${homepageAnalysis.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store screenshot result in database
	 */
	private async storeScreenshotResult(screenshotResult: ScreenshotResult): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			// Update domain_analysis table with screenshot URLs
			const updateDomainQuery = `
				UPDATE domain_analysis
				SET screenshot_urls = ?
				WHERE domain = ?
			`;

			const screenshotUrls = [];
			if (screenshotResult.screenshots.desktop.url)
			{
				screenshotUrls.push(`desktop:${screenshotResult.screenshots.desktop.url}`);
			}
			if (screenshotResult.screenshots.mobile.url)
			{
				screenshotUrls.push(`mobile:${screenshotResult.screenshots.mobile.url}`);
			}

			await scylla.execute(updateDomainQuery, [
				screenshotUrls,
				screenshotResult.domain,
			]);

			// Store detailed screenshot metadata in dedicated table
			const insertScreenshotQuery = `
				INSERT INTO domain_screenshots (
					domain,
					capture_date,
					desktop_url,
					desktop_optimized_url,
					desktop_width,
					desktop_height,
					desktop_format,
					desktop_size,
					desktop_error,
					mobile_url,
					mobile_optimized_url,
					mobile_width,
					mobile_height,
					mobile_format,
					mobile_size,
					mobile_error,
					capture_time_ms,
					last_captured
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`;

			await scylla.execute(insertScreenshotQuery, [
				screenshotResult.domain,
				new Date(),
				screenshotResult.screenshots.desktop.url || null,
				screenshotResult.screenshots.desktop.optimizedUrl || null,
				screenshotResult.screenshots.desktop.width,
				screenshotResult.screenshots.desktop.height,
				screenshotResult.screenshots.desktop.format,
				screenshotResult.screenshots.desktop.size || null,
				screenshotResult.screenshots.desktop.error || null,
				screenshotResult.screenshots.mobile.url || null,
				screenshotResult.screenshots.mobile.optimizedUrl || null,
				screenshotResult.screenshots.mobile.width,
				screenshotResult.screenshots.mobile.height,
				screenshotResult.screenshots.mobile.format,
				screenshotResult.screenshots.mobile.size || null,
				screenshotResult.screenshots.mobile.error || null,
				screenshotResult.captureTime,
				new Date(screenshotResult.lastCaptured),
			]);

			logger.info(`Successfully stored screenshot result for domain: ${screenshotResult.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store screenshot result for domain: ${screenshotResult.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store performance audit result in database
	 */
	private async storePerformanceAudit(performanceAudit: PerformanceAuditResult): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			// Update domain_analysis table with performance metrics
			const updateDomainQuery = `
				UPDATE domain_analysis
				SET
					performance_metrics = ?,
					content_metrics = content_metrics + ?,
					last_crawled = ?
				WHERE domain = ?
			`;

			// Prepare performance metrics map
			const performanceMetrics = new Map(Object.entries({
				load_time: performanceAudit.metrics.loadTime.toString(),
				fcp: performanceAudit.metrics.firstContentfulPaint.toString(),
				lcp: performanceAudit.metrics.largestContentfulPaint.toString(),
				cls: performanceAudit.metrics.cumulativeLayoutShift.toString(),
				fid: performanceAudit.metrics.firstInputDelay.toString(),
				speed_index: performanceAudit.metrics.speedIndex.toString(),
				score: performanceAudit.metrics.score.toString(),
				total_requests: performanceAudit.resourceAnalysis.totalRequests.toString(),
				total_size: performanceAudit.resourceAnalysis.totalSize.toString(),
				audit_time: performanceAudit.auditDetails.auditTime.toString(),
			}));

			// Prepare content metrics for resource analysis
			const contentMetrics = new Map(Object.entries({
				resource_count: performanceAudit.resourceAnalysis.totalRequests.toString(),
				page_size: performanceAudit.resourceAnalysis.totalSize.toString(),
				performance_score: performanceAudit.metrics.score.toString(),
				recommendations_count: performanceAudit.recommendations.length.toString(),
			}));

			await scylla.execute(updateDomainQuery, [
				performanceMetrics,
				contentMetrics,
				new Date(),
				performanceAudit.domain,
			]);

			// Store detailed performance audit in dedicated table
			const insertPerformanceQuery = `
				INSERT INTO domain_performance_audit (
					domain,
					audit_date,
					load_time,
					first_contentful_paint,
					largest_contentful_paint,
					cumulative_layout_shift,
					first_input_delay,
					speed_index,
					performance_score,
					total_requests,
					total_size,
					resource_types,
					largest_resources,
					connection_type,
					rtt,
					downlink,
					effective_type,
					viewport_width,
					viewport_height,
					user_agent,
					audit_time_ms,
					recommendations,
					last_audited,
					audit_error
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`;

			await scylla.execute(insertPerformanceQuery, [
				performanceAudit.domain,
				new Date(),
				performanceAudit.metrics.loadTime,
				performanceAudit.metrics.firstContentfulPaint,
				performanceAudit.metrics.largestContentfulPaint,
				performanceAudit.metrics.cumulativeLayoutShift,
				performanceAudit.metrics.firstInputDelay,
				performanceAudit.metrics.speedIndex,
				performanceAudit.metrics.score,
				performanceAudit.resourceAnalysis.totalRequests,
				performanceAudit.resourceAnalysis.totalSize,
				JSON.stringify(performanceAudit.resourceAnalysis.resourceTypes),
				JSON.stringify(performanceAudit.resourceAnalysis.largestResources.slice(0, 10)), // Top 10 only
				performanceAudit.networkAnalysis.connectionType,
				performanceAudit.networkAnalysis.rtt,
				performanceAudit.networkAnalysis.downlink,
				performanceAudit.networkAnalysis.effectiveType,
				performanceAudit.auditDetails.viewport.width,
				performanceAudit.auditDetails.viewport.height,
				performanceAudit.auditDetails.userAgent,
				performanceAudit.auditDetails.auditTime,
				performanceAudit.recommendations,
				new Date(performanceAudit.lastAudited),
				performanceAudit.error || null,
			]);

			logger.info(`Successfully stored performance audit for domain: ${performanceAudit.domain}`, {
				performanceScore: performanceAudit.metrics.score,
				loadTime: performanceAudit.metrics.loadTime,
				auditTime: performanceAudit.auditDetails.auditTime,
			});
		}
		catch (error)
		{
			logger.error(`Failed to store performance audit for domain: ${performanceAudit.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store favicon collection in database
	 */
	private async storeFaviconCollection(faviconCollection: FaviconCollectionResult): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const insertFaviconQuery = `
        INSERT INTO favicon_collection (
          domain,
          favicon_url,
          favicon_found,
          favicon_size,
          favicon_type,
          source_duckduckgo,
          source_direct,
          source_html,
          fallback_used,
          last_collected,
          error_message
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

			await scylla.execute(insertFaviconQuery, [
				faviconCollection.domain,
				faviconCollection.faviconUrl || null,
				faviconCollection.faviconFound,
				faviconCollection.faviconSize || null,
				faviconCollection.faviconType || null,
				faviconCollection.sources.duckduckgo,
				faviconCollection.sources.direct,
				faviconCollection.sources.html,
				faviconCollection.fallbackUsed || null,
				faviconCollection.lastCollected,
				faviconCollection.error || null,
			]);

			logger.info(`Successfully stored favicon collection for domain: ${faviconCollection.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store favicon collection for domain: ${faviconCollection.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store domain info in database
	 */
	private async storeDomainInfo(domainInfo: DomainInfoAnalysisResult): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const insertDomainInfoQuery = `
        INSERT INTO domain_info (
          domain,
          is_valid,
          is_tld,
          is_subdomain,
          normalized_domain,
          registrar,
          registration_date,
          expiration_date,
          last_updated,
          name_servers,
          domain_status,
          registrant_country,
          registrant_organization,
          admin_email,
          tech_email,
          days_until_expiry,
          domain_age,
          last_analyzed,
          error_message
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

			await scylla.execute(insertDomainInfoQuery, [
				domainInfo.domain,
				domainInfo.validation.isValid,
				domainInfo.validation.isTLD,
				domainInfo.validation.isSubdomain,
				domainInfo.validation.normalizedDomain,
				domainInfo.whoisData?.registrar || null,
				domainInfo.whoisData?.registrationDate || null,
				domainInfo.whoisData?.expirationDate || null,
				domainInfo.whoisData?.lastUpdated || null,
				domainInfo.whoisData?.nameServers || [],
				domainInfo.whoisData?.status || [],
				domainInfo.whoisData?.registrantCountry || null,
				domainInfo.whoisData?.registrantOrganization || null,
				domainInfo.whoisData?.adminEmail || null,
				domainInfo.whoisData?.techEmail || null,
				domainInfo.whoisData?.daysUntilExpiry || null,
				domainInfo.whoisData?.domainAge || null,
				domainInfo.lastAnalyzed,
				domainInfo.error || null,
			]);

			logger.info(`Successfully stored domain info for domain: ${domainInfo.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store domain info for domain: ${domainInfo.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Start the crawler service
	 */
	async start(): Promise<void>
	{
		try
		{
			this.isRunning = true;
			logger.info('Crawler Service started and listening for jobs');

			// Keep the service running
			while (this.isRunning)
			{
				await this.delay(1000);
			}
		}
		catch (error)
		{
			logger.error('Error in crawler service:', error);
			throw error;
		}
	}

	/**
	 * Stop the crawler service
	 */
	async stop(): Promise<void>
	{
		this.isRunning = false;
		logger.info('Crawler Service stopped');
	}

	/**
	 * Graceful shutdown
	 */
	async shutdown(): Promise<void>
	{
		try
		{
			logger.info('Shutting down Crawler Service...');
			await this.stop();
			await this.jobQueue.shutdown();
			await this.dbManager.close();
			logger.info('Crawler Service shut down successfully');
		}
		catch (error)
		{
			logger.error('Error during shutdown:', error);
			throw error;
		}
	}

	/**
	 * Collect data for a single domain using selective collection
	 */
	async collectDomainData(
		domain: string,
		profile: string = 'standard',
		options?: {
			priority?: 'low' | 'medium' | 'high' | 'critical';
			onlyIfMissing?: boolean;
			requiredFields?: string[];
		},
	): Promise<DataCollectionResponse>
	{
		logger.info(`Collecting data for domain: ${domain} using profile: ${profile}`);

		try
		{
			const collectionOptions = options ? {
				priority: options.priority,
				selective: {
					onlyIfMissing: options.onlyIfMissing || false,
					requiredFields: options.requiredFields || [],
					skipIfExists: [],
				},
			} : undefined;

			return await this.selectiveDataService.collectDomain(domain, profile, collectionOptions);
		}
		catch (error)
		{
			logger.error(`Failed to collect data for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Collect data for multiple domains in batch
	 */
	async collectBatchDomainData(
		domains: string[],
		profile: string = 'standard',
		options?: {
			concurrency?: number;
			priority?: 'low' | 'medium' | 'high' | 'critical';
			onProgress?: (completed: number, total: number, current: string) => void;
			onError?: (domain: string, error: Error) => void;
		},
	): Promise<BatchCollectionResult>
	{
		logger.info(`Starting batch collection for ${domains.length} domains using profile: ${profile}`);

		try
		{
			const batchRequest: BatchCollectionRequest = {
				domains,
				profile,
				concurrency: options?.concurrency || 5,
				priority: options?.priority,
				onProgress: options?.onProgress,
				onError: options?.onError,
			};

			return await this.selectiveDataService.collectBatch(batchRequest);
		}
		catch (error)
		{
			logger.error('Failed to collect batch data for domains:', error);
			throw error;
		}
	}

	/**
	 * Collect only missing data for a domain
	 */
	async collectMissingDomainData(
		domain: string,
		requiredModules: string[],
		options?: {
			priority?: 'low' | 'medium' | 'high' | 'critical';
			requiredFields?: string[];
		},
	): Promise<DataCollectionResponse>
	{
		logger.info(`Collecting missing data for domain: ${domain}`, {
			requiredModules,
			requiredFields: options?.requiredFields,
		});

		try
		{
			return await this.selectiveDataService.collectMissingData(domain, requiredModules, options);
		}
		catch (error)
		{
			logger.error(`Failed to collect missing data for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Refresh stale data for a domain
	 */
	async refreshDomainData(
		domain: string,
		maxAgeHours: number = 24,
		options?: {
			priority?: 'low' | 'medium' | 'high' | 'critical';
			modules?: string[];
		},
	): Promise<DataCollectionResponse>
	{
		logger.info(`Refreshing stale data for domain: ${domain}`, {
			maxAge: maxAgeHours,
			modules: options?.modules,
		});

		try
		{
			return await this.selectiveDataService.refreshStaleData(domain, maxAgeHours, options);
		}
		catch (error)
		{
			logger.error(`Failed to refresh data for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Analyze domain data completeness
	 */
	async analyzeDomainCompleteness(
		domain: string,
		requiredModules: string[],
	): Promise<{
		completeness: number;
		missing: string[];
		stale: string[];
		recommendations: string[];
	}>
	{
		logger.info(`Analyzing data completeness for domain: ${domain}`);

		try
		{
			return await this.selectiveDataService.analyzeDomainCompleteness(domain, requiredModules);
		}
		catch (error)
		{
			logger.error(`Failed to analyze completeness for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Get available collection profiles
	 */
	getCollectionProfiles(): CollectionProfile[]
	{
		return this.selectiveDataService.getProfiles();
	}

	/**
	 * Get collection statistics
	 */
	getCollectionStatistics(): {
		profileUsage: Record<string, number>;
		avgCompleteness: number;
		commonIssues: string[];
		performanceMetrics: {
			avgDuration: number;
			successRate: number;
			resourceEfficiency: number;
		};
		}
	{
		return this.selectiveDataService.getCollectionStats();
	}

	/**
	 * Get module registry statistics
	 */
	getModuleStatistics(): {
		modules: string[];
		stats: Record<string, any>;
		validation: { valid: boolean; errors: string[] };
		}
	{
		return {
			modules: this.moduleRegistry.getModuleNames(),
			stats: this.moduleRegistry.getModuleStats(),
			validation: this.moduleRegistry.validateConfiguration(),
		};
	}

	/**
	 * Health check
	 */
	async healthCheck(): Promise<HealthCheck>
	{
		const health: HealthCheck = {
			service: 'crawler',
			status: this.isRunning ? 'running' : 'stopped',
			databases: await this.dbManager.healthCheck(),
			timestamp: new Date().toISOString(),
		};

		return health;
	}

	/**
	 * Delay utility
	 */
	delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

// Initialize and start the service
async function main(): Promise<void>
{
	const crawler = new CrawlerService();

	try
	{
		await crawler.initialize();

		// Graceful shutdown handling
		process.on('SIGTERM', async () =>
		{
			await crawler.shutdown();
			process.exit(0);
		});

		process.on('SIGINT', async () =>
		{
			await crawler.shutdown();
			process.exit(0);
		});

		await crawler.start();
	}
	catch (error)
	{
		logger.error('Failed to start Crawler Service:', error);
		process.exit(1);
	}
}

// Start the service if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`)
{
	main();
}

export default CrawlerService;
