import {
	describe, it, expect, beforeEach,
} from 'vitest';
import { DataCollectionOrchestrator, DataCollectionRequest } from '../DataCollectionOrchestrator';
import { ModuleRegistry } from '../ModuleRegistry';
import { DataCollectionModule, DataCollectionModuleConfig } from '../DataCollectionModule';

// Mock module for testing
class MockDNSModule extends DataCollectionModule<any>
{
	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'dns',
			priority: 95,
			timeout: 5000,
			retryAttempts: 2,
			retryDelay: 1000,
			dependencies: [],
		};
		super(config);
	}

	protected async collect(domain: string): Promise<any>
	{
		return {
			domain,
			records: {
				A: ['192.168.1.1'],
				AAAA: [],
				MX: [],
				NS: ['ns1.example.com', 'ns2.example.com'],
			},
			lastAnalyzed: new Date().toISOString(),
		};
	}
}

class MockSSLModule extends DataCollectionModule<any>
{
	constructor()
	{
		const config: DataCollectionModuleConfig = {
			name: 'ssl',
			priority: 80,
			timeout: 8000,
			retryAttempts: 2,
			retryDelay: 1500,
			dependencies: ['dns'],
		};
		super(config);
	}

	protected async collect(domain: string): Promise<any>
	{
		return {
			domain,
			hasSSL: true,
			grade: 'A',
			certificate: {
				issuer: 'Let\'s Encrypt',
				validFrom: new Date().toISOString(),
				validTo: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
				daysUntilExpiry: 90,
			},
			lastAnalyzed: new Date().toISOString(),
		};
	}
}

describe('DataCollectionOrchestrator', () =>
{
	let orchestrator: DataCollectionOrchestrator;
	let moduleRegistry: ModuleRegistry;

	beforeEach(() =>
	{
		moduleRegistry = new ModuleRegistry();
		moduleRegistry.registerModule('dns', new MockDNSModule());
		moduleRegistry.registerModule('ssl', new MockSSLModule());
		orchestrator = new DataCollectionOrchestrator(moduleRegistry);
	});

	describe('collectData', () =>
	{
		it('should collect data for a single module', async () =>
		{
			const request: DataCollectionRequest = {
				domain: 'example.com',
				modules: ['dns'],
				priority: 'medium',
			};

			const response = await orchestrator.collectData(request);

			expect(response.domain).toBe('example.com');
			expect(response.execution.successCount).toBe(1);
			expect(response.execution.failureCount).toBe(0);
			expect(response.execution.results.dns).toBeDefined();
			expect(response.execution.results.dns.success).toBe(true);
			expect(response.validation.isComplete).toBe(true);
			expect(response.validation.completenessScore).toBeGreaterThan(0.8);
		});

		it('should collect data for multiple modules with dependencies', async () =>
		{
			const request: DataCollectionRequest = {
				domain: 'example.com',
				modules: ['dns', 'ssl'],
				priority: 'high',
			};

			const response = await orchestrator.collectData(request);

			expect(response.domain).toBe('example.com');
			expect(response.execution.successCount).toBe(2);
			expect(response.execution.failureCount).toBe(0);
			expect(response.execution.results.dns).toBeDefined();
			expect(response.execution.results.ssl).toBeDefined();
			expect(response.execution.results.dns.success).toBe(true);
			expect(response.execution.results.ssl.success).toBe(true);
			expect(response.validation.isComplete).toBe(true);
		});

		it('should handle selective data collection with onlyIfMissing', async () =>
		{
			const request: DataCollectionRequest = {
				domain: 'example.com',
				modules: ['dns'],
				priority: 'medium',
				selective: {
					onlyIfMissing: true,
					requiredFields: ['records.A'],
					skipIfExists: [],
				},
			};

			const response = await orchestrator.collectData(request);

			expect(response.domain).toBe('example.com');
			expect(response.validation.completenessScore).toBeGreaterThan(0);
			expect(response.recommendations).toBeDefined();
			expect(Array.isArray(response.recommendations)).toBe(true);
		});

		it('should provide data quality metrics', async () =>
		{
			const request: DataCollectionRequest = {
				domain: 'example.com',
				modules: ['dns', 'ssl'],
				priority: 'medium',
			};

			const response = await orchestrator.collectData(request);

			expect(response.validation.dataQuality).toBeDefined();
			expect(response.validation.dataQuality.dns).toBeDefined();
			expect(response.validation.dataQuality.ssl).toBeDefined();
			expect(response.validation.dataQuality.dns.score).toBeGreaterThan(0);
			expect(response.validation.dataQuality.ssl.score).toBeGreaterThan(0);
		});

		it('should generate recommendations based on collection results', async () =>
		{
			const request: DataCollectionRequest = {
				domain: 'example.com',
				modules: ['dns', 'ssl'],
				priority: 'critical',
			};

			const response = await orchestrator.collectData(request);

			expect(response.recommendations).toBeDefined();
			expect(Array.isArray(response.recommendations)).toBe(true);
			// Recommendations might be empty for successful collections, which is fine
		});

		it('should track resource usage during collection', async () =>
		{
			const request: DataCollectionRequest = {
				domain: 'example.com',
				modules: ['dns'],
				priority: 'medium',
			};

			const response = await orchestrator.collectData(request);

			expect(response.metadata.resourceUsage).toBeDefined();
			expect(response.metadata.resourceUsage.memoryUsed).toBeGreaterThanOrEqual(0);
			expect(response.metadata.resourceUsage.cpuTime).toBeGreaterThan(0);
			expect(response.metadata.resourceUsage.networkRequests).toBeGreaterThanOrEqual(0);
		});

		it('should calculate execution time correctly', async () =>
		{
			const request: DataCollectionRequest = {
				domain: 'example.com',
				modules: ['dns'],
				priority: 'medium',
			};

			const startTime = Date.now();
			const response = await orchestrator.collectData(request);
			const endTime = Date.now();

			expect(response.metadata.totalDuration).toBeGreaterThan(0);
			expect(response.metadata.totalDuration).toBeLessThanOrEqual(endTime - startTime + 100); // Allow small margin
			expect(response.metadata.startTime).toBeInstanceOf(Date);
			expect(response.metadata.endTime).toBeInstanceOf(Date);
		});
	});

	describe('data validation', () =>
	{
		it('should identify missing critical data', async () =>
		{
			const request: DataCollectionRequest = {
				domain: 'nonexistent.invalid',
				modules: ['dns', 'ssl', 'nonexistent'],
				priority: 'critical',
			};

			const response = await orchestrator.collectData(request);

			expect(response.validation.missingCritical).toBeDefined();
			expect(Array.isArray(response.validation.missingCritical)).toBe(true);
			expect(Array.isArray(response.validation.missingOptional)).toBe(true);

			// Completeness should be less than 1 due to missing module
			expect(response.validation.completenessScore).toBeLessThan(1);

			// The nonexistent module should not be in execution results
			expect(response.execution.results.nonexistent).toBeUndefined();

			// Should have 2 successful modules (dns, ssl) out of 3 requested
			expect(response.execution.successCount).toBe(2);
		});

		it('should calculate completeness score correctly', async () =>
		{
			const request: DataCollectionRequest = {
				domain: 'example.com',
				modules: ['dns', 'ssl'],
				priority: 'medium',
			};

			const response = await orchestrator.collectData(request);

			expect(response.validation.completenessScore).toBeGreaterThanOrEqual(0);
			expect(response.validation.completenessScore).toBeLessThanOrEqual(1);
			// With successful collection, should be high
			expect(response.validation.completenessScore).toBeGreaterThan(0.8);
		});
	});

	describe('error handling', () =>
	{
		it('should handle module execution failures gracefully', async () =>
		{
			// Create a module that always fails
			class FailingModule extends DataCollectionModule<any>
			{
				constructor()
				{
					const config: DataCollectionModuleConfig = {
						name: 'failing',
						priority: 50,
						timeout: 1000,
						retryAttempts: 1,
						retryDelay: 500,
						dependencies: [],
					};
					super(config);
				}

				protected async collect(domain: string): Promise<any>
				{
					throw new Error('Simulated failure');
				}
			}

			moduleRegistry.registerModule('failing', new FailingModule());

			const request: DataCollectionRequest = {
				domain: 'example.com',
				modules: ['dns', 'failing'],
				priority: 'medium',
			};

			const response = await orchestrator.collectData(request);

			expect(response.execution.successCount).toBe(1); // DNS should succeed
			expect(response.execution.failureCount).toBe(1); // Failing module should fail
			expect(response.execution.results.dns.success).toBe(true);
			expect(response.execution.results.failing.success).toBe(false);
			expect(response.validation.completenessScore).toBeLessThan(1);
		});
	});
});
