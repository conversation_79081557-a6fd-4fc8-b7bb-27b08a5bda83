import {
	describe, it, expect, beforeEach, vi,
} from 'vitest';
import { RedisClientWrapper, ScyllaClient } from '@shared';
import ScreenshotModule from '../ScreenshotModule';
import ScreenshotAnalyzer, { ScreenshotResult } from '../../analyzers/ScreenshotAnalyzer';

// Mock dependencies
vi.mock('../../analyzers/ScreenshotAnalyzer.js');
vi.mock('@shared', () => ({
	RedisClientWrapper: vi.fn(),
	ScyllaClient: vi.fn(),
	Logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			debug: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
		})),
	},
}));

const MockedScreenshotAnalyzer = ScreenshotAnalyzer as jest.MockedClass<typeof ScreenshotAnalyzer>;
const MockedRedisClientWrapper = RedisClientWrapper as jest.MockedClass<typeof RedisClientWrapper>;
const MockedScyllaClient = ScyllaClient as jest.MockedClass<typeof ScyllaClient>;

describe('ScreenshotModule', () =>
{
	let module: ScreenshotModule;
	let mockRedisClient: jest.Mocked<RedisClientWrapper>;
	let mockScyllaClient: jest.Mocked<ScyllaClient>;
	let mockAnalyzer: jest.Mocked<ScreenshotAnalyzer>;

	const mockDomain = 'example.com';
	const mockScreenshotResult: ScreenshotResult = {
		domain: mockDomain,
		screenshots: {
			desktop: {
				url: 'data:image/png;base64,desktop-screenshot',
				optimizedUrl: 'https://imageproxy.com/desktop-optimized.webp',
				width: 1920,
				height: 1080,
				format: 'webp',
				size: 150000,
				compressionRatio: 60,
			},
			mobile: {
				url: 'data:image/png;base64,mobile-screenshot',
				optimizedUrl: 'https://imageproxy.com/mobile-optimized.webp',
				width: 375,
				height: 667,
				format: 'webp',
				size: 80000,
				compressionRatio: 65,
			},
		},
		metadata: {
			browserlessVersion: '1.0.0',
			captureSettings: {
				timeout: 30000,
				waitUntil: 'networkidle2',
				quality: 90,
			},
			optimization: {
				enabled: true,
				service: 'weserv',
				quality: 80,
			},
		},
		captureTime: 5000,
		lastCaptured: new Date().toISOString(),
	};

	beforeEach(() =>
	{
		vi.clearAllMocks();

		// Mock Redis client
		mockRedisClient = {
			connect: vi.fn(),
			disconnect: vi.fn(),
			get: vi.fn(),
			set: vi.fn(),
			del: vi.fn(),
			getClient: vi.fn(),
		} as any;

		// Mock Scylla client
		mockScyllaClient = {
			connect: vi.fn(),
			disconnect: vi.fn(),
			execute: vi.fn(),
			getClient: vi.fn(),
		} as any;

		// Mock analyzer
		mockAnalyzer = {
			captureScreenshots: vi.fn(),
			validateCapability: vi.fn(),
		} as any;

		MockedRedisClientWrapper.mockImplementation(() => mockRedisClient);
		MockedScyllaClient.mockImplementation(() => mockScyllaClient);
		MockedScreenshotAnalyzer.mockImplementation(() => mockAnalyzer);

		module = new ScreenshotModule(mockRedisClient, mockScyllaClient);
	});

	describe('initialization', () =>
	{
		it('should initialize database connections successfully', async () =>
		{
			mockRedisClient.getClient.mockReturnValue(null);
			mockScyllaClient.getClient.mockReturnValue(null);
			mockAnalyzer.validateCapability.mockResolvedValue({
				browserlessAvailable: true,
				imageProxyAvailable: true,
			});

			await module.initialize();

			expect(mockRedisClient.connect).toHaveBeenCalled();
			expect(mockScyllaClient.connect).toHaveBeenCalled();
			expect(mockAnalyzer.validateCapability).toHaveBeenCalled();
		});

		it('should handle initialization errors', async () =>
		{
			mockRedisClient.connect.mockRejectedValue(new Error('Redis connection failed'));

			await expect(module.initialize()).rejects.toThrow('Redis connection failed');
		});
	});

	describe('collect', () =>
	{
		it('should return cached screenshot if available and fresh', async () =>
		{
			const cachedResult = {
				...mockScreenshotResult,
				lastCaptured: new Date().toISOString(), // Fresh timestamp
			};

			mockRedisClient.get.mockResolvedValue(cachedResult);

			const result = await (module as any).collect(mockDomain);

			expect(result).toEqual(cachedResult);
			expect(mockAnalyzer.captureScreenshots).not.toHaveBeenCalled();
		});

		it('should capture fresh screenshot if cache is stale', async () =>
		{
			const staleResult = {
				...mockScreenshotResult,
				lastCaptured: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(), // 25 hours ago
			};

			mockRedisClient.get.mockResolvedValue(staleResult);
			mockAnalyzer.captureScreenshots.mockResolvedValue(mockScreenshotResult);
			mockRedisClient.set.mockResolvedValue();
			mockScyllaClient.execute.mockResolvedValue({ rows: [] });

			const result = await (module as any).collect(mockDomain);

			expect(result).toEqual(mockScreenshotResult);
			expect(mockAnalyzer.captureScreenshots).toHaveBeenCalledWith(mockDomain);
		});

		it('should capture fresh screenshot if no cache exists', async () =>
		{
			mockRedisClient.get.mockResolvedValue(null);
			mockAnalyzer.captureScreenshots.mockResolvedValue(mockScreenshotResult);
			mockRedisClient.set.mockResolvedValue();
			mockScyllaClient.execute.mockResolvedValue({ rows: [] });

			const result = await (module as any).collect(mockDomain);

			expect(result).toEqual(mockScreenshotResult);
			expect(mockAnalyzer.captureScreenshots).toHaveBeenCalledWith(mockDomain);
		});
	});

	describe('captureFresh', () =>
	{
		it('should always capture fresh screenshot bypassing cache', async () =>
		{
			mockAnalyzer.captureScreenshots.mockResolvedValue(mockScreenshotResult);
			mockRedisClient.set.mockResolvedValue();
			mockScyllaClient.execute.mockResolvedValue({ rows: [] });

			const result = await module.captureFresh(mockDomain);

			expect(result).toEqual(mockScreenshotResult);
			expect(mockAnalyzer.captureScreenshots).toHaveBeenCalledWith(mockDomain);
			expect(mockRedisClient.get).not.toHaveBeenCalled();
		});
	});

	describe('getCachedScreenshot', () =>
	{
		it('should return cached screenshot if available', async () =>
		{
			mockRedisClient.get.mockResolvedValue(mockScreenshotResult);

			const result = await module.getCachedScreenshot(mockDomain);

			expect(result).toEqual(mockScreenshotResult);
			expect(mockRedisClient.get).toHaveBeenCalledWith(`screenshot:${mockDomain}`);
		});

		it('should return null if no cache exists', async () =>
		{
			mockRedisClient.get.mockResolvedValue(null);

			const result = await module.getCachedScreenshot(mockDomain);

			expect(result).toBeNull();
		});

		it('should handle cache errors gracefully', async () =>
		{
			mockRedisClient.get.mockRejectedValue(new Error('Cache error'));

			const result = await module.getCachedScreenshot(mockDomain);

			expect(result).toBeNull();
		});
	});

	describe('getScreenshotStats', () =>
	{
		it('should return screenshot statistics', async () =>
		{
			const mockStats = {
				total_captured: '100',
				successful_desktop: '95',
				successful_mobile: '90',
				avg_capture_time: '4500.5',
			};

			mockScyllaClient.execute.mockResolvedValue({
				rows: [mockStats],
			});

			const result = await module.getScreenshotStats();

			expect(result).toEqual({
				totalCaptured: 100,
				successfulDesktop: 95,
				successfulMobile: 90,
				averageCaptureTime: 4500.5,
			});
		});

		it('should handle database errors gracefully', async () =>
		{
			mockScyllaClient.execute.mockRejectedValue(new Error('Database error'));

			const result = await module.getScreenshotStats();

			expect(result).toEqual({
				totalCaptured: 0,
				successfulDesktop: 0,
				successfulMobile: 0,
				averageCaptureTime: 0,
			});
		});
	});

	describe('invalidateCache', () =>
	{
		it('should invalidate screenshot cache for domain', async () =>
		{
			mockRedisClient.del.mockResolvedValue(1);

			await module.invalidateCache(mockDomain);

			expect(mockRedisClient.del).toHaveBeenCalledWith(`screenshot:${mockDomain}`);
		});

		it('should handle cache invalidation errors gracefully', async () =>
		{
			mockRedisClient.del.mockRejectedValue(new Error('Cache error'));

			await expect(module.invalidateCache(mockDomain)).resolves.not.toThrow();
		});
	});

	describe('captureBatch', () =>
	{
		it('should capture screenshots for multiple domains', async () =>
		{
			const domains = ['example1.com', 'example2.com'];
			const results = domains.map(domain => ({
				...mockScreenshotResult,
				domain,
			}));

			mockRedisClient.get.mockResolvedValue(null);
			mockAnalyzer.captureScreenshots
				.mockResolvedValueOnce(results[0])
				.mockResolvedValueOnce(results[1]);
			mockRedisClient.set.mockResolvedValue();
			mockScyllaClient.execute.mockResolvedValue({ rows: [] });

			const batchResults = await module.captureBatch(domains);

			expect(batchResults).toHaveLength(2);
			expect(batchResults[0].domain).toBe('example1.com');
			expect(batchResults[1].domain).toBe('example2.com');
		});

		it('should use cached results when available and useCache is true', async () =>
		{
			const domains = ['cached.com', 'fresh.com'];
			const cachedResult = {
				...mockScreenshotResult,
				domain: 'cached.com',
				lastCaptured: new Date().toISOString(),
			};
			const freshResult = {
				...mockScreenshotResult,
				domain: 'fresh.com',
			};

			mockRedisClient.get
				.mockResolvedValueOnce(cachedResult)
				.mockResolvedValueOnce(null);
			mockAnalyzer.captureScreenshots.mockResolvedValueOnce(freshResult);
			mockRedisClient.set.mockResolvedValue();
			mockScyllaClient.execute.mockResolvedValue({ rows: [] });

			const results = await module.captureBatch(domains, true);

			expect(results).toHaveLength(2);
			expect(results[0]).toEqual(cachedResult);
			expect(results[1]).toEqual(freshResult);
			expect(mockAnalyzer.captureScreenshots).toHaveBeenCalledTimes(1);
		});
	});

	describe('validateCapability', () =>
	{
		it('should validate screenshot capture capability', async () =>
		{
			const mockCapability = {
				browserlessAvailable: true,
				imageProxyAvailable: true,
				browserlessStatus: { available: true },
			};

			mockAnalyzer.validateCapability.mockResolvedValue(mockCapability);

			const result = await module.validateCapability();

			expect(result).toEqual(mockCapability);
			expect(mockAnalyzer.validateCapability).toHaveBeenCalled();
		});
	});

	describe('cleanup', () =>
	{
		it('should cleanup database connections', async () =>
		{
			await module.cleanup();

			expect(mockRedisClient.disconnect).toHaveBeenCalled();
			expect(mockScyllaClient.disconnect).toHaveBeenCalled();
		});

		it('should handle cleanup errors gracefully', async () =>
		{
			mockRedisClient.disconnect.mockRejectedValue(new Error('Disconnect error'));

			await expect(module.cleanup()).resolves.not.toThrow();
		});
	});
});
