import { vi } from 'vitest';
import PerformanceModule from '../PerformanceModule';
import PerformanceAuditor from '../../analyzers/PerformanceAuditor';

// Mock the PerformanceAuditor
vi.mock('../../analyzers/PerformanceAuditor');
const MockedPerformanceAuditor = PerformanceAuditor as jest.MockedClass<typeof PerformanceAuditor>;

// Mock shared dependencies
vi.mock('@shared', () => ({
	Logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			debug: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
		})),
	},
}));

describe('PerformanceModule', () =>
{
	let performanceModule: PerformanceModule;
	let mockPerformanceAuditor: jest.Mocked<PerformanceAuditor>;

	beforeEach(() =>
	{
		vi.clearAllMocks();

		// Setup mock instance
		mockPerformanceAuditor = {
			auditPerformance: vi.fn(),
			auditMobilePerformance: vi.fn(),
			getBrowserlessStatus: vi.fn(),
			validateCapability: vi.fn(),
			batchAuditPerformance: vi.fn(),
		} as any;

		MockedPerformanceAuditor.mockImplementation(() => mockPerformanceAuditor);

		performanceModule = new PerformanceModule();
	});

	describe('initialization', () =>
	{
		it('should initialize with correct module configuration', () =>
		{
			const config = performanceModule.getConfig();

			expect(config.name).toBe('performance');
			expect(config.priority).toBe(3);
			expect(config.timeout).toBe(90000);
			expect(config.retryAttempts).toBe(2);
		});
	});

	describe('isAvailable', () =>
	{
		it('should return true when browserless and performance APIs are available', async () =>
		{
			mockPerformanceAuditor.validateCapability.mockResolvedValue({
				browserlessAvailable: true,
				performanceAPISupported: true,
			});

			const available = await performanceModule.isAvailable();

			expect(available).toBe(true);
			expect(mockPerformanceAuditor.validateCapability).toHaveBeenCalledTimes(1);
		});

		it('should return false when browserless is not available', async () =>
		{
			mockPerformanceAuditor.validateCapability.mockResolvedValue({
				browserlessAvailable: false,
				performanceAPISupported: true,
			});

			const available = await performanceModule.isAvailable();

			expect(available).toBe(false);
		});

		it('should return false when performance APIs are not supported', async () =>
		{
			mockPerformanceAuditor.validateCapability.mockResolvedValue({
				browserlessAvailable: true,
				performanceAPISupported: false,
			});

			const available = await performanceModule.isAvailable();

			expect(available).toBe(false);
		});

		it('should handle capability check errors gracefully', async () =>
		{
			mockPerformanceAuditor.validateCapability.mockRejectedValue(new Error('Service check failed'));

			const available = await performanceModule.isAvailable();

			expect(available).toBe(false);
		});
	});

	describe('execute', () =>
	{
		const mockAuditResult = {
			domain: 'example.com',
			metrics: {
				loadTime: 2500,
				firstContentfulPaint: 1200,
				largestContentfulPaint: 2000,
				cumulativeLayoutShift: 0.05,
				firstInputDelay: 50,
				speedIndex: 1800,
				score: 0.85,
			},
			resourceAnalysis: {
				totalRequests: 25,
				totalSize: 1024000,
				resourceTypes: {
					img: { count: 10, size: 512000 },
					script: { count: 8, size: 256000 },
				},
				largestResources: [],
			},
			networkAnalysis: {
				connectionType: '4g',
				rtt: 50,
				downlink: 10,
				effectiveType: '4g',
			},
			auditDetails: {
				viewport: { width: 1920, height: 1080 },
				userAgent: 'test-agent',
				connectionSettings: {},
				auditTime: 5000,
			},
			recommendations: ['Optimize images', 'Reduce JavaScript'],
			lastAudited: new Date().toISOString(),
		};

		it('should successfully collect performance data', async () =>
		{
			mockPerformanceAuditor.getBrowserlessStatus.mockResolvedValue({
				available: true,
				stats: { sessions: 1 },
			});

			mockPerformanceAuditor.auditPerformance.mockResolvedValue(mockAuditResult);

			const result = await performanceModule.collectData('example.com');

			expect(result.success).toBe(true);
			expect(result.data).toEqual(mockAuditResult);
			expect(result.error).toBeUndefined();
			expect(result.metadata.module).toBe('performance');
			expect(result.metadata.executionTime).toBeGreaterThan(0);
			expect(result.metadata.resourceUsage).toBeDefined();

			expect(mockPerformanceAuditor.getBrowserlessStatus).toHaveBeenCalledTimes(1);
			expect(mockPerformanceAuditor.auditPerformance).toHaveBeenCalledWith('example.com');
		});

		it('should handle browserless unavailability', async () =>
		{
			mockPerformanceAuditor.getBrowserlessStatus.mockResolvedValue({
				available: false,
			});

			const result = await performanceModule.collectData('example.com');

			expect(result.success).toBe(false);
			expect(result.error).toBe('Browserless service is not available for performance auditing');
			expect(result.data).toBeUndefined();
			expect(result.metadata.module).toBe('performance');

			expect(mockPerformanceAuditor.auditPerformance).not.toHaveBeenCalled();
		});

		it('should handle audit errors', async () =>
		{
			mockPerformanceAuditor.getBrowserlessStatus.mockResolvedValue({
				available: true,
			});

			const auditResultWithError = {
				...mockAuditResult,
				error: 'Audit failed due to timeout',
			};

			mockPerformanceAuditor.auditPerformance.mockResolvedValue(auditResultWithError);

			const result = await performanceModule.collectData('example.com');

			expect(result.success).toBe(false);
			expect(result.error).toBe('Audit failed due to timeout');
			expect(result.data).toBeUndefined();
		});

		it('should handle auditor exceptions', async () =>
		{
			mockPerformanceAuditor.getBrowserlessStatus.mockResolvedValue({
				available: true,
			});

			mockPerformanceAuditor.auditPerformance.mockRejectedValue(new Error('Network timeout'));

			const result = await performanceModule.collectData('example.com');

			expect(result.success).toBe(false);
			expect(result.error).toBe('Network timeout');
			expect(result.data).toBeUndefined();
		});
	});

	describe('collectMobileData', () =>
	{
		const mockMobileAuditResult = {
			domain: 'example.com',
			metrics: {
				loadTime: 3500,
				firstContentfulPaint: 1800,
				largestContentfulPaint: 2800,
				cumulativeLayoutShift: 0.08,
				firstInputDelay: 80,
				speedIndex: 2200,
				score: 0.75,
			},
			resourceAnalysis: {
				totalRequests: 30,
				totalSize: 1200000,
				resourceTypes: {},
				largestResources: [],
			},
			networkAnalysis: {
				connectionType: '3g',
				rtt: 100,
				downlink: 5,
				effectiveType: '3g',
			},
			auditDetails: {
				viewport: { width: 375, height: 667 },
				userAgent: 'mobile-agent',
				connectionSettings: {},
				auditTime: 6000,
			},
			recommendations: [],
			lastAudited: new Date().toISOString(),
		};

		it('should successfully collect mobile performance data', async () =>
		{
			mockPerformanceAuditor.getBrowserlessStatus.mockResolvedValue({
				available: true,
			});

			mockPerformanceAuditor.auditMobilePerformance.mockResolvedValue(mockMobileAuditResult);

			const result = await performanceModule.collectMobileData('example.com');

			expect(result.success).toBe(true);
			expect(result.data).toEqual(mockMobileAuditResult);
			expect(result.metadata.deviceType).toBe('mobile');

			expect(mockPerformanceAuditor.auditMobilePerformance).toHaveBeenCalledWith('example.com');
		});

		it('should handle mobile audit failures', async () =>
		{
			mockPerformanceAuditor.getBrowserlessStatus.mockResolvedValue({
				available: true,
			});

			mockPerformanceAuditor.auditMobilePerformance.mockRejectedValue(new Error('Mobile audit failed'));

			const result = await performanceModule.collectMobileData('example.com');

			expect(result.success).toBe(false);
			expect(result.error).toBe('Mobile audit failed');
			expect(result.metadata.deviceType).toBe('mobile');
		});
	});

	describe('getDataSummary', () =>
	{
		it('should return comprehensive data summary', () =>
		{
			const mockData = {
				domain: 'example.com',
				metrics: {
					loadTime: 2500,
					firstContentfulPaint: 1200,
					largestContentfulPaint: 2000,
					cumulativeLayoutShift: 0.05,
					firstInputDelay: 50,
					speedIndex: 1800,
					score: 0.85,
				},
				resourceAnalysis: {
					totalRequests: 25,
					totalSize: 1024000,
					resourceTypes: { img: { count: 10, size: 512000 } },
					largestResources: [{
						url: 'test.jpg', type: 'img', size: 100000, loadTime: 200,
					}],
				},
				networkAnalysis: {
					connectionType: '4g',
					rtt: 50,
					downlink: 10,
					effectiveType: '4g',
				},
				auditDetails: {
					viewport: { width: 1920, height: 1080 },
					userAgent: 'test-agent',
					connectionSettings: {},
					auditTime: 5000,
				},
				recommendations: ['Optimize images'],
				lastAudited: '2024-01-01T00:00:00.000Z',
			};

			const summary = performanceModule.getDataSummary(mockData as any);

			expect(summary).toEqual({
				loadTime: 2500,
				firstContentfulPaint: 1200,
				largestContentfulPaint: 2000,
				cumulativeLayoutShift: 0.05,
				firstInputDelay: 50,
				speedIndex: 1800,
				performanceScore: 0.85,
				totalRequests: 25,
				totalSize: 1024000,
				resourceTypes: { img: { count: 10, size: 512000 } },
				largestResourcesCount: 1,
				connectionType: '4g',
				rtt: 50,
				downlink: 10,
				effectiveType: '4g',
				auditTime: 5000,
				viewport: { width: 1920, height: 1080 },
				recommendationsCount: 1,
				lastAudited: '2024-01-01T00:00:00.000Z',
				hasError: false,
			});
		});
	});

	describe('transformForStorage', () =>
	{
		it('should transform data for ScyllaDB storage format', () =>
		{
			const mockData = {
				domain: 'example.com',
				metrics: {
					loadTime: 2500,
					firstContentfulPaint: 1200,
					largestContentfulPaint: 2000,
					cumulativeLayoutShift: 0.05,
					firstInputDelay: 50,
					speedIndex: 1800,
					score: 0.85,
				},
				resourceAnalysis: {
					totalRequests: 25,
					totalSize: 1024000,
					resourceTypes: { img: { count: 10, size: 512000 } },
					largestResources: [{
						url: 'test.jpg', type: 'img', size: 100000, loadTime: 200,
					}],
				},
				networkAnalysis: {
					connectionType: '4g',
					rtt: 50,
					downlink: 10,
					effectiveType: '4g',
				},
				auditDetails: {
					viewport: { width: 1920, height: 1080 },
					userAgent: 'test-agent',
					connectionSettings: {},
					auditTime: 5000,
					browserVersion: 'Chrome/120',
				},
				recommendations: ['Optimize images'],
				lastAudited: '2024-01-01T00:00:00.000Z',
			};

			const transformed = performanceModule.transformForStorage(mockData as any);

			expect(transformed.performance_metrics).toBeInstanceOf(Map);
			expect(transformed.performance_metrics.get('load_time')).toBe('2500');
			expect(transformed.performance_metrics.get('score')).toBe('0.85');

			expect(transformed.resource_analysis).toBeInstanceOf(Map);
			expect(transformed.network_analysis).toBeInstanceOf(Map);
			expect(transformed.audit_metadata).toBeInstanceOf(Map);

			expect(transformed.performance_recommendations).toEqual(['Optimize images']);
			expect(transformed.last_performance_audit).toBe('2024-01-01T00:00:00.000Z');
		});
	});

	describe('validateData', () =>
	{
		it('should validate correct performance data', () =>
		{
			const validData = {
				domain: 'example.com',
				metrics: {
					loadTime: 2500,
					firstContentfulPaint: 1200,
					largestContentfulPaint: 2000,
					cumulativeLayoutShift: 0.05,
					firstInputDelay: 50,
					speedIndex: 1800,
					score: 0.85,
				},
				resourceAnalysis: {
					totalRequests: 25,
					totalSize: 1024000,
					resourceTypes: {},
					largestResources: [],
				},
				auditDetails: {
					viewport: { width: 1920, height: 1080 },
					userAgent: 'test-agent',
					connectionSettings: {},
					auditTime: 5000,
				},
				networkAnalysis: {
					connectionType: '4g',
					rtt: 50,
					downlink: 10,
					effectiveType: '4g',
				},
				recommendations: [],
				lastAudited: '2024-01-01T00:00:00.000Z',
			};

			const validation = performanceModule.validateData(validData as any);

			expect(validation.isValid).toBe(true);
			expect(validation.errors).toHaveLength(0);
		});

		it('should detect validation errors', () =>
		{
			const invalidData = {
				// Missing domain
				metrics: {
					score: 1.5, // Invalid score > 1
				},
				// Missing other required fields
			};

			const validation = performanceModule.validateData(invalidData as any);

			expect(validation.isValid).toBe(false);
			expect(validation.errors).toContain('Domain is required');
			expect(validation.errors).toContain('Performance score must be between 0 and 1');
		});

		it('should detect validation warnings', () =>
		{
			const dataWithWarnings = {
				domain: 'example.com',
				metrics: {
					largestContentfulPaint: 70000, // Unrealistic LCP
					cumulativeLayoutShift: 6, // Unrealistic CLS
					firstInputDelay: 15000, // Unrealistic FID
					score: 0.5,
				},
				resourceAnalysis: {
					totalRequests: 1500, // Too many requests
					totalSize: 100 * 1024 * 1024, // Too large
					resourceTypes: {},
					largestResources: [],
				},
				auditDetails: {
					auditTime: 0, // Invalid audit time
				},
			};

			const validation = performanceModule.validateData(dataWithWarnings as any);

			expect(validation.warnings.length).toBeGreaterThan(0);
			expect(validation.warnings.some(w => w.includes('LCP value seems unrealistic'))).toBe(true);
			expect(validation.warnings.some(w => w.includes('CLS value seems unrealistic'))).toBe(true);
			expect(validation.warnings.some(w => w.includes('FID value seems unrealistic'))).toBe(true);
			expect(validation.warnings.some(w => w.includes('high number of requests'))).toBe(true);
			expect(validation.warnings.some(w => w.includes('large page size'))).toBe(true);
		});
	});

	describe('getHealthStatus', () =>
	{
		it('should return healthy status when all systems are operational', async () =>
		{
			mockPerformanceAuditor.validateCapability.mockResolvedValue({
				browserlessAvailable: true,
				performanceAPISupported: true,
			});

			mockPerformanceAuditor.getBrowserlessStatus.mockResolvedValue({
				available: true,
				stats: { sessions: 2 },
				pressure: { cpu: 0.3 },
			});

			const health = await performanceModule.getHealthStatus();

			expect(health.healthy).toBe(true);
			expect(health.status).toBe('operational');
			expect(health.details.browserlessAvailable).toBe(true);
			expect(health.details.performanceAPISupported).toBe(true);
		});

		it('should return degraded status when systems are partially available', async () =>
		{
			mockPerformanceAuditor.validateCapability.mockResolvedValue({
				browserlessAvailable: false,
				performanceAPISupported: true,
			});

			mockPerformanceAuditor.getBrowserlessStatus.mockResolvedValue({
				available: false,
			});

			const health = await performanceModule.getHealthStatus();

			expect(health.healthy).toBe(false);
			expect(health.status).toBe('degraded');
		});

		it('should handle health check errors', async () =>
		{
			mockPerformanceAuditor.validateCapability.mockRejectedValue(new Error('Health check failed'));

			const health = await performanceModule.getHealthStatus();

			expect(health.healthy).toBe(false);
			expect(health.status).toBe('error');
			expect(health.details.error).toBe('Health check failed');
		});
	});

	describe('getMetrics', () =>
	{
		it('should return performance module metrics', () =>
		{
			const metrics = performanceModule.getMetrics();

			expect(metrics).toHaveProperty('avgAuditTime');
			expect(metrics).toHaveProperty('successRate');
			expect(metrics).toHaveProperty('resourceIntensiveOperations');
			expect(typeof metrics.avgAuditTime).toBe('number');
			expect(typeof metrics.successRate).toBe('number');
			expect(typeof metrics.resourceIntensiveOperations).toBe('number');
		});
	});
});
