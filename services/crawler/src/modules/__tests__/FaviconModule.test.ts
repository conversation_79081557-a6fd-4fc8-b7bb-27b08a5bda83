import {
	describe, it, expect, beforeEach, afterEach, vi,
} from 'vitest';
import { RedisClientWrapper, ScyllaClient } from '@shared';
import { FaviconModule } from '../FaviconModule';
import { FaviconCollectionResult } from '../../analyzers/FaviconCollector';

// Mock the database clients for testing
vi.mock('@shared', () => ({
	...vi.importActual('@shared'),
	RedisClientWrapper: vi.fn().mockImplementation(() => ({
		connect: vi.fn(),
		disconnect: vi.fn(),
		get: vi.fn(),
		set: vi.fn(),
		del: vi.fn(),
		getClient: vi.fn().mockReturnValue({ keys: vi.fn().mockResolvedValue([]) }),
	})),
	ScyllaClient: vi.fn().mockImplementation(() => ({
		connect: vi.fn(),
		disconnect: vi.fn(),
		execute: vi.fn(),
		getClient: vi.fn().mockReturnValue({}),
	})),
	CacheManager: vi.fn().mockImplementation(() => ({})),
	Logger: {
		getLogger: vi.fn().mockReturnValue({
			info: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
			debug: vi.fn(),
		}),
	},
}));

// Mock FaviconCollector
vi.mock('../../analyzers/FaviconCollector.js', () => ({
	__esModule: true,
	default: vi.fn().mockImplementation(() => ({
		collectFavicon: vi.fn(),
		getStoredFavicon: vi.fn(),
		invalidateFaviconCache: vi.fn(),
		getFaviconCacheStats: vi.fn(),
	})),
}));

describe('FaviconModule', () =>
{
	let faviconModule: FaviconModule;
	let mockRedisClient: jest.Mocked<RedisClientWrapper>;
	let mockScyllaClient: jest.Mocked<ScyllaClient>;

	beforeEach(() =>
	{
		// Create mock instances
		mockRedisClient = new RedisClientWrapper() as jest.Mocked<RedisClientWrapper>;
		mockScyllaClient = new ScyllaClient() as jest.Mocked<ScyllaClient>;

		// Reset mocks
		vi.clearAllMocks();

		faviconModule = new FaviconModule(mockRedisClient, mockScyllaClient);
	});

	afterEach(async () =>
	{
		await faviconModule.cleanup();
	});

	describe('initialization', () =>
	{
		it('should initialize with default database clients when none provided', () =>
		{
			const module = new FaviconModule();

			expect(module).toBeDefined();
		});

		it('should initialize database connections', async () =>
		{
			mockRedisClient.getClient.mockReturnValue(null);
			mockScyllaClient.getClient.mockReturnValue(null);

			await faviconModule.initialize();

			expect(mockRedisClient.connect).toHaveBeenCalled();
			expect(mockScyllaClient.connect).toHaveBeenCalled();
		});

		it('should handle initialization errors', async () =>
		{
			mockRedisClient.getClient.mockReturnValue(null);
			mockScyllaClient.getClient.mockReturnValue(null);
			mockRedisClient.connect.mockRejectedValue(new Error('Redis connection failed'));

			await expect(faviconModule.initialize()).rejects.toThrow('Redis connection failed');
		});

		it('should cleanup database connections', async () =>
		{
			await faviconModule.cleanup();

			expect(mockRedisClient.disconnect).toHaveBeenCalled();
			expect(mockScyllaClient.disconnect).toHaveBeenCalled();
		});
	});

	describe('favicon collection', () =>
	{
		const mockResult: FaviconCollectionResult = {
			domain: 'example.com',
			faviconFound: true,
			faviconUrl: 'https://icons.duckduckgo.com/ip3/example.com.ico',
			faviconSize: 1024,
			faviconType: 'image/x-icon',
			sources: { duckduckgo: true, direct: false, html: false },
			fallbackUsed: 'duckduckgo',
			lastCollected: '2024-01-01T00:00:00.000Z',
		};

		it('should collect favicon using cache by default', async () =>
		{
			const mockCollector = (faviconModule as any).collector;
			mockCollector.collectFavicon.mockResolvedValue(mockResult);

			const result = await faviconModule.collect('example.com');

			expect(result).toEqual(mockResult);
			expect(mockCollector.collectFavicon).toHaveBeenCalledWith('example.com', true);
		});

		it('should collect fresh favicon without cache', async () =>
		{
			const mockCollector = (faviconModule as any).collector;
			mockCollector.collectFavicon.mockResolvedValue(mockResult);

			const result = await faviconModule.collectFresh('example.com');

			expect(result).toEqual(mockResult);
			expect(mockCollector.collectFavicon).toHaveBeenCalledWith('example.com', false);
		});

		it('should get cached favicon', async () =>
		{
			mockRedisClient.get.mockResolvedValue(mockResult);

			const result = await faviconModule.getCachedFavicon('example.com');

			expect(result).toEqual(mockResult);
			expect(mockRedisClient.get).toHaveBeenCalledWith('favicon:example.com');
		});

		it('should handle cache retrieval errors', async () =>
		{
			mockRedisClient.get.mockRejectedValue(new Error('Redis error'));

			const result = await faviconModule.getCachedFavicon('example.com');

			expect(result).toBeNull();
		});

		it('should get stored favicon from database', async () =>
		{
			const mockCollector = (faviconModule as any).collector;
			mockCollector.getStoredFavicon.mockResolvedValue(mockResult);

			const result = await faviconModule.getStoredFavicon('example.com');

			expect(result).toEqual(mockResult);
			expect(mockCollector.getStoredFavicon).toHaveBeenCalledWith('example.com');
		});

		it('should invalidate cache', async () =>
		{
			const mockCollector = (faviconModule as any).collector;
			mockCollector.invalidateFaviconCache.mockResolvedValue(undefined);

			await faviconModule.invalidateCache('example.com');

			expect(mockCollector.invalidateFaviconCache).toHaveBeenCalledWith('example.com');
		});

		it('should get cache statistics', async () =>
		{
			const mockStats = {
				totalCached: 100,
				successfulCached: 80,
				failedCached: 20,
			};

			const mockCollector = (faviconModule as any).collector;
			mockCollector.getFaviconCacheStats.mockResolvedValue(mockStats);

			const result = await faviconModule.getCacheStats();

			expect(result).toEqual(mockStats);
			expect(mockCollector.getFaviconCacheStats).toHaveBeenCalled();
		});
	});

	describe('batch collection', () =>
	{
		const mockResults: FaviconCollectionResult[] = [
			{
				domain: 'example1.com',
				faviconFound: true,
				faviconUrl: 'https://icons.duckduckgo.com/ip3/example1.com.ico',
				faviconSize: 1024,
				faviconType: 'image/x-icon',
				sources: { duckduckgo: true, direct: false, html: false },
				fallbackUsed: 'duckduckgo',
				lastCollected: '2024-01-01T00:00:00.000Z',
			},
			{
				domain: 'example2.com',
				faviconFound: false,
				sources: { duckduckgo: false, direct: false, html: false },
				fallbackUsed: null,
				lastCollected: '2024-01-01T00:00:00.000Z',
			},
		];

		it('should collect favicons for multiple domains', async () =>
		{
			const mockCollector = (faviconModule as any).collector;
			mockCollector.collectFavicon
				.mockResolvedValueOnce(mockResults[0])
				.mockResolvedValueOnce(mockResults[1]);

			const domains = ['example1.com', 'example2.com'];
			const results = await faviconModule.collectBatch(domains);

			expect(results).toHaveLength(2);
			expect(results[0]).toEqual(mockResults[0]);
			expect(results[1]).toEqual(mockResults[1]);
			expect(mockCollector.collectFavicon).toHaveBeenCalledTimes(2);
		});

		it('should handle batch collection with cache disabled', async () =>
		{
			const mockCollector = (faviconModule as any).collector;
			mockCollector.collectFavicon
				.mockResolvedValueOnce(mockResults[0])
				.mockResolvedValueOnce(mockResults[1]);

			const domains = ['example1.com', 'example2.com'];
			const results = await faviconModule.collectBatch(domains, false);

			expect(results).toHaveLength(2);
			expect(mockCollector.collectFavicon).toHaveBeenCalledWith('example1.com', false);
			expect(mockCollector.collectFavicon).toHaveBeenCalledWith('example2.com', false);
		});

		it('should handle errors in batch collection gracefully', async () =>
		{
			const mockCollector = (faviconModule as any).collector;
			mockCollector.collectFavicon
				.mockResolvedValueOnce(mockResults[0])
				.mockRejectedValueOnce(new Error('Collection failed'));

			const domains = ['example1.com', 'example2.com'];
			const results = await faviconModule.collectBatch(domains);

			expect(results).toHaveLength(2);
			expect(results[0]).toEqual(mockResults[0]);
			expect(results[1].domain).toBe('example2.com');
			expect(results[1].faviconFound).toBe(false);
			expect(results[1].error).toBe('Collection failed');
		});

		it('should process large batches with concurrency limit', async () =>
		{
			const mockCollector = (faviconModule as any).collector;

			// Create 10 domains
			const domains = Array.from({ length: 10 }, (_, i) => `example${i}.com`);
			const mockBatchResults = domains.map(domain => ({
				domain,
				faviconFound: true,
				faviconUrl: `https://icons.duckduckgo.com/ip3/${domain}.ico`,
				faviconSize: 1024,
				faviconType: 'image/x-icon',
				sources: { duckduckgo: true, direct: false, html: false },
				fallbackUsed: 'duckduckgo',
				lastCollected: '2024-01-01T00:00:00.000Z',
			}));

			// Mock all calls
			mockBatchResults.forEach((result) =>
			{
				mockCollector.collectFavicon.mockResolvedValueOnce(result);
			});

			const results = await faviconModule.collectBatch(domains);

			expect(results).toHaveLength(10);
			expect(mockCollector.collectFavicon).toHaveBeenCalledTimes(10);

			// Verify all domains were processed
			results.forEach((result, index) =>
			{
				expect(result.domain).toBe(domains[index]);
			});
		});

		it('should handle empty domain list', async () =>
		{
			const results = await faviconModule.collectBatch([]);

			expect(results).toHaveLength(0);
		});
	});

	describe('module configuration', () =>
	{
		it('should have correct module configuration', () =>
		{
			const config = (faviconModule as any).config;

			expect(config.name).toBe('favicon');
			expect(config.priority).toBe(50);
			expect(config.timeout).toBe(15000);
			expect(config.retryAttempts).toBe(1);
			expect(config.retryDelay).toBe(1000);
			expect(config.dependencies).toEqual(['dns']);
		});
	});

	describe('error handling', () =>
	{
		it('should handle cleanup errors gracefully', async () =>
		{
			mockRedisClient.disconnect.mockRejectedValue(new Error('Disconnect failed'));

			// Should not throw error
			await expect(faviconModule.cleanup()).resolves.not.toThrow();
		});
	});
});
