import {
	describe, it, expect, beforeEach, vi,
} from 'vitest';
import type { Logger } from '@shared';
import { ImageProcessingModule } from '../ImageProcessingModule';

// Mock logger
const mockLogger: Logger = {
	info: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
	debug: vi.fn(),
};

// Mock ImageProcessor
vi.mock('../analyzers/ImageProcessor', () => ({
	ImageProcessor: vi.fn().mockImplementation(() => ({
		processImage: vi.fn(),
		validateImageUrl: vi.fn(),
	})),
}));

describe('ImageProcessingModule', () =>
{
	let imageProcessingModule: ImageProcessingModule;

	beforeEach(() =>
	{
		imageProcessingModule = new ImageProcessingModule(mockLogger);
		vi.clearAllMocks();
	});

	describe('collect', () =>
	{
		it('should initialize with correct module name', () =>
		{
			expect(imageProcessingModule.getName()).toBe('image-processing');
		});

		it('should collect and return processing stats', async () =>
		{
			const domain = 'example.com';

			const result = await imageProcessingModule.collect(domain);

			expect(result).toHaveProperty('screenshots');
			expect(result).toHaveProperty('favicons');
			expect(result).toHaveProperty('contentImages');
			expect(result).toHaveProperty('processingStats');
			expect(result.processingStats).toHaveProperty('totalImages');
			expect(result.processingStats).toHaveProperty('successfullyProcessed');
			expect(result.processingStats).toHaveProperty('failed');
			expect(result.processingStats).toHaveProperty('totalSizeSaved');
		});
	});

	describe('processImage', () =>
	{
		it('should process single image with correct options', async () =>
		{
			const mockImageUrl = 'https://example.com/image.jpg';
			const mockResult = {
				url: 'https://processed.url',
				originalUrl: mockImageUrl,
				format: 'webp',
				size: 50000,
				optimized: true,
			};

			// Mock the ImageProcessor instance
			const mockImageProcessor = {
				processImage: vi.fn().mockResolvedValue(mockResult),
				validateImageUrl: vi.fn(),
			};

			(imageProcessingModule as any).imageProcessor = mockImageProcessor;

			const result = await imageProcessingModule.processImage(mockImageUrl, 'screenshot');

			expect(mockImageProcessor.processImage).toHaveBeenCalledWith(
				mockImageUrl,
				'screenshot',
				expect.objectContaining({
					format: 'webp',
					quality: 90,
					compression: true,
				}),
			);
			expect(result).toEqual(mockResult);
		});
	});

	describe('isAvailable', () =>
	{
		it('should check service availability', async () =>
		{
			const mockImageProcessor = {
				processImage: vi.fn(),
				validateImageUrl: vi.fn().mockResolvedValue(true),
			};

			(imageProcessingModule as any).imageProcessor = mockImageProcessor;

			const isAvailable = await imageProcessingModule.isAvailable();

			expect(isAvailable).toBe(true);
			expect(mockImageProcessor.validateImageUrl).toHaveBeenCalled();
		});

		it('should handle service unavailability', async () =>
		{
			const mockImageProcessor = {
				processImage: vi.fn(),
				validateImageUrl: vi.fn().mockRejectedValue(new Error('Service unavailable')),
			};

			(imageProcessingModule as any).imageProcessor = mockImageProcessor;

			const isAvailable = await imageProcessingModule.isAvailable();

			expect(isAvailable).toBe(false);
		});
	});

	describe('configuration', () =>
	{
		it('should use custom configuration', () =>
		{
			const customConfig = {
				weservUrl: 'https://custom.weserv.nl',
				maxConcurrentProcessing: 10,
				screenshotOptions: {
					format: 'png' as const,
					quality: 95,
				},
			};

			const customModule = new ImageProcessingModule(mockLogger, customConfig);

			expect((customModule as any).config.weservUrl).toBe(customConfig.weservUrl);
			expect((customModule as any).config.maxConcurrentProcessing).toBe(10);
		});

		it('should use default configuration when not provided', () =>
		{
			const defaultModule = new ImageProcessingModule(mockLogger);

			expect((defaultModule as any).config.maxConcurrentProcessing).toBe(5);
			expect((defaultModule as any).config.screenshotOptions.format).toBe('webp');
			expect((defaultModule as any).config.faviconOptions.format).toBe('png');
		});
	});
});
