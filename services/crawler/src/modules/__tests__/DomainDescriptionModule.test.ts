import {
	describe, it, expect, beforeEach, vi,
} from 'vitest';
import type { Logger, DomainDescriptionConfig } from '@shared';
import { DomainDescriptionModule } from '../DomainDescriptionModule';

// Mock DomainDescriptionGenerator
vi.mock('../analyzers/DomainDescriptionGenerator', () => ({
	DomainDescriptionGenerator: vi.fn().mockImplementation(() => ({
		generateDescription: vi.fn(),
		extractCompanyInfo: vi.fn(),
		isAvailable: vi.fn(),
		batchGenerateDescriptions: vi.fn(),
	})),
}));

// Mock logger
const mockLogger: Logger = {
	info: vi.fn(),
	warn: vi.fn(),
	error: vi.fn(),
	debug: vi.fn(),
};

describe('DomainDescriptionModule', () =>
{
	let module: DomainDescriptionModule;
	let mockConfig: DomainDescriptionConfig;

	beforeEach(() =>
	{
		mockConfig = {
			providers: [
				{
					provider: 'openai',
					model: 'gpt-3.5-turbo',
					apiKey: 'test-key',
				},
			],
			defaultProvider: 'openai',
			enableBatchProcessing: true,
			batchSize: 3,
			retryAttempts: 2,
			fallbackEnabled: true,
		};

		module = new DomainDescriptionModule(mockLogger, mockConfig);
		vi.clearAllMocks();
	});

	describe('constructor', () =>
	{
		it('should initialize with correct module name', () =>
		{
			expect(module.getName()).toBe('domain-description');
		});

		it('should throw error when no providers configured', () =>
		{
			expect(() =>
			{
				new DomainDescriptionModule(mockLogger, { providers: [] });
			}).toThrow('At least one AI provider must be configured');
		});

		it('should use default configuration values', () =>
		{
			const minimalConfig = {
				providers: [
					{
						provider: 'openai' as const,
						model: 'gpt-3.5-turbo',
						apiKey: 'test-key',
					},
				],
			};

			const defaultModule = new DomainDescriptionModule(mockLogger, minimalConfig);

			expect((defaultModule as any).config.enableBatchProcessing).toBe(true);
			expect((defaultModule as any).config.batchSize).toBe(3);
			expect((defaultModule as any).config.retryAttempts).toBe(2);
			expect((defaultModule as any).config.fallbackEnabled).toBe(true);
		});
	});

	describe('collect', () =>
	{
		it('should collect domain description successfully', async () =>
		{
			const mockDescription = {
				shortDescription: 'Test description',
				longDescription: 'Detailed test description',
				companyInfo: { name: 'Test Company' },
				keyFeatures: ['Feature 1', 'Feature 2'],
				targetAudience: 'Test audience',
				confidence: 0.9,
				generatedAt: new Date(),
			};

			const mockGenerator = {
				generateDescription: vi.fn().mockResolvedValue(mockDescription),
				extractCompanyInfo: vi.fn(),
				isAvailable: vi.fn(),
				batchGenerateDescriptions: vi.fn(),
			};

			(module as any).descriptionGenerator = mockGenerator;

			const result = await module.collect('example.com');

			expect(result.domain).toBe('example.com');
			expect(result.success).toBe(true);
			expect(result.description).toEqual(mockDescription);
			expect(result.processingTime).toBeGreaterThan(0);
			expect(result.aiProvider).toBe('openai');
		});

		it('should handle generation failure with fallback', async () =>
		{
			const mockGenerator = {
				generateDescription: vi.fn().mockRejectedValue(new Error('AI Error')),
				extractCompanyInfo: vi.fn(),
				isAvailable: vi.fn(),
				batchGenerateDescriptions: vi.fn(),
			};

			(module as any).descriptionGenerator = mockGenerator;

			const result = await module.collect('example.com');

			expect(result.domain).toBe('example.com');
			expect(result.success).toBe(false);
			expect(result.error).toBe('AI Error');
			expect(result.aiProvider).toBe('fallback');
			expect(result.description.confidence).toBe(0.1);
		});

		it('should throw error when fallback is disabled', async () =>
		{
			const noFallbackConfig = {
				...mockConfig,
				fallbackEnabled: false,
			};

			const noFallbackModule = new DomainDescriptionModule(mockLogger, noFallbackConfig);

			const mockGenerator = {
				generateDescription: vi.fn().mockRejectedValue(new Error('AI Error')),
				extractCompanyInfo: vi.fn(),
				isAvailable: vi.fn(),
				batchGenerateDescriptions: vi.fn(),
			};

			(noFallbackModule as any).descriptionGenerator = mockGenerator;

			await expect(noFallbackModule.collect('example.com')).rejects.toThrow('AI Error');
		});
	});

	describe('generateDescription', () =>
	{
		it('should generate description with custom content', async () =>
		{
			const mockDescription = {
				shortDescription: 'Custom description',
				confidence: 0.8,
				generatedAt: new Date(),
			};

			const mockGenerator = {
				generateDescription: vi.fn().mockResolvedValue(mockDescription),
				extractCompanyInfo: vi.fn(),
				isAvailable: vi.fn(),
				batchGenerateDescriptions: vi.fn(),
			};

			(module as any).descriptionGenerator = mockGenerator;

			const customContent = {
				title: 'Custom Title',
				content: 'Custom content',
			};

			const result = await module.generateDescription('example.com', customContent, 'openai');

			expect(mockGenerator.generateDescription).toHaveBeenCalledWith(
				expect.objectContaining({
					domain: 'example.com',
					title: 'Custom Title',
					content: 'Custom content',
				}),
				'openai',
			);
			expect(result).toEqual(mockDescription);
		});
	});

	describe('extractCompanyInfo', () =>
	{
		it('should extract company information', async () =>
		{
			const mockCompanyInfo = {
				name: 'Test Company',
				industry: 'Technology',
				services: ['Service 1', 'Service 2'],
			};

			const mockGenerator = {
				generateDescription: vi.fn(),
				extractCompanyInfo: vi.fn().mockResolvedValue(mockCompanyInfo),
				isAvailable: vi.fn(),
				batchGenerateDescriptions: vi.fn(),
			};

			(module as any).descriptionGenerator = mockGenerator;

			const result = await module.extractCompanyInfo('example.com', { title: 'Test' });

			expect(result).toEqual(mockCompanyInfo);
		});
	});

	describe('batchProcess', () =>
	{
		it('should process multiple domains in batch', async () =>
		{
			const domains = ['site1.com', 'site2.com', 'site3.com'];

			// Mock successful collection for all domains
			const mockCollect = vi.spyOn(module, 'collect');
			mockCollect
				.mockResolvedValueOnce({
					domain: 'site1.com',
					description: { shortDescription: 'Site 1', confidence: 0.8 } as any,
					processingTime: 100,
					aiProvider: 'openai',
					success: true,
				})
				.mockResolvedValueOnce({
					domain: 'site2.com',
					description: { shortDescription: 'Site 2', confidence: 0.7 } as any,
					processingTime: 150,
					aiProvider: 'openai',
					success: true,
				})
				.mockResolvedValueOnce({
					domain: 'site3.com',
					description: { shortDescription: 'Site 3', confidence: 0.9 } as any,
					processingTime: 120,
					aiProvider: 'openai',
					success: true,
				});

			const results = await module.batchProcess(domains);

			expect(results.size).toBe(3);
			expect(results.get('site1.com')?.success).toBe(true);
			expect(results.get('site2.com')?.success).toBe(true);
			expect(results.get('site3.com')?.success).toBe(true);
		});

		it('should handle individual failures in batch with fallback', async () =>
		{
			const domains = ['good.com', 'bad.com'];

			const mockCollect = vi.spyOn(module, 'collect');
			mockCollect
				.mockResolvedValueOnce({
					domain: 'good.com',
					description: { shortDescription: 'Good site', confidence: 0.8 } as any,
					processingTime: 100,
					aiProvider: 'openai',
					success: true,
				})
				.mockRejectedValueOnce(new Error('Processing failed'));

			const results = await module.batchProcess(domains);

			expect(results.size).toBe(2);
			expect(results.get('good.com')?.success).toBe(true);
			expect(results.get('bad.com')?.success).toBe(false);
			expect(results.get('bad.com')?.error).toBe('Processing failed');
		});

		it('should throw error when batch processing is disabled', async () =>
		{
			const noBatchConfig = {
				...mockConfig,
				enableBatchProcessing: false,
			};

			const noBatchModule = new DomainDescriptionModule(mockLogger, noBatchConfig);

			await expect(noBatchModule.batchProcess(['example.com'])).rejects.toThrow(
				'Batch processing is disabled',
			);
		});
	});

	describe('isAvailable', () =>
	{
		it('should check AI service availability', async () =>
		{
			const mockGenerator = {
				generateDescription: vi.fn(),
				extractCompanyInfo: vi.fn(),
				isAvailable: vi.fn().mockResolvedValue(true),
				batchGenerateDescriptions: vi.fn(),
			};

			(module as any).descriptionGenerator = mockGenerator;

			const isAvailable = await module.isAvailable('openai');

			expect(isAvailable).toBe(true);
			expect(mockGenerator.isAvailable).toHaveBeenCalledWith('openai');
		});

		it('should handle availability check failure', async () =>
		{
			const mockGenerator = {
				generateDescription: vi.fn(),
				extractCompanyInfo: vi.fn(),
				isAvailable: vi.fn().mockRejectedValue(new Error('Service error')),
				batchGenerateDescriptions: vi.fn(),
			};

			(module as any).descriptionGenerator = mockGenerator;

			const isAvailable = await module.isAvailable();

			expect(isAvailable).toBe(false);
		});
	});

	describe('getAvailableProviders', () =>
	{
		it('should return list of configured providers', () =>
		{
			const multiProviderConfig = {
				providers: [
					{ provider: 'openai' as const, model: 'gpt-3.5-turbo', apiKey: 'key1' },
					{ provider: 'anthropic' as const, model: 'claude-3-haiku', apiKey: 'key2' },
					{ provider: 'google' as const, model: 'gemini-pro', apiKey: 'key3' },
				],
			};

			const multiProviderModule = new DomainDescriptionModule(mockLogger, multiProviderConfig);
			const providers = multiProviderModule.getAvailableProviders();

			expect(providers).toEqual(['openai', 'anthropic', 'google']);
		});
	});
});
