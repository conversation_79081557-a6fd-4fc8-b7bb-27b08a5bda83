import {
	describe, test, it, expect, beforeEach, afterEach, vi,
} from 'vitest';
import AdvancedContentModule from '../AdvancedContentModule';
import AdvancedContentAnalyzer from '../../analyzers/AdvancedContentAnalyzer';

// Mock the Logger
vi.mock('@shared', () => ({
	Logger: {
		getLogger: () => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
}));

// Mock the AdvancedContentAnalyzer
vi.mock('../../analyzers/AdvancedContentAnalyzer');

describe('AdvancedContentModule', () =>
{
	let module: AdvancedContentModule;
	let mockAnalyzer: jest.Mocked<AdvancedContentAnalyzer>;

	beforeEach(() =>
	{
		vi.clearAllMocks();
		module = new AdvancedContentModule();
		mockAnalyzer = new AdvancedContentAnalyzer() as jest.Mocked<AdvancedContentAnalyzer>;
	});

	describe('constructor', () =>
	{
		it('should initialize with correct configuration', () =>
		{
			const config = module.getConfig();

			expect(config.name).toBe('advanced-content');
			expect(config.priority).toBe(40);
			expect(config.timeout).toBe(60000);
			expect(config.retryAttempts).toBe(2);
			expect(config.retryDelay).toBe(5000);
		});

		it('should have correct dependencies', () =>
		{
			const config = module.getConfig();

			expect(config.dependencies).toContain('dns');
			expect(config.dependencies).toContain('robots');
			expect(config.dependencies).toContain('homepage');
		});
	});

	describe('collect', () =>
	{
		it('should call analyzer and return results', async () =>
		{
			const mockResult = {
				domain: 'example.com',
				pages: {
					homepage: {
						url: 'https://example.com',
						title: 'Test Page',
						description: 'Test description',
						content: 'Test content',
						headings: ['H1'],
						metaKeywords: ['test'],
						wordCount: 10,
						readabilityScore: 65,
						language: 'en',
						contentHash: 'abc123',
					},
				},
				contentQuality: {
					overallScore: 0.8,
					contentLength: 0.7,
					readabilityScore: 0.9,
					mediaRichness: 0.6,
					structuralQuality: 0.8,
					uniqueness: 1.0,
					freshness: 0.5,
				},
				languageDetection: {
					primary: 'en',
					confidence: 1.0,
					alternatives: [],
				},
				contentMetrics: {
					totalWordCount: 10,
					averageReadability: 65,
					duplicateContentPercentage: 0,
					mediaToTextRatio: 0.1,
					headingStructureScore: 0.8,
					internalLinkDensity: 0.05,
				},
				technicalSEO: {
					hasStructuredData: false,
					structuredDataTypes: [],
					hasCanonicalTags: false,
					hasMetaDescriptions: true,
					hasAltTags: false,
					imageOptimization: 0.5,
				},
				lastAnalyzed: new Date().toISOString(),
			};

			// Mock the analyzer method
			mockAnalyzer.analyzeContent = vi.fn().mockResolvedValue(mockResult);
			(module as any).analyzer = mockAnalyzer;

			const result = await (module as any).collect('example.com');

			expect(mockAnalyzer.analyzeContent).toHaveBeenCalledWith('example.com');
			expect(result).toEqual(mockResult);
		});

		it('should handle analyzer errors', async () =>
		{
			const error = new Error('Analysis failed');
			mockAnalyzer.analyzeContent = vi.fn().mockRejectedValue(error);
			(module as any).analyzer = mockAnalyzer;

			await expect((module as any).collect('example.com')).rejects.toThrow('Analysis failed');
		});
	});

	describe('module integration', () =>
	{
		it('should be compatible with DataCollectionModule interface', () =>
		{
			expect(typeof module.getConfig).toBe('function');
			expect(typeof module.canRun).toBe('function');
			expect(typeof module.execute).toBe('function');
		});

		it('should have lower priority than basic modules', () =>
		{
			// Advanced content analysis should have lower priority (higher number = lower priority)
			// compared to basic modules like DNS, robots.txt, etc.
			const config = module.getConfig();

			expect(config.priority).toBeGreaterThan(30);
		});

		it('should have longer timeout than basic modules', () =>
		{
			// Advanced content analysis needs more time due to multi-page crawling
			const config = module.getConfig();

			expect(config.timeout).toBeGreaterThan(30000);
		});
	});
});
