import type { DomainDescription } from '@shared';

function buildDomainDescription(result: any): DomainDescription
{
	const now = new Date().toISOString();
	return {
		metadata: {
			domain: result.domain,
			tld: result.domain.split('.').pop() || '',
			status: 'active',
			category: { primary: 'uncategorized' },
		},
		overview: {},
		technical: {
			technologies: [],
			security: { sslGrade: result.data.sslAnalysis?.grade },
			dns: {
				a: result.data.dnsAnalysis?.records.A || [],
				aaaa: result.data.dnsAnalysis?.records.AAAA || [],
				mx: (result.data.dnsAnalysis?.records.MX || []).map((m: any) => `${m.priority} ${m.exchange}`),
				cname: result.data.dnsAnalysis?.records.CNAME || [],
				txt: result.data.dnsAnalysis?.records.TXT || [],
				ns: result.data.dnsAnalysis?.records.NS || [],
			},
		},
		seo: {
			title: result.data.homepageAnalysis?.metaTags.title,
			metaDescription: result.data.homepageAnalysis?.metaTags.description,
			sitemap: { present: (result.data.robotsAnalysis?.sitemaps?.length || 0) > 0 },
			robots: { present: !!result.data.robotsAnalysis, policy: 'mixed' },
		},
		reputation: {},
		ranking: {},
		compliance: {},
		crawl: { lastCrawled: now, crawlType: result.crawlType as any, errors: result.status === 'failed' ? 1 : 0 },
	};
}

export { buildDomainDescription };
