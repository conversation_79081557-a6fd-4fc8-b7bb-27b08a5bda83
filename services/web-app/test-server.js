#!/usr/bin/env node

import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

console.log('🚀 Testing ultimate-express server with React SSR...\n');

// Start the server
const server = spawn('tsx', ['src/index.ts'], {
	env: {
		...process.env,
		SERVICE_NAME: 'web-app',
		NODE_ENV: 'development'
	},
	stdio: 'pipe'
});

let serverReady = false;

server.stdout.on('data', (data) =>
{
	const output = data.toString();
	if (output.includes('Web Application Service started on port 3000'))
	{
		serverReady = true;
		console.log('✅ Server started successfully on port 3000');
	}
});

server.stderr.on('data', (data) =>
{
	const output = data.toString();
	if (output.includes('Web Application Service started on port 3000'))
	{
		serverReady = true;
		console.log('✅ Server started successfully on port 3000');
	}
});

// Wait for server to start
await setTimeout(3000);

if (!serverReady)
{
	console.log('❌ Server failed to start');
	server.kill();
	process.exit(1);
}

// Test endpoints
const tests = [
	{
		name: 'Health Check',
		url: 'http://localhost:3000/health',
		expectedStatus: 200
	},
	{
		name: 'Homepage (React SSR)',
		url: 'http://localhost:3000/',
		expectedStatus: 200,
		expectedContent: 'Domain Ranking System'
	},
	{
		name: 'Search Page (React SSR)',
		url: 'http://localhost:3000/search',
		expectedStatus: 200,
		expectedContent: 'Domain Search'
	},
	{
		name: 'Domain Search API',
		url: 'http://localhost:3000/api/domains/search?q=example.com',
		expectedStatus: 200,
		expectedContent: 'example.com'
	},
	{
		name: 'Domain Analysis API',
		url: 'http://localhost:3000/api/domains/example.com/analysis',
		expectedStatus: 200,
		expectedContent: 'example.com'
	}
];

console.log('\n🧪 Running tests...\n');

let passedTests = 0;

for (const test of tests)
{
	try
	{
		const response = await fetch(test.url);
		const text = await response.text();

		if (response.status === test.expectedStatus)
		{
			if (test.expectedContent && !text.includes(test.expectedContent))
			{
				console.log(`❌ ${test.name}: Expected content "${test.expectedContent}" not found`);
			}
			else
			{
				console.log(`✅ ${test.name}: PASSED`);
				passedTests++;
			}
		}
		else
		{
			console.log(`❌ ${test.name}: Expected status ${test.expectedStatus}, got ${response.status}`);
		}
	}
	catch (error)
	{
		console.log(`❌ ${test.name}: ${error.message}`);
	}
}

// Clean up
server.kill();

console.log(`\n📊 Test Results: ${passedTests}/${tests.length} tests passed\n`);

if (passedTests === tests.length)
{
	console.log('🎉 All tests passed! Ultimate-express server with React SSR is working correctly.');
	process.exit(0);
}
else
{
	console.log('❌ Some tests failed.');
	process.exit(1);
}
