import {
	DatabaseManager, logger, ScyllaClient, Maria<PERSON>lient, ManticoreClient,
} from '@shared';

/**
 * Domain Analysis Service
 * Handles domain analysis operations with comprehensive metrics
 */
class DomainAnalysisService
{
	private dbManager: DatabaseManager;

	private scyllaClient: ScyllaClient;

	private mariaClient: MariaClient;

	private manticoreClient: ManticoreClient;

	private logger = logger.getLogger('DomainAnalysisService');

	constructor(dbManager: DatabaseManager)
	{
		this.dbManager = dbManager;
		this.scyllaClient = dbManager.getScyllaClient();
		this.mariaClient = dbManager.getMariaClient();
		this.manticoreClient = dbManager.getManticoreClient();
	}

	/**
	 * Get comprehensive domain analysis
	 */
	async getDomainAnalysis(domain: string): Promise<{
		domain: string;
		globalRank: number;
		categoryRank: number;
		category: string;
		metrics: {
			performance: any;
			security: any;
			seo: any;
			technical: any;
			traffic: any;
		};
		domainInfo: {
			age: number;
			registrar: string;
			registrationDate: string;
			expirationDate: string;
			country: string;
		};
		technologies: string[];
		screenshots: string[];
		subdomains: string[];
		lastUpdated: string;
		crawlStatus: string;
	} | null>
	{
		try
		{
			// Normalize domain
			const normalizedDomain = this.normalizeDomain(domain);

			// Get domain analysis from ScyllaDB
			const analysisQuery = `
				SELECT * FROM domain_analysis
				WHERE domain = ?
				LIMIT 1
			`;

			const analysisResult = await this.scyllaClient.execute(analysisQuery, [normalizedDomain]);

			if (!analysisResult.rows || analysisResult.rows.length === 0)
			{
				return null;
			}

			const row = analysisResult.rows[0];

			// Parse metrics from ScyllaDB maps
			const performanceMetrics = this.parseMetricsMap(row.performance_metrics);
			const securityMetrics = this.parseMetricsMap(row.security_metrics);
			const seoMetrics = this.parseMetricsMap(row.seo_metrics);
			const technicalMetrics = this.parseMetricsMap(row.technical_metrics);
			const contentMetrics = this.parseMetricsMap(row.content_metrics);
			const serverInfo = this.parseMetricsMap(row.server_info);
			const sslCertificate = this.parseMetricsMap(row.ssl_certificate);
			const socialLinks = this.parseMetricsMap(row.social_links);

			// Get additional domain info from MariaDB
			const domainInfo = await this.getDomainWhoisInfo(normalizedDomain);

			return {
				domain: normalizedDomain,
				globalRank: row.global_rank || 0,
				categoryRank: row.category_rank || 0,
				category: row.category || 'Unknown',
				metrics: {
					performance: {
						loadTime: performanceMetrics.load_time || 0,
						firstContentfulPaint: performanceMetrics.fcp || 0,
						largestContentfulPaint: performanceMetrics.lcp || 0,
						cumulativeLayoutShift: performanceMetrics.cls || 0,
						firstInputDelay: performanceMetrics.fid || 0,
						speedIndex: performanceMetrics.speed_index || 0,
						score: this.calculatePerformanceScore(performanceMetrics),
					},
					security: {
						sslGrade: sslCertificate.grade || 'Unknown',
						sslIssuer: sslCertificate.issuer || 'Unknown',
						sslExpiration: sslCertificate.expiration || null,
						securityHeaders: this.parseSecurityHeaders(securityMetrics),
						vulnerabilities: this.parseVulnerabilities(securityMetrics),
						score: this.calculateSecurityScore(securityMetrics, sslCertificate),
					},
					seo: {
						title: seoMetrics.title || '',
						description: seoMetrics.description || '',
						hasRobotsTxt: seoMetrics.robots_txt === 'true',
						hasSitemap: seoMetrics.sitemap === 'true',
						structuredData: seoMetrics.structured_data || '',
						metaKeywords: seoMetrics.meta_keywords || '',
						score: this.calculateSEOScore(seoMetrics),
					},
					technical: {
						pageSize: technicalMetrics.page_size || 0,
						resourceCount: technicalMetrics.resource_count || 0,
						compression: technicalMetrics.compression === 'true',
						httpVersion: technicalMetrics.http_version || 'Unknown',
						serverSoftware: serverInfo.server || 'Unknown',
						cdnProvider: serverInfo.cdn_provider || null,
						score: this.calculateTechnicalScore(technicalMetrics),
					},
					traffic: {
						estimatedVisits: row.traffic_estimate || 0,
						bounceRate: contentMetrics.bounce_rate || 0,
						avgSessionDuration: contentMetrics.avg_session_duration || 0,
						pageViewsPerSession: contentMetrics.page_views_per_session || 0,
					},
				},
				domainInfo: {
					age: row.domain_age_days || 0,
					registrar: domainInfo?.registrar || row.registrar || 'Unknown',
					registrationDate: domainInfo?.registration_date || row.registration_date || '',
					expirationDate: domainInfo?.expiration_date || row.expiration_date || '',
					country: serverInfo.country || domainInfo?.registrant_country || 'Unknown',
				},
				technologies: Array.from(row.technologies || []),
				screenshots: row.screenshot_urls || [],
				subdomains: Array.from(row.subdomains || []),
				socialLinks: socialLinks || {},
				lastUpdated: row.last_crawled ? row.last_crawled.toISOString() : '',
				crawlStatus: row.crawl_status || 'unknown',
			};
		}
		catch (error)
		{
			this.logger.error('Domain analysis failed:', error);
			throw error;
		}
	}

	/**
	 * Compare multiple domains
	 */
	async compareDomains(domains: string[]): Promise<{
		domains: Array<{
			domain: string;
			scores: {
				overall: number;
				performance: number;
				security: number;
				seo: number;
				technical: number;
			};
			globalRank: number;
			category: string;
			trafficEstimate: number;
		}>;
		comparison: {
			winner: string;
			categories: {
				performance: string;
				security: string;
				seo: string;
				technical: string;
			};
		};
		timestamp: string;
	}>
	{
		try
		{
			const normalizedDomains = domains.map(d => this.normalizeDomain(d));

			// Use Manticore for comparison
			const comparisonResult = await this.manticoreClient.compareDomains(normalizedDomains);

			const domainData = comparisonResult.results.map(result => ({
				domain: result.domain,
				scores: {
					overall: result.scores.overall || 0,
					performance: result.scores.performance || 0,
					security: result.scores.security || 0,
					seo: result.scores.seo || 0,
					technical: result.scores.technical || 0,
				},
				globalRank: result.globalRank || 0,
				category: result.category || 'Unknown',
				trafficEstimate: result.trafficEstimate || 0,
			}));

			// Determine winners in each category
			const comparison = {
				winner: this.findOverallWinner(domainData),
				categories: {
					performance: this.findCategoryWinner(domainData, 'performance'),
					security: this.findCategoryWinner(domainData, 'security'),
					seo: this.findCategoryWinner(domainData, 'seo'),
					technical: this.findCategoryWinner(domainData, 'technical'),
				},
			};

			return {
				domains: domainData,
				comparison,
				timestamp: new Date().toISOString(),
			};
		}
		catch (error)
		{
			this.logger.error('Domain comparison failed:', error);
			throw error;
		}
	}

	/**
	 * Get domain ranking explanation
	 */
	async getDomainRankingExplanation(domain: string): Promise<{
		domain: string;
		globalRank: number;
		categoryRank: number;
		scoreBreakdown: {
			overall: number;
			performance: { score: number; factors: string[] };
			security: { score: number; factors: string[] };
			seo: { score: number; factors: string[] };
			technical: { score: number; factors: string[] };
		};
		strengths: string[];
		weaknesses: string[];
		recommendations: string[];
	} | null>
	{
		try
		{
			const analysis = await this.getDomainAnalysis(domain);

			if (!analysis)
			{
				return null;
			}

			const scoreBreakdown = {
				overall: this.calculateOverallScore(analysis.metrics),
				performance: {
					score: analysis.metrics.performance.score,
					factors: this.getPerformanceFactors(analysis.metrics.performance),
				},
				security: {
					score: analysis.metrics.security.score,
					factors: this.getSecurityFactors(analysis.metrics.security),
				},
				seo: {
					score: analysis.metrics.seo.score,
					factors: this.getSEOFactors(analysis.metrics.seo),
				},
				technical: {
					score: analysis.metrics.technical.score,
					factors: this.getTechnicalFactors(analysis.metrics.technical),
				},
			};

			const strengths = this.identifyStrengths(scoreBreakdown);
			const weaknesses = this.identifyWeaknesses(scoreBreakdown);
			const recommendations = this.generateRecommendations(scoreBreakdown, analysis);

			return {
				domain: analysis.domain,
				globalRank: analysis.globalRank,
				categoryRank: analysis.categoryRank,
				scoreBreakdown,
				strengths,
				weaknesses,
				recommendations,
			};
		}
		catch (error)
		{
			this.logger.error('Ranking explanation failed:', error);
			throw error;
		}
	}

	/**
	 * Get domain WHOIS information from MariaDB
	 */
	private async getDomainWhoisInfo(domain: string): Promise<any>
	{
		try
		{
			const query = `
				SELECT registrar, registration_date, expiration_date,
				       registrant_country, registrant_organization
				FROM domain_whois
				WHERE domain = ?
			`;

			const result = await this.mariaClient.execute(query, [domain]);
			return result.length > 0 ? result[0] : null;
		}
		catch (error)
		{
			this.logger.warn('WHOIS info query failed:', error);
			return null;
		}
	}

	/**
	 * Normalize domain name
	 */
	private normalizeDomain(domain: string): string
	{
		return domain.toLowerCase()
			.replace(/^https?:\/\//, '')
			.replace(/^www\./, '')
			.replace(/\/$/, '');
	}

	/**
	 * Parse metrics map from ScyllaDB
	 */
	private parseMetricsMap(metricsMap: any): Record<string, any>
	{
		if (!metricsMap || typeof metricsMap !== 'object')
		{
			return {};
		}
		return metricsMap;
	}

	/**
	 * Calculate performance score
	 */
	private calculatePerformanceScore(metrics: any): number
	{
		const loadTime = parseFloat(metrics.load_time) || 0;
		const fcp = parseFloat(metrics.fcp) || 0;
		const lcp = parseFloat(metrics.lcp) || 0;
		const cls = parseFloat(metrics.cls) || 0;

		// Simple scoring algorithm (0-1 scale)
		let score = 1.0;

		// Penalize slow load times
		if (loadTime > 3) score -= 0.3;
		else if (loadTime > 1.5) score -= 0.1;

		// Penalize poor Core Web Vitals
		if (lcp > 2.5) score -= 0.2;
		if (fcp > 1.8) score -= 0.1;
		if (cls > 0.1) score -= 0.2;

		return Math.max(0, Math.min(1, score));
	}

	/**
	 * Calculate security score
	 */
	private calculateSecurityScore(securityMetrics: any, sslCertificate: any): number
	{
		let score = 0.5; // Base score

		// SSL grade bonus
		const sslGrade = sslCertificate.grade || '';
		if (sslGrade === 'A+') score += 0.3;
		else if (sslGrade === 'A') score += 0.2;
		else if (sslGrade === 'B') score += 0.1;

		// Security headers bonus
		if (securityMetrics.hsts === 'true') score += 0.1;
		if (securityMetrics.csp === 'true') score += 0.1;
		if (securityMetrics.x_frame_options === 'true') score += 0.1;

		return Math.max(0, Math.min(1, score));
	}

	/**
	 * Calculate SEO score
	 */
	private calculateSEOScore(seoMetrics: any): number
	{
		let score = 0.3; // Base score

		if (seoMetrics.title && seoMetrics.title.length > 10) score += 0.2;
		if (seoMetrics.description && seoMetrics.description.length > 50) score += 0.2;
		if (seoMetrics.robots_txt === 'true') score += 0.1;
		if (seoMetrics.sitemap === 'true') score += 0.1;
		if (seoMetrics.structured_data) score += 0.1;

		return Math.max(0, Math.min(1, score));
	}

	/**
	 * Calculate technical score
	 */
	private calculateTechnicalScore(technicalMetrics: any): number
	{
		let score = 0.5; // Base score

		const pageSize = parseInt(technicalMetrics.page_size) || 0;
		if (pageSize < 1000000) score += 0.1; // Under 1MB
		else if (pageSize > 5000000) score -= 0.1; // Over 5MB

		if (technicalMetrics.compression === 'true') score += 0.1;
		if (technicalMetrics.http_version === 'HTTP/2') score += 0.1;

		return Math.max(0, Math.min(1, score));
	}

	/**
	 * Calculate overall score
	 */
	private calculateOverallScore(metrics: any): number
	{
		const weights = {
			performance: 0.25,
			security: 0.20,
			seo: 0.20,
			technical: 0.15,
			traffic: 0.20,
		};

		return (
			metrics.performance.score * weights.performance
			+ metrics.security.score * weights.security
			+ metrics.seo.score * weights.seo
			+ metrics.technical.score * weights.technical
			+ (metrics.traffic.estimatedVisits > 0 ? 0.8 : 0.2) * weights.traffic
		);
	}

	/**
	 * Parse security headers
	 */
	private parseSecurityHeaders(securityMetrics: any): any
	{
		return {
			hsts: securityMetrics.hsts === 'true',
			csp: securityMetrics.csp === 'true',
			xFrameOptions: securityMetrics.x_frame_options === 'true',
			xContentTypeOptions: securityMetrics.x_content_type_options === 'true',
		};
	}

	/**
	 * Parse vulnerabilities
	 */
	private parseVulnerabilities(securityMetrics: any): string[]
	{
		const vulnerabilities = [];
		if (securityMetrics.mixed_content === 'true') vulnerabilities.push('Mixed Content');
		if (securityMetrics.weak_ssl === 'true') vulnerabilities.push('Weak SSL Configuration');
		return vulnerabilities;
	}

	/**
	 * Get performance factors
	 */
	private getPerformanceFactors(performance: any): string[]
	{
		const factors = [];
		if (performance.loadTime < 1.5) factors.push('Fast load time');
		if (performance.firstContentfulPaint < 1.8) factors.push('Good First Contentful Paint');
		if (performance.largestContentfulPaint < 2.5) factors.push('Good Largest Contentful Paint');
		if (performance.cumulativeLayoutShift < 0.1) factors.push('Low layout shift');
		return factors;
	}

	/**
	 * Get security factors
	 */
	private getSecurityFactors(security: any): string[]
	{
		const factors = [];
		if (security.sslGrade === 'A+' || security.sslGrade === 'A') factors.push('Strong SSL certificate');
		if (security.securityHeaders.hsts) factors.push('HSTS enabled');
		if (security.securityHeaders.csp) factors.push('Content Security Policy');
		if (security.vulnerabilities.length === 0) factors.push('No known vulnerabilities');
		return factors;
	}

	/**
	 * Get SEO factors
	 */
	private getSEOFactors(seo: any): string[]
	{
		const factors = [];
		if (seo.title && seo.title.length > 10) factors.push('Good title tag');
		if (seo.description && seo.description.length > 50) factors.push('Good meta description');
		if (seo.hasRobotsTxt) factors.push('Robots.txt present');
		if (seo.hasSitemap) factors.push('Sitemap available');
		return factors;
	}

	/**
	 * Get technical factors
	 */
	private getTechnicalFactors(technical: any): string[]
	{
		const factors = [];
		if (technical.compression) factors.push('Compression enabled');
		if (technical.httpVersion === 'HTTP/2') factors.push('HTTP/2 support');
		if (technical.pageSize < 1000000) factors.push('Optimized page size');
		return factors;
	}

	/**
	 * Find overall winner
	 */
	private findOverallWinner(domains: any[]): string
	{
		return domains.reduce((winner, current) => (current.scores.overall > winner.scores.overall ? current : winner)).domain;
	}

	/**
	 * Find category winner
	 */
	private findCategoryWinner(domains: any[], category: string): string
	{
		return domains.reduce((winner, current) => (current.scores[category] > winner.scores[category] ? current : winner)).domain;
	}

	/**
	 * Identify strengths
	 */
	private identifyStrengths(scoreBreakdown: any): string[]
	{
		const strengths = [];
		if (scoreBreakdown.performance.score > 0.8) strengths.push('Excellent performance');
		if (scoreBreakdown.security.score > 0.8) strengths.push('Strong security');
		if (scoreBreakdown.seo.score > 0.8) strengths.push('Good SEO optimization');
		if (scoreBreakdown.technical.score > 0.8) strengths.push('Solid technical implementation');
		return strengths;
	}

	/**
	 * Identify weaknesses
	 */
	private identifyWeaknesses(scoreBreakdown: any): string[]
	{
		const weaknesses = [];
		if (scoreBreakdown.performance.score < 0.5) weaknesses.push('Poor performance');
		if (scoreBreakdown.security.score < 0.5) weaknesses.push('Security concerns');
		if (scoreBreakdown.seo.score < 0.5) weaknesses.push('SEO needs improvement');
		if (scoreBreakdown.technical.score < 0.5) weaknesses.push('Technical issues');
		return weaknesses;
	}

	/**
	 * Generate recommendations
	 */
	private generateRecommendations(scoreBreakdown: any, analysis: any): string[]
	{
		const recommendations = [];

		if (scoreBreakdown.performance.score < 0.7)
		{
			recommendations.push('Optimize page load speed and Core Web Vitals');
		}

		if (scoreBreakdown.security.score < 0.7)
		{
			recommendations.push('Implement security headers and upgrade SSL certificate');
		}

		if (scoreBreakdown.seo.score < 0.7)
		{
			recommendations.push('Improve meta tags, add sitemap, and optimize content');
		}

		if (scoreBreakdown.technical.score < 0.7)
		{
			recommendations.push('Enable compression, upgrade to HTTP/2, and optimize resources');
		}

		return recommendations;
	}
}

export { DomainAnalysisService };

export default DomainAnalysisService;
