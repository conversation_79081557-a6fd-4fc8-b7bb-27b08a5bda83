import {
	describe, test, expect, beforeEach, vi,
} from 'vitest';
import { ManticoreClient } from '@shared/database/ManticoreClient';
import { CachingService } from '@shared/services/CachingService';
import { DomainSearchService } from '../DomainSearchService';

// Mock dependencies
vi.mock('@shared/database/ManticoreClient');
vi.mock('@shared/services/CachingService');

const MockedManticoreClient = vi.mocked(ManticoreClient, true);
const MockedCachingService = vi.mocked(CachingService, true);

describe('DomainSearchService', () =>
{
	let searchService: DomainSearchService;
	let mockManticoreClient: any;
	let mockCachingService: any;

	beforeEach(() =>
	{
		vi.clearAllMocks();

		mockManticoreClient = new MockedManticoreClient();
		mockCachingService = new MockedCachingService();

		MockedManticoreClient.mockImplementation(() => mockManticoreClient);
		MockedCachingService.mockImplementation(() => mockCachingService);

		// Mock Manticore client methods
		mockManticoreClient.search = vi.fn();
		mockManticoreClient.connect = vi.fn();
		mockManticoreClient.disconnect = vi.fn();

		// Mock caching service methods
		mockCachingService.get = vi.fn();
		mockCachingService.set = vi.fn();
		mockCachingService.generateSearchKey = vi.fn();

		searchService = new DomainSearchService();
	});

	describe('searchDomains', () =>
	{
		test('should search domains with basic query', async () =>
		{
			const mockSearchResults = {
				hits: [
					{
						domain: 'example.com',
						title: 'Example Domain',
						description: 'A sample domain',
						global_rank: 1,
						overall_score: 0.95,
						category: 'technology',
					},
					{
						domain: 'test.com',
						title: 'Test Domain',
						description: 'Another test domain',
						global_rank: 2,
						overall_score: 0.90,
						category: 'technology',
					},
				],
				total: 2,
				facets: {
					category: { technology: 2 },
					country: { US: 2 },
				},
			};

			mockManticoreClient.search.mockResolvedValue(mockSearchResults);
			mockCachingService.get.mockResolvedValue(null); // No cache hit

			const result = await searchService.searchDomains('technology', {
				limit: 20,
				offset: 0,
			});

			expect(mockManticoreClient.search).toHaveBeenCalledWith({
				index: 'domains_index',
				query: {
					match: {
						domain: 'technology',
						title: 'technology',
						description: 'technology',
						content: 'technology',
					},
				},
				filter: {},
				sort: [{ global_rank: { order: 'asc' } }],
				limit: 20,
				offset: 0,
				facet: {
					category: {},
					country: {},
					technologies: {},
					ssl_grade: {},
				},
			});

			expect(result.domains).toHaveLength(2);
			expect(result.totalResults).toBe(2);
			expect(result.facets).toBeDefined();
		});

		test('should apply search filters correctly', async () =>
		{
			const mockSearchResults = {
				hits: [
					{
						domain: 'tech-site.com',
						category: 'technology',
						country: 'US',
						ssl_grade: 'A',
					},
				],
				total: 1,
				facets: {},
			};

			mockManticoreClient.search.mockResolvedValue(mockSearchResults);
			mockCachingService.get.mockResolvedValue(null);

			const filters = {
				category: 'technology',
				country: 'US',
				minRank: 1,
				maxRank: 100,
				sslGrade: 'A',
				technologies: ['react', 'node.js'],
			};

			await searchService.searchDomains('tech', filters);

			expect(mockManticoreClient.search).toHaveBeenCalledWith(
				expect.objectContaining({
					filter: {
						category: 'technology',
						country: 'US',
						global_rank: { gte: 1, lte: 100 },
						ssl_grade: 'A',
						technologies: { in: ['react', 'node.js'] },
					},
				}),
			);
		});

		test('should handle different sort options', async () =>
		{
			const mockSearchResults = { hits: [], total: 0, facets: {} };
			mockManticoreClient.search.mockResolvedValue(mockSearchResults);
			mockCachingService.get.mockResolvedValue(null);

			// Test different sort options
			const sortOptions = [
				{ sort: 'rank', expected: [{ global_rank: { order: 'asc' } }] },
				{ sort: 'score', expected: [{ overall_score: { order: 'desc' } }] },
				{ sort: 'performance', expected: [{ performance_score: { order: 'desc' } }] },
				{ sort: 'security', expected: [{ security_score: { order: 'desc' } }] },
			];

			for (const { sort, expected } of sortOptions)
			{
				await searchService.searchDomains('test', { sort });

				expect(mockManticoreClient.search).toHaveBeenCalledWith(
					expect.objectContaining({
						sort: expected,
					}),
				);
			}
		});

		test('should use cached results when available', async () =>
		{
			const cachedResults = {
				domains: [{ domain: 'cached.com', rank: 1 }],
				totalResults: 1,
				facets: {},
				cached: true,
			};

			mockCachingService.get.mockResolvedValue(cachedResults);
			mockCachingService.generateSearchKey.mockReturnValue('search:key:123');

			const result = await searchService.searchDomains('cached query');

			expect(mockCachingService.get).toHaveBeenCalledWith('search:key:123');
			expect(mockManticoreClient.search).not.toHaveBeenCalled();
			expect(result).toEqual(cachedResults);
		});

		test('should cache search results', async () =>
		{
			const mockSearchResults = {
				hits: [{ domain: 'example.com', global_rank: 1 }],
				total: 1,
				facets: {},
			};

			mockManticoreClient.search.mockResolvedValue(mockSearchResults);
			mockCachingService.get.mockResolvedValue(null);
			mockCachingService.generateSearchKey.mockReturnValue('search:key:456');

			await searchService.searchDomains('test query');

			expect(mockCachingService.set).toHaveBeenCalledWith(
				'search:key:456',
				expect.objectContaining({
					domains: expect.any(Array),
					totalResults: 1,
					facets: {},
				}),
				1800, // 30 minutes TTL
			);
		});
	});

	describe('getTopDomains', () =>
	{
		test('should get top domains by category', async () =>
		{
			const mockResults = {
				hits: [
					{ domain: 'top1.com', global_rank: 1, category: 'technology' },
					{ domain: 'top2.com', global_rank: 2, category: 'technology' },
				],
				total: 2,
			};

			mockManticoreClient.search.mockResolvedValue(mockResults);
			mockCachingService.get.mockResolvedValue(null);

			const result = await searchService.getTopDomains('technology', { limit: 50 });

			expect(mockManticoreClient.search).toHaveBeenCalledWith({
				index: 'domains_index',
				query: { match_all: {} },
				filter: { category: 'technology' },
				sort: [{ global_rank: { order: 'asc' } }],
				limit: 50,
			});

			expect(result.domains).toHaveLength(2);
			expect(result.category).toBe('technology');
		});

		test('should get global top domains when no category specified', async () =>
		{
			const mockResults = {
				hits: [
					{ domain: 'global1.com', global_rank: 1 },
					{ domain: 'global2.com', global_rank: 2 },
				],
				total: 2,
			};

			mockManticoreClient.search.mockResolvedValue(mockResults);
			mockCachingService.get.mockResolvedValue(null);

			const result = await searchService.getTopDomains();

			expect(mockManticoreClient.search).toHaveBeenCalledWith({
				index: 'domains_index',
				query: { match_all: {} },
				filter: {},
				sort: [{ global_rank: { order: 'asc' } }],
				limit: 100,
			});

			expect(result.domains).toHaveLength(2);
			expect(result.category).toBe('global');
		});
	});

	describe('compareDomains', () =>
	{
		test('should compare multiple domains', async () =>
		{
			const domains = ['example.com', 'test.com', 'demo.org'];
			const mockResults = {
				hits: [
					{
						domain: 'example.com',
						overall_score: 0.95,
						performance_score: 0.90,
						security_score: 0.95,
						seo_score: 0.85,
					},
					{
						domain: 'test.com',
						overall_score: 0.80,
						performance_score: 0.75,
						security_score: 0.85,
						seo_score: 0.80,
					},
				],
				total: 2,
			};

			mockManticoreClient.search.mockResolvedValue(mockResults);

			const result = await searchService.compareDomains(domains);

			expect(mockManticoreClient.search).toHaveBeenCalledWith({
				index: 'domains_index',
				query: {
					terms: { domain: domains },
				},
				sort: [{ overall_score: { order: 'desc' } }],
			});

			expect(result.comparison).toHaveLength(2);
			expect(result.metrics).toBeDefined();
			expect(result.recommendations).toBeDefined();
		});

		test('should generate comparison metrics', async () =>
		{
			const mockResults = {
				hits: [
					{
						domain: 'high-score.com',
						overall_score: 0.95,
						performance_score: 0.90,
					},
					{
						domain: 'low-score.com',
						overall_score: 0.60,
						performance_score: 0.55,
					},
				],
				total: 2,
			};

			mockManticoreClient.search.mockResolvedValue(mockResults);

			const result = await searchService.compareDomains(['high-score.com', 'low-score.com']);

			expect(result.metrics.bestPerforming).toBe('high-score.com');
			expect(result.metrics.averageScore).toBeCloseTo(0.775, 3);
			expect(result.metrics.scoreRange).toBeCloseTo(0.35, 3);
		});
	});

	describe('error handling', () =>
	{
		test('should handle Manticore search errors', async () =>
		{
			mockManticoreClient.search.mockRejectedValue(new Error('Search service unavailable'));
			mockCachingService.get.mockResolvedValue(null);

			await expect(searchService.searchDomains('test query'))
				.rejects.toThrow('Search service unavailable');
		});

		test('should handle invalid search parameters', async () =>
		{
			await expect(searchService.searchDomains(''))
				.rejects.toThrow('Search query cannot be empty');

			await expect(searchService.searchDomains('test', { limit: -1 }))
				.rejects.toThrow('Invalid limit parameter');

			await expect(searchService.searchDomains('test', { offset: -1 }))
				.rejects.toThrow('Invalid offset parameter');
		});

		test('should handle empty comparison domain list', async () =>
		{
			await expect(searchService.compareDomains([]))
				.rejects.toThrow('At least one domain is required for comparison');
		});
	});

	describe('search suggestions', () =>
	{
		test('should provide search suggestions', async () =>
		{
			const mockSuggestions = {
				hits: [
					{ domain: 'technology-blog.com', title: 'Technology Blog' },
					{ domain: 'tech-news.com', title: 'Tech News' },
				],
			};

			mockManticoreClient.search.mockResolvedValue(mockSuggestions);

			const suggestions = await searchService.getSearchSuggestions('tech');

			expect(mockManticoreClient.search).toHaveBeenCalledWith({
				index: 'domains_index',
				query: {
					match: {
						domain: 'tech',
						title: 'tech',
					},
				},
				limit: 10,
			});

			expect(suggestions).toHaveLength(2);
			expect(suggestions[0]).toEqual({
				domain: 'technology-blog.com',
				title: 'Technology Blog',
			});
		});
	});
});
