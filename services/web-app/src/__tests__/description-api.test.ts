import {
	describe,
	it,
	expect,
	vi,
	beforeEach,
	afterEach,
} from 'vitest';

vi.mock('xss', () => ({
	default: (s: string) => s,
}));
vi.mock('ultimate-express', async () =>
{
	const ue = await import('ultimate-express');
	const app = ue.default;
	return {
		default: app,
		Router: app.Router,
		json: () => (req: any, res: any, next: any) => next(),
	};
});

vi.mock('@shared', async (orig) =>
{
	const mod = await orig();
	return {
		...mod,
		Logger: {
			getLogger: () => ({
				info() {},
				warn() {},
				error() {},
				debug() {},
			}),
			configure: () => {},
		},
		DatabaseManager: class
		{
			async initialize()
			{
				// Empty implementation for testing
			}

			getScyllaClient()
			{
				return {
					execute: vi.fn(async (_q: string, [domain]: any[]) =>
					{
						if (domain === 'missing.com') return { rows: [] };
						if (domain === 'invalid.com') return { rows: [{ domain, last_crawled: Date.now() }] };
						return { rows: [{ domain, category: 'general', last_crawled: Date.now() }] };
					}),
				};
			}
		},
		DomainDescriptionValidator: {
			get: () => new (class
			{
				assert(obj: any)
				{
					if (!obj.metadata?.category?.primary) throw new Error('invalid');
				}
			})(),
		},
	};
});

const getHandler = async () =>
{
	const { default: descriptionRouter } = await import('../routes/description');
	// Extract the registered handlers; ultimate-express router keeps stack similar to ultimate-express
	const layer = (descriptionRouter as any).stack?.find((l: any) => l.route?.path === '/:domain/description' && l.route?.methods?.get);
	if (!layer) throw new Error('handler not found');
	return layer.route.stack[0].handle as (req: any, res: any) => Promise<void>;
};

const OLD = process.env.VALIDATE_DESCRIPTIONS;

describe('GET /api/domains/:domain/description (router)', () =>
{
	beforeEach(() =>
	{
		vi.restoreAllMocks();
	});

	afterEach(() =>
	{
		process.env.VALIDATE_DESCRIPTIONS = OLD;
	});

	it('200 with valid description', async () =>
	{
		process.env.VALIDATE_DESCRIPTIONS = '1';
		const handler = await getHandler();
		const req: any = { params: { domain: 'ok.com' } };
		const res: any = {
			statusCode: 0,
			body: null,
			status(c: number)
			{
				this.statusCode = c;
				return this;
			},
			json(p: any)
			{
				this.body = p;
				return this;
			},
		};
		await handler(req, res);

		expect(res.statusCode).toBe(200);
		expect(res.body.metadata.domain).toBe('ok.com');
		expect(res.body.metadata.category.primary).toBeDefined();
	});

	it('404 when not found', async () =>
	{
		const handler = await getHandler();
		const req: any = { params: { domain: 'missing.com' } };
		const res: any = {
			statusCode: 0,
			body: null,
			status(c: number)
			{
				this.statusCode = c;
				return this;
			},
			json(p: any)
			{
				this.body = p;
				return this;
			},
		};
		await handler(req, res);

		expect(res.statusCode).toBe(404);
	});

	it('500 when invalid description under validation', async () =>
	{
		process.env.VALIDATE_DESCRIPTIONS = '1';
		const handler = await getHandler();
		const req: any = { params: { domain: 'invalid.com' } };
		const res: any = {
			statusCode: 0,
			body: null,
			status(c: number)
			{
				this.statusCode = c;
				return this;
			},
			json(p: any)
			{
				this.body = p;
				return this;
			},
		};
		await handler(req, res);

		expect(res.statusCode).toBe(500);
	});
});
