import { Router, Request, Response } from 'ultimate-express';
import { HealthChecker } from '@shared/monitoring/HealthChecker';
import { MetricsCollector } from '@shared/monitoring/MetricsCollector';
import Logger from '@shared/utils/Logger';

const router = Router();
const logger = Logger.getLogger('HealthRoutes');
const healthChecker = new HealthChecker('web-app', process.env.npm_package_version || '1.0.0');
const metricsCollector = new MetricsCollector();

/**
 * Basic health check endpoint for load balancers
 * GET /health
 */
router.get('/', async (req: Request, res: Response) =>
{
	try
	{
		const health = await healthChecker.getSimpleHealth();

		const statusCode = health.status === 'healthy' ? 200
						  : health.status === 'degraded' ? 200 : 503;

		res.status(statusCode).json(health);
	}
	catch (error)
	{
		logger.error('Health check failed:', error);
		res.status(503).json({
			status: 'unhealthy',
			timestamp: new Date().toISOString(),
			error: 'Health check failed',
		});
	}
});

/**
 * Detailed health check with service status
 * GET /health/detailed
 */
router.get('/detailed', async (req: Request, res: Response) =>
{
	try
	{
		const includeMetrics = req.query.metrics === 'true';
		const health = await healthChecker.getHealthStatus(includeMetrics);

		const statusCode = health.status === 'healthy' ? 200
						  : health.status === 'degraded' ? 200 : 503;

		res.status(statusCode).json(health);
	}
	catch (error)
	{
		logger.error('Detailed health check failed:', error);
		res.status(503).json({
			status: 'unhealthy',
			timestamp: new Date().toISOString(),
			error: 'Detailed health check failed',
			message: error.message,
		});
	}
});

/**
 * Readiness probe for Kubernetes
 * GET /health/ready
 */
router.get('/ready', async (req: Request, res: Response) =>
{
	try
	{
		const isReady = await healthChecker.isReady();

		if (isReady)
		{
			res.status(200).json({
				status: 'ready',
				timestamp: new Date().toISOString(),
			});
		}
		else
		{
			res.status(503).json({
				status: 'not_ready',
				timestamp: new Date().toISOString(),
			});
		}
	}
	catch (error)
	{
		logger.error('Readiness check failed:', error);
		res.status(503).json({
			status: 'not_ready',
			timestamp: new Date().toISOString(),
			error: error.message,
		});
	}
});

/**
 * Liveness probe for Kubernetes
 * GET /health/live
 */
router.get('/live', async (req: Request, res: Response) =>
{
	try
	{
		const isAlive = await healthChecker.isAlive();

		if (isAlive)
		{
			res.status(200).json({
				status: 'alive',
				timestamp: new Date().toISOString(),
			});
		}
		else
		{
			res.status(503).json({
				status: 'dead',
				timestamp: new Date().toISOString(),
			});
		}
	}
	catch (error)
	{
		logger.error('Liveness check failed:', error);
		res.status(503).json({
			status: 'dead',
			timestamp: new Date().toISOString(),
			error: error.message,
		});
	}
});

/**
 * Performance metrics endpoint
 * GET /health/metrics
 */
router.get('/metrics', async (req: Request, res: Response) =>
{
	try
	{
		const summary = await metricsCollector.getMetricsSummary();

		res.status(200).json({
			timestamp: new Date().toISOString(),
			summary,
		});
	}
	catch (error)
	{
		logger.error('Metrics retrieval failed:', error);
		res.status(500).json({
			error: 'Failed to retrieve metrics',
			timestamp: new Date().toISOString(),
			message: error.message,
		});
	}
});

/**
 * Specific metric data endpoint
 * GET /health/metrics/:name
 */
router.get('/metrics/:name', async (req: Request, res: Response) =>
{
	try
	{
		const { name } = req.params;
		const startTime = parseInt(req.query.start as string) || (Date.now() - 3600000); // Default: 1 hour ago
		const endTime = parseInt(req.query.end as string) || Date.now();

		const metrics = await metricsCollector.getMetrics(name, startTime, endTime);

		res.status(200).json({
			metric: name,
			timeRange: { start: startTime, end: endTime },
			data: metrics,
			count: metrics.length,
		});
	}
	catch (error)
	{
		logger.error(`Failed to retrieve metric ${req.params.name}:`, error);
		res.status(500).json({
			error: 'Failed to retrieve metric data',
			timestamp: new Date().toISOString(),
			message: error.message,
		});
	}
});

/**
 * System information endpoint
 * GET /health/info
 */
router.get('/info', async (req: Request, res: Response) =>
{
	try
	{
		const os = await import('os');
		const process = await import('process');

		const info = {
			service: 'web-app',
			version: process.env.npm_package_version || '1.0.0',
			environment: process.env.NODE_ENV || 'development',
			nodeVersion: process.version,
			platform: os.platform(),
			architecture: os.arch(),
			hostname: os.hostname(),
			uptime: process.uptime(),
			memory: process.memoryUsage(),
			cpus: os.cpus().length,
			loadAverage: os.loadavg(),
			timestamp: new Date().toISOString(),
		};

		res.status(200).json(info);
	}
	catch (error)
	{
		logger.error('System info retrieval failed:', error);
		res.status(500).json({
			error: 'Failed to retrieve system information',
			timestamp: new Date().toISOString(),
			message: error.message,
		});
	}
});

/**
 * Database connection status
 * GET /health/database
 */
router.get('/database', async (req: Request, res: Response) =>
{
	try
	{
		const dbHealth = await healthChecker.getHealthStatus(false);
		const databaseServices = Object.entries(dbHealth.services)
			.filter(([name]) => ['scylladb', 'mariadb', 'redis', 'manticore'].includes(name))
			.reduce((acc, [name, health]) =>
			{
				acc[name] = health;
				return acc;
			}, {} as any);

		const allHealthy = Object.values(databaseServices).every(
			(service: any) => service.status === 'healthy',
		);

		res.status(allHealthy ? 200 : 503).json({
			status: allHealthy ? 'healthy' : 'degraded',
			timestamp: new Date().toISOString(),
			databases: databaseServices,
		});
	}
	catch (error)
	{
		logger.error('Database health check failed:', error);
		res.status(503).json({
			status: 'unhealthy',
			timestamp: new Date().toISOString(),
			error: error.message,
		});
	}
});

/**
 * Cleanup old metrics
 * POST /health/cleanup
 */
router.post('/cleanup', async (req: Request, res: Response) =>
{
	try
	{
		const days = parseInt(req.body.days) || 7;
		const cleanedCount = await metricsCollector.cleanupOldMetrics(days);

		res.status(200).json({
			message: 'Cleanup completed',
			cleanedMetrics: cleanedCount,
			olderThanDays: days,
			timestamp: new Date().toISOString(),
		});
	}
	catch (error)
	{
		logger.error('Metrics cleanup failed:', error);
		res.status(500).json({
			error: 'Failed to cleanup metrics',
			timestamp: new Date().toISOString(),
			message: error.message,
		});
	}
});

export default router;
