import { defineConfig } from 'vitest/config';

export default defineConfig(async () =>
{
	const tsconfigPaths = (await import('vite-tsconfig-paths')).default;
	return {
		plugins: [tsconfigPaths()],
		test: {
			environment: 'node',
			include: ['src/**/__tests__/**/*.test.ts', 'src/**/?(*.)+(spec|test).ts'],
			coverage: {
				provider: 'v8',
				reporter: ['text', 'lcov', 'html'],
				reportsDirectory: 'coverage',
				include: ['src/**/*.ts'],
				exclude: ['src/**/*.d.ts', 'src/**/__tests__/**', 'src/**/index.ts'],
			},
			testTimeout: 10000,
		},
	};
});
