import {
	describe, it, expect, beforeEach, vi,
} from 'vitest';
import { DatabaseManager, JobQueue } from '@shared';
import {
	RankingUpdateService, RankingUpdateTrigger, BatchRankingUpdate, RankingTrendAnalysis,
} from '../RankingUpdateService';

// Mock dependencies
vi.mock('@shared', () => ({
	DatabaseManager: vi.fn().mockImplementation(() => ({
		initialize: vi.fn().mockResolvedValue(undefined),
		getScyllaClient: vi.fn().mockReturnValue({
			execute: vi.fn().mockResolvedValue({ rows: [] }),
		}),
		getRedisClient: vi.fn().mockReturnValue({
			get: vi.fn().mockResolvedValue(null),
			setex: vi.fn().mockResolvedValue('OK'),
		}),
		healthCheck: vi.fn().mockResolvedValue(true),
	})),
	JobQueue: vi.fn().mockImplementation(() => ({
		initialize: vi.fn().mockResolvedValue(undefined),
		createConsumer: vi.fn().mockResolvedValue({}),
		publishJob: vi.fn().mockResolvedValue('job_123'),
		publishRankingUpdateJob: vi.fn().mockResolvedValue('job_123'),
		publishManticoreSyncJob: vi.fn().mockResolvedValue('job_123'),
		healthCheck: vi.fn().mockResolvedValue(true),
	})),
	Logger: {
		getLogger: vi.fn().mockReturnValue({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		}),
	},
	Config: {
		getAll: vi.fn().mockReturnValue({}),
	},
	Constants: {
		JOB_QUEUES: {
			RANKING_UPDATE: 'queue:ranking:update',
		},
	},
}));

describe('RankingUpdateService', () =>
{
	let service: RankingUpdateService;
	let mockDbManager: vi.Mocked<DatabaseManager>;
	let mockJobQueue: vi.Mocked<JobQueue>;

	beforeEach(() =>
	{
		vi.clearAllMocks();
		service = new RankingUpdateService();
		mockDbManager = (service as any).dbManager;
		mockJobQueue = (service as any).jobQueue;
	});

	describe('initialization', () =>
	{
		it('should initialize successfully', async () =>
		{
			await service.initialize();

			expect(mockDbManager.initialize).toHaveBeenCalled();
			expect(mockJobQueue.initialize).toHaveBeenCalled();
			expect(mockJobQueue.createConsumer).toHaveBeenCalledTimes(3);
		});

		it('should handle initialization errors', async () =>
		{
			mockDbManager.initialize.mockRejectedValue(new Error('DB connection failed'));

			await expect(service.initialize()).rejects.toThrow('DB connection failed');
		});
	});

	describe('triggerDomainRankingUpdate', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should trigger ranking update for a single domain', async () =>
		{
			const domain = 'example.com';
			const trigger: Omit<RankingUpdateTrigger, 'domain'> = {
				type: 'manual',
				priority: 'high',
			};

			const jobId = await service.triggerDomainRankingUpdate(domain, trigger);

			expect(jobId).toBe('job_123');
			expect(mockJobQueue.publishRankingUpdateJob).toHaveBeenCalledWith(domain, 'global');
		});

		it('should handle trigger errors', async () =>
		{
			mockJobQueue.publishRankingUpdateJob.mockRejectedValue(new Error('Queue error'));

			await expect(service.triggerDomainRankingUpdate('example.com')).rejects.toThrow('Queue error');
		});
	});

	describe('triggerBatchRankingUpdate', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should trigger batch ranking update for multiple domains', async () =>
		{
			const domains = ['example1.com', 'example2.com', 'example3.com'];
			const rankingType = 'global';

			const batchId = await service.triggerBatchRankingUpdate(domains, rankingType);

			expect(batchId).toMatch(/^batch_\d+_[a-z0-9]+$/);
			expect(mockJobQueue.publishJob).toHaveBeenCalledWith(
				'queue:ranking:batch',
				expect.objectContaining({
					batchId,
					domains,
					rankingType,
				}),
				expect.objectContaining({
					priority: 5, // medium priority
				}),
			);
		});

		it('should trigger category-specific batch update', async () =>
		{
			const domains = ['tech1.com', 'tech2.com'];
			const rankingType = 'category';
			const category = 'technology';

			const batchId = await service.triggerBatchRankingUpdate(domains, rankingType, category, 'high');

			expect(batchId).toMatch(/^batch_\d+_[a-z0-9]+$/);
			expect(mockJobQueue.publishJob).toHaveBeenCalledWith(
				'queue:ranking:batch',
				expect.objectContaining({
					batchId,
					domains,
					rankingType,
					category,
				}),
				expect.objectContaining({
					priority: 10, // high priority
				}),
			);
		});

		it('should track active batches', async () =>
		{
			const domains = ['example.com'];
			const batchId = await service.triggerBatchRankingUpdate(domains);

			const batchStatus = service.getBatchStatus(batchId);

			expect(batchStatus).toBeDefined();
			expect(batchStatus?.domains).toEqual(domains);
			expect(batchStatus?.status).toBe('pending');
		});
	});

	describe('calculateRankingTrend', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should calculate ranking trend with historical data', async () =>
		{
			const domain = 'example.com';
			const mockHistoryData = [
				{
					domain, date: '2024-01-01', ranking_type: 'global', rank: 100, score: 0.8,
				},
				{
					domain, date: '2024-01-02', ranking_type: 'global', rank: 95, score: 0.82,
				},
				{
					domain, date: '2024-01-03', ranking_type: 'global', rank: 90, score: 0.85,
				},
			];

			mockDbManager.getScyllaClient().execute.mockResolvedValue({
				rows: mockHistoryData,
			});

			const trendAnalysis = await service.calculateRankingTrend(domain, 'global', '30d');

			expect(trendAnalysis).toEqual({
				domain,
				period: '30d',
				trendDirection: 'up', // Rank improved from 100 to 90
				averageRank: 95, // (100 + 95 + 90) / 3
				bestRank: 90,
				worstRank: 100,
				volatility: expect.any(Number),
				rankChanges: expect.arrayContaining([
					expect.objectContaining({
						domain,
						rank: 100,
						score: 0.8,
					}),
				]),
			});
		});

		it('should handle empty history data', async () =>
		{
			mockDbManager.getScyllaClient().execute.mockResolvedValue({ rows: [] });

			const trendAnalysis = await service.calculateRankingTrend('example.com', 'global', '30d');

			expect(trendAnalysis).toEqual({
				domain: 'example.com',
				period: '30d',
				trendDirection: 'stable',
				averageRank: 0,
				bestRank: 0,
				worstRank: 0,
				volatility: 0,
				rankChanges: [],
			});
		});

		it('should calculate trend direction correctly', async () =>
		{
			const domain = 'declining.com';
			const mockHistoryData = [
				{
					domain, date: '2024-01-01', ranking_type: 'global', rank: 50, score: 0.9,
				},
				{
					domain, date: '2024-01-02', ranking_type: 'global', rank: 60, score: 0.85,
				},
				{
					domain, date: '2024-01-03', ranking_type: 'global', rank: 70, score: 0.8,
				},
			];

			mockDbManager.getScyllaClient().execute.mockResolvedValue({
				rows: mockHistoryData,
			});

			const trendAnalysis = await service.calculateRankingTrend(domain, 'global', '30d');

			expect(trendAnalysis.trendDirection).toBe('down'); // Rank worsened from 50 to 70
		});
	});

	describe('batch management', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should track active batches', async () =>
		{
			const domains1 = ['example1.com'];
			const domains2 = ['example2.com'];

			const batchId1 = await service.triggerBatchRankingUpdate(domains1);
			const batchId2 = await service.triggerBatchRankingUpdate(domains2);

			const activeBatches = service.getActiveBatches();

			expect(activeBatches).toHaveLength(2);
			expect(activeBatches.map(b => b.batchId)).toContain(batchId1);
			expect(activeBatches.map(b => b.batchId)).toContain(batchId2);
		});

		it('should clean up completed batches', async () =>
		{
			const domains = ['example.com'];
			const batchId = await service.triggerBatchRankingUpdate(domains);

			// Simulate completed batch
			const batch = service.getBatchStatus(batchId);
			if (batch)
			{
				batch.status = 'completed';
				batch.completedAt = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
			}

			service.cleanupCompletedBatches();

			const batchAfterCleanup = service.getBatchStatus(batchId);

			expect(batchAfterCleanup).toBeUndefined();
		});
	});

	describe('health check', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should return true when all dependencies are healthy', async () =>
		{
			const isHealthy = await service.healthCheck();

			expect(isHealthy).toBe(true);
		});

		it('should return false when database is unhealthy', async () =>
		{
			mockDbManager.healthCheck.mockResolvedValue(false);

			const isHealthy = await service.healthCheck();

			expect(isHealthy).toBe(false);
		});

		it('should return false when job queue is unhealthy', async () =>
		{
			mockJobQueue.healthCheck.mockResolvedValue(false);

			const isHealthy = await service.healthCheck();

			expect(isHealthy).toBe(false);
		});

		it('should handle health check errors', async () =>
		{
			mockDbManager.healthCheck.mockRejectedValue(new Error('Health check failed'));

			const isHealthy = await service.healthCheck();

			expect(isHealthy).toBe(false);
		});
	});

	describe('priority handling', () =>
	{
		beforeEach(async () =>
		{
			await service.initialize();
		});

		it('should convert priority strings to numeric values correctly', async () =>
		{
			const domains = ['example.com'];

			// Test low priority
			await service.triggerBatchRankingUpdate(domains, 'global', undefined, 'low');

			expect(mockJobQueue.publishJob).toHaveBeenLastCalledWith(
				expect.any(String),
				expect.any(Object),
				expect.objectContaining({ priority: 1 }),
			);

			// Test medium priority
			await service.triggerBatchRankingUpdate(domains, 'global', undefined, 'medium');

			expect(mockJobQueue.publishJob).toHaveBeenLastCalledWith(
				expect.any(String),
				expect.any(Object),
				expect.objectContaining({ priority: 5 }),
			);

			// Test high priority
			await service.triggerBatchRankingUpdate(domains, 'global', undefined, 'high');

			expect(mockJobQueue.publishJob).toHaveBeenLastCalledWith(
				expect.any(String),
				expect.any(Object),
				expect.objectContaining({ priority: 10 }),
			);
		});
	});
});
