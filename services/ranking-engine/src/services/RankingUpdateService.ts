import {
	<PERSON><PERSON>ana<PERSON>, JobQueue, Logger, Config, Constants, DomainDescriptionValidator,
} from '@shared';
import { CompositeRanker, DomainData, RankingResult } from '../algorithms/CompositeRanker';

const logger = Logger.getLogger('RankingUpdateService');

export interface RankingUpdateTrigger
{
	type: 'domain_updated' | 'scheduled' | 'manual' | 'batch';
	domain?: string;
	category?: string;
	priority: 'low' | 'medium' | 'high';
	scheduledAt?: Date;
	metadata?: Record<string, any>;
}

export interface BatchRankingUpdate
{
	batchId: string;
	domains: string[];
	rankingType: 'global' | 'category';
	category?: string;
	status: 'pending' | 'processing' | 'completed' | 'failed';
	startedAt?: Date;
	completedAt?: Date;
	processedCount: number;
	totalCount: number;
	errors: string[];
}

export interface RankingHistoryEntry
{
	domain: string;
	date: string;
	rankingType: string;
	rank: number;
	score: number;
	previousRank?: number;
	rankChange?: number;
	category?: string;
}

export interface RankingTrendAnalysis
{
	domain: string;
	period: string;
	trendDirection: 'up' | 'down' | 'stable';
	averageRank: number;
	bestRank: number;
	worstRank: number;
	volatility: number;
	rankChanges: RankingHistoryEntry[];
}

/**
 * Service for managing ranking updates, batch processing, and history tracking
 */
class RankingUpdateService
{
	private dbManager: DatabaseManager;

	private jobQueue: JobQueue;

	private compositeRanker: CompositeRanker;

	private activeBatches = new Map<string, BatchRankingUpdate>();

	private updateTriggers = new Map<string, RankingUpdateTrigger>();

	constructor()
	{
		this.dbManager = new DatabaseManager();
		this.jobQueue = new JobQueue();
		this.compositeRanker = new CompositeRanker();
	}

	/**
	 * Initialize the ranking update service
	 */
	async initialize(): Promise<void>
	{
		try
		{
			logger.info('Initializing Ranking Update Service...');

			// Initialize dependencies
			await this.dbManager.initialize();
			await this.jobQueue.initialize();

			// Setup job consumers for ranking updates
			await this.setupJobConsumers();

			// Setup scheduled ranking updates
			await this.setupScheduledUpdates();

			logger.info('Ranking Update Service initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Ranking Update Service:', error);
			throw error;
		}
	}

	/**
	 * Setup job queue consumers for ranking updates
	 */
	private async setupJobConsumers(): Promise<void>
	{
		// Single domain ranking update consumer
		await this.jobQueue.createConsumer(
			Constants.JOB_QUEUES.RANKING_UPDATE,
			this.handleSingleRankingUpdate.bind(this),
			{ concurrency: 3 },
		);

		// Batch ranking update consumer
		await this.jobQueue.createConsumer(
			'queue:ranking:batch',
			this.handleBatchRankingUpdate.bind(this),
			{ concurrency: 1 },
		);

		// Ranking history update consumer
		await this.jobQueue.createConsumer(
			'queue:ranking:history',
			this.handleRankingHistoryUpdate.bind(this),
			{ concurrency: 2 },
		);

		logger.info('Ranking update job consumers setup completed');
	}

	/**
	 * Setup scheduled ranking updates
	 */
	private async setupScheduledUpdates(): Promise<void>
	{
		// Schedule daily global ranking recalculation
		await this.scheduleRecurringUpdate({
			type: 'scheduled',
			priority: 'medium',
			metadata: {
				updateType: 'global_daily',
				cronExpression: '0 2 * * *', // 2 AM daily
			},
		});

		// Schedule weekly category ranking updates
		await this.scheduleRecurringUpdate({
			type: 'scheduled',
			priority: 'low',
			metadata: {
				updateType: 'category_weekly',
				cronExpression: '0 3 * * 0', // 3 AM on Sundays
			},
		});

		logger.info('Scheduled ranking updates configured');
	}

	/**
	 * Trigger ranking update for a single domain
	 */
	async triggerDomainRankingUpdate(
		domain: string,
		trigger: Omit<RankingUpdateTrigger, 'domain'> = { type: 'manual', priority: 'medium' },
	): Promise<string>
	{
		try
		{
			const updateTrigger: RankingUpdateTrigger = {
				...trigger,
				domain,
				scheduledAt: new Date(),
			};

			// Store trigger for tracking
			const triggerId = this.generateTriggerId();
			this.updateTriggers.set(triggerId, updateTrigger);

			// Publish ranking update job
			const jobId = await this.jobQueue.publishRankingUpdateJob(domain, 'global');

			logger.info(`Ranking update triggered for domain: ${domain}`, {
				triggerId,
				jobId,
				trigger: updateTrigger,
			});

			return jobId;
		}
		catch (error)
		{
			logger.error(`Failed to trigger ranking update for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Trigger batch ranking update for multiple domains
	 */
	async triggerBatchRankingUpdate(
		domains: string[],
		rankingType: 'global' | 'category' = 'global',
		category?: string,
		priority: 'low' | 'medium' | 'high' = 'medium',
	): Promise<string>
	{
		try
		{
			const batchId = this.generateBatchId();
			const batchUpdate: BatchRankingUpdate = {
				batchId,
				domains,
				rankingType,
				category,
				status: 'pending',
				processedCount: 0,
				totalCount: domains.length,
				errors: [],
			};

			// Store batch for tracking
			this.activeBatches.set(batchId, batchUpdate);

			// Publish batch ranking job
			const jobData = {
				batchId,
				domains,
				rankingType,
				category,
				priority,
			};

			const jobId = await this.jobQueue.publishJob('queue:ranking:batch', jobData, {
				priority: this.getPriorityValue(priority),
			});

			logger.info(`Batch ranking update triggered for ${domains.length} domains`, {
				batchId,
				jobId,
				rankingType,
				category,
			});

			return batchId;
		}
		catch (error)
		{
			logger.error('Failed to trigger batch ranking update:', error);
			throw error;
		}
	}

	/**
	 * Handle single domain ranking update job
	 */
	private async handleSingleRankingUpdate(jobData: any): Promise<void>
	{
		const { domain, rankingType = 'global', category } = jobData;

		try
		{
			logger.info(`Processing single ranking update for domain: ${domain}`);

			// Fetch domain data
			const domainData = await this.fetchDomainData(domain);
			if (!domainData)
			{
				throw new Error(`No data found for domain: ${domain}`);
			}

			// Optional: validate DomainDescription input
			if (process.env.VALIDATE_DESCRIPTIONS === '1')
			{
				try
				{
					const validator = DomainDescriptionValidator.get();
					validator.assert(domainData as any);
				}
				catch (e)
				{
					logger.warn(`Invalid DomainDescription for ${domain}: ${(e as Error).message}`);
					throw e;
				}
			}

			// Calculate composite score
			const compositeScore = this.compositeRanker.calculateCompositeScore(domainData);

			// Optional: validate output shape where applicable
			if (process.env.VALIDATE_DESCRIPTIONS === '1')
			{
				try
				{
					const updated = { ...(domainData as any), ranking: { ...(domainData as any).ranking, overall: compositeScore.overallScore } };
					const validator = DomainDescriptionValidator.get();
					validator.assert(updated);
				}
				catch (e)
				{
					logger.warn(`Post-score validation failed for ${domain}: ${(e as Error).message}`);
				}
			}

			// Store updated ranking
			await this.storeDomainRanking(compositeScore, rankingType, category);

			// Update ranking history
			await this.updateRankingHistory(domain, compositeScore, rankingType, category);

			// Trigger related updates if needed
			await this.triggerRelatedUpdates(domain, rankingType, category);

			logger.info(`Single ranking update completed for domain: ${domain}`, {
				overallScore: compositeScore.overallScore,
				grade: compositeScore.grade,
			});
		}
		catch (error)
		{
			logger.error(`Single ranking update failed for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Handle batch ranking update job
	 */
	private async handleBatchRankingUpdate(jobData: any): Promise<void>
	{
		const {
			batchId, domains, rankingType, category,
		} = jobData;

		try
		{
			logger.info(`Processing batch ranking update: ${batchId}`);

			// Get batch from tracking
			const batch = this.activeBatches.get(batchId);
			if (!batch)
			{
				throw new Error(`Batch not found: ${batchId}`);
			}

			// Update batch status
			batch.status = 'processing';
			batch.startedAt = new Date();

			// Fetch all domain data
			const domainDataList: DomainData[] = [];
			for (const domain of domains)
			{
				try
				{
					const domainData = await this.fetchDomainData(domain);
					if (domainData)
					{
						domainDataList.push(domainData);
					}
					batch.processedCount++;
				}
				catch (error)
				{
					batch.errors.push(`Failed to fetch data for ${domain}: ${error.message}`);
					logger.warn(`Failed to fetch data for domain: ${domain}`, error);
				}
			}

			if (domainDataList.length === 0)
			{
				throw new Error('No valid domain data found for batch ranking');
			}

			// Calculate rankings
			let rankings: RankingResult[];
			if (category)
			{
				rankings = this.compositeRanker.calculateCategoryRankings(domainDataList, category);
			}
			else
			{
				rankings = this.compositeRanker.calculateGlobalRankings(domainDataList);
			}

			// Store rankings in database
			await this.storeRankings(rankings, rankingType, category);

			// Update ranking history for all domains
			await this.updateBatchRankingHistory(rankings, rankingType, category);

			// Update batch status
			batch.status = 'completed';
			batch.completedAt = new Date();

			logger.info(`Batch ranking update completed: ${batchId}`, {
				domainsProcessed: domainDataList.length,
				rankingsCalculated: rankings.length,
				errors: batch.errors.length,
			});
		}
		catch (error)
		{
			// Update batch status on error
			const batch = this.activeBatches.get(batchId);
			if (batch)
			{
				batch.status = 'failed';
				batch.errors.push(`Batch processing failed: ${error.message}`);
			}

			logger.error(`Batch ranking update failed: ${batchId}:`, error);
			throw error;
		}
	}

	/**
	 * Handle ranking history update job
	 */
	private async handleRankingHistoryUpdate(jobData: any): Promise<void>
	{
		const {
			domain, rankingType, category, period = '30d',
		} = jobData;

		try
		{
			logger.info(`Processing ranking history update for domain: ${domain}`);

			// Calculate trend analysis
			const trendAnalysis = await this.calculateRankingTrend(domain, rankingType, period, category);

			// Store trend analysis results
			await this.storeTrendAnalysis(trendAnalysis);

			logger.info(`Ranking history update completed for domain: ${domain}`, {
				trendDirection: trendAnalysis.trendDirection,
				volatility: trendAnalysis.volatility,
			});
		}
		catch (error)
		{
			logger.error(`Ranking history update failed for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Update ranking history for a single domain
	 */
	private async updateRankingHistory(
		domain: string,
		compositeScore: any,
		rankingType: string,
		category?: string,
	): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const today = new Date().toISOString().split('T')[0];

			// Get previous ranking for comparison
			const previousRank = await this.getPreviousRanking(domain, rankingType, category);

			const historyEntry: RankingHistoryEntry = {
				domain,
				date: today,
				rankingType: category ? `category:${category}` : 'global',
				rank: compositeScore.globalRank || compositeScore.categoryRank || 0,
				score: compositeScore.overallScore,
				previousRank,
				rankChange: previousRank ? previousRank - (compositeScore.globalRank || compositeScore.categoryRank || 0) : undefined,
				category,
			};

			// Store in ranking history table
			const query = `
				INSERT INTO domain_ranking_history (domain, date, ranking_type, rank, score)
				VALUES (?, ?, ?, ?, ?)
			`;

			await scylla.execute(query, [
				historyEntry.domain,
				historyEntry.date,
				historyEntry.rankingType,
				historyEntry.rank,
				historyEntry.score,
			]);

			logger.debug(`Ranking history updated for domain: ${domain}`, historyEntry);
		}
		catch (error)
		{
			logger.error(`Failed to update ranking history for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Update ranking history for batch of domains
	 */
	private async updateBatchRankingHistory(
		rankings: RankingResult[],
		rankingType: string,
		category?: string,
	): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const today = new Date().toISOString().split('T')[0];
			const rankingTypeKey = category ? `category:${category}` : 'global';

			// Prepare batch statements
			const queries = [];

			for (const ranking of rankings)
			{
				// Get previous ranking for comparison
				const previousRank = await this.getPreviousRanking(ranking.domain, rankingType, category);

				queries.push({
					query: `
						INSERT INTO domain_ranking_history (domain, date, ranking_type, rank, score)
						VALUES (?, ?, ?, ?, ?)
					`,
					params: [ranking.domain, today, rankingTypeKey, ranking.rank, ranking.score],
				});
			}

			// Execute batch
			for (const queryData of queries)
			{
				await scylla.execute(queryData.query, queryData.params);
			}

			logger.info(`Batch ranking history updated for ${rankings.length} domains`);
		}
		catch (error)
		{
			logger.error('Failed to update batch ranking history:', error);
			throw error;
		}
	}

	/**
	 * Calculate ranking trend analysis for a domain
	 */
	async calculateRankingTrend(
		domain: string,
		rankingType: string,
		period: string = '30d',
		category?: string,
	): Promise<RankingTrendAnalysis>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const rankingTypeKey = category ? `category:${category}` : 'global';

			// Calculate date range
			const endDate = new Date();
			const startDate = new Date();
			const days = parseInt(period.replace('d', ''));
			startDate.setDate(endDate.getDate() - days);

			// Fetch ranking history
			const query = `
				SELECT domain, date, ranking_type, rank, score
				FROM domain_ranking_history
				WHERE domain = ? AND ranking_type = ? AND date >= ? AND date <= ?
				ORDER BY date ASC
			`;

			const result = await scylla.execute(query, [
				domain,
				rankingTypeKey,
				startDate.toISOString().split('T')[0],
				endDate.toISOString().split('T')[0],
			]);

			const rankChanges: RankingHistoryEntry[] = result.rows.map((row: any) => ({
				domain: row.domain,
				date: row.date,
				rankingType: row.ranking_type,
				rank: row.rank,
				score: row.score,
				category,
			}));

			if (rankChanges.length === 0)
			{
				return {
					domain,
					period,
					trendDirection: 'stable',
					averageRank: 0,
					bestRank: 0,
					worstRank: 0,
					volatility: 0,
					rankChanges: [],
				};
			}

			// Calculate trend metrics
			const ranks = rankChanges.map(entry => entry.rank);
			const averageRank = ranks.reduce((sum, rank) => sum + rank, 0) / ranks.length;
			const bestRank = Math.min(...ranks);
			const worstRank = Math.max(...ranks);

			// Calculate volatility (standard deviation)
			const variance = ranks.reduce((sum, rank) => sum + (rank - averageRank) ** 2, 0) / ranks.length;
			const volatility = Math.sqrt(variance);

			// Determine trend direction
			let trendDirection: 'up' | 'down' | 'stable' = 'stable';
			if (rankChanges.length >= 2)
			{
				const firstRank = rankChanges[0].rank;
				const lastRank = rankChanges[rankChanges.length - 1].rank;
				const rankDifference = firstRank - lastRank; // Positive = improved (lower rank number)

				if (rankDifference > 5)
				{
					trendDirection = 'up'; // Improved ranking
				}
				else if (rankDifference < -5)
				{
					trendDirection = 'down'; // Worsened ranking
				}
			}

			const trendAnalysis: RankingTrendAnalysis = {
				domain,
				period,
				trendDirection,
				averageRank: Math.round(averageRank),
				bestRank,
				worstRank,
				volatility: Math.round(volatility * 100) / 100,
				rankChanges,
			};

			logger.debug(`Ranking trend calculated for domain: ${domain}`, trendAnalysis);
			return trendAnalysis;
		}
		catch (error)
		{
			logger.error(`Failed to calculate ranking trend for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
	 * Store trend analysis results
	 */
	private async storeTrendAnalysis(trendAnalysis: RankingTrendAnalysis): Promise<void>
	{
		try
		{
			// Store trend analysis in cache for quick access
			const redis = this.dbManager.getRedisClient();
			const cacheKey = `trend:${trendAnalysis.domain}:${trendAnalysis.period}`;

			await redis.setex(cacheKey, 86400, JSON.stringify(trendAnalysis)); // Cache for 24 hours

			logger.debug(`Trend analysis stored for domain: ${trendAnalysis.domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to store trend analysis for domain: ${trendAnalysis.domain}:`, error);
		}
	}

	/**
	 * Get previous ranking for comparison
	 */
	private async getPreviousRanking(
		domain: string,
		rankingType: string,
		category?: string,
	): Promise<number | undefined>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const rankingTypeKey = category ? `category:${category}` : 'global';

			// Get the most recent ranking before today
			const yesterday = new Date();
			yesterday.setDate(yesterday.getDate() - 1);

			const query = `
				SELECT rank
				FROM domain_ranking_history
				WHERE domain = ? AND ranking_type = ? AND date <= ?
				ORDER BY date DESC
				LIMIT 1
			`;

			const result = await scylla.execute(query, [
				domain,
				rankingTypeKey,
				yesterday.toISOString().split('T')[0],
			]);

			return result.rows[0]?.rank;
		}
		catch (error)
		{
			logger.error(`Failed to get previous ranking for domain: ${domain}:`, error);
			return undefined;
		}
	}

	/**
	 * Trigger related updates after a ranking change
	 */
	private async triggerRelatedUpdates(
		domain: string,
		rankingType: string,
		category?: string,
	): Promise<void>
	{
		try
		{
			// Trigger Manticore sync to update search indexes
			await this.jobQueue.publishManticoreSyncJob(domain, 'incremental');

			// If this is a significant ranking change, trigger category recalculation
			if (category)
			{
				await this.scheduleDelayedCategoryUpdate(category);
			}

			// Trigger ranking history analysis
			await this.jobQueue.publishJob('queue:ranking:history', {
				domain,
				rankingType,
				category,
			}, { delay: 5000 }); // 5 second delay

			logger.debug(`Related updates triggered for domain: ${domain}`);
		}
		catch (error)
		{
			logger.error(`Failed to trigger related updates for domain: ${domain}:`, error);
		}
	}

	/**
	 * Schedule delayed category ranking update to avoid frequent recalculations
	 */
	private async scheduleDelayedCategoryUpdate(category: string): Promise<void>
	{
		const delayKey = `category_update_scheduled:${category}`;
		const redis = this.dbManager.getRedisClient();

		// Check if category update is already scheduled
		const isScheduled = await redis.get(delayKey);
		if (isScheduled)
		{
			return; // Already scheduled
		}

		// Mark as scheduled for 10 minutes
		await redis.setex(delayKey, 600, 'true');

		// Schedule category ranking update with delay
		setTimeout(async () =>
		{
			try
			{
				const domains = await this.getDomainsForCategory(category);
				if (domains.length > 0)
				{
					await this.triggerBatchRankingUpdate(domains, 'category', category, 'low');
				}
			}
			catch (error)
			{
				logger.error(`Failed to trigger delayed category update for: ${category}:`, error);
			}
		}, 600000); // 10 minutes delay

		logger.debug(`Delayed category update scheduled for: ${category}`);
	}

	/**
	 * Get domains for a specific category
	 */
	private async getDomainsForCategory(category: string): Promise<string[]>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const query = `
				SELECT domain
				FROM domain_analysis
				WHERE category = ?
				LIMIT 1000
			`;

			const result = await scylla.execute(query, [category]);
			return result.rows.map((row: any) => row.domain);
		}
		catch (error)
		{
			logger.error(`Failed to get domains for category: ${category}:`, error);
			return [];
		}
	}

	/**
	 * Schedule recurring ranking update
	 */
	private async scheduleRecurringUpdate(trigger: RankingUpdateTrigger): Promise<void>
	{
		// This would integrate with a cron scheduler in production
		// For now, just log the scheduled update
		logger.info('Recurring ranking update scheduled', trigger);
	}

	/**
	 * Fetch domain data for ranking calculation
	 */
	private async fetchDomainData(domain: string): Promise<any | null>
	{
		logger.debug(`Fetching domain data for: ${domain}`);
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const query = 'SELECT domain, category, performance_metrics, security_metrics, seo_metrics, technical_metrics, technologies, server_info, domain_age_days, registrar, screenshot_urls, last_crawled FROM domain_analysis WHERE domain = ?';
			const res = await scylla.execute(query, [domain]);
			const row = res.rows?.[0];
			if (!row) return null;
			const desc: any = {
				metadata: {
					domain: row.domain,
					tld: String(row.domain).split('.').pop() || '',
					status: 'active',
					category: { primary: row.category || 'uncategorized' },
					owner: { registrar: row.registrar || undefined },
					ageDays: row.domain_age_days || undefined,
				},
				technical: {
					technologies: Array.isArray(row.technologies) ? row.technologies : (row.technologies ? Array.from(row.technologies) : []),
					performance: row.performance_metrics ? { loadTimeMs: Number(row.performance_metrics.load_time) || undefined } : undefined,
					security: row.security_metrics ? { sslGrade: row.security_metrics.ssl_grade } : undefined,
				},
				seo: row.seo_metrics ? { title: row.seo_metrics.title, metaDescription: row.seo_metrics.description } : {},
				ranking: {},
				crawl: { lastCrawled: new Date(row.last_crawled || Date.now()).toISOString(), crawlType: 'quick' },
			};
			return desc;
		}
		catch (e)
		{
			logger.error(`Failed to fetch domain data for ${domain}:`, e);
			return null;
		}
	}

	/**
	 * Store domain ranking in database
	 */
	private async storeDomainRanking(compositeScore: any, rankingType: string, category?: string): Promise<void>
	{
		// Implementation would store the ranking in ScyllaDB
		logger.debug(`Storing domain ranking for: ${compositeScore.domain}`);
	}

	/**
	 * Store multiple rankings in database
	 */
	private async storeRankings(rankings: RankingResult[], rankingType: string, category?: string): Promise<void>
	{
		// Implementation would store rankings in ScyllaDB
		logger.debug(`Storing ${rankings.length} rankings for type: ${rankingType}`);
	}

	/**
	 * Get batch status
	 */
	getBatchStatus(batchId: string): BatchRankingUpdate | undefined
	{
		return this.activeBatches.get(batchId);
	}

	/**
	 * Get all active batches
	 */
	getActiveBatches(): BatchRankingUpdate[]
	{
		return Array.from(this.activeBatches.values());
	}

	/**
	 * Clean up completed batches
	 */
	cleanupCompletedBatches(): void
	{
		const cutoffTime = new Date();
		cutoffTime.setHours(cutoffTime.getHours() - 24); // Keep for 24 hours

		for (const [batchId, batch] of this.activeBatches)
		{
			if (batch.status === 'completed' && batch.completedAt && batch.completedAt < cutoffTime)
			{
				this.activeBatches.delete(batchId);
			}
		}
	}

	/**
	 * Generate unique batch ID
	 */
	private generateBatchId(): string
	{
		return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * Generate unique trigger ID
	 */
	private generateTriggerId(): string
	{
		return `trigger_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * Convert priority string to numeric value
	 */
	private getPriorityValue(priority: 'low' | 'medium' | 'high'): number
	{
		const priorities = {
			low: 1,
			medium: 5,
			high: 10,
		};
		return priorities[priority] || 5;
	}

	/**
	 * Health check for ranking update service
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			// Check database connections
			const dbHealth = await this.dbManager.healthCheck();
			if (!dbHealth)
			{
				return false;
			}

			// Check job queue
			const queueHealth = await this.jobQueue.healthCheck();
			if (!queueHealth)
			{
				return false;
			}

			return true;
		}
		catch (error)
		{
			logger.error('Ranking update service health check failed:', error);
			return false;
		}
	}
}

export default RankingUpdateService;
