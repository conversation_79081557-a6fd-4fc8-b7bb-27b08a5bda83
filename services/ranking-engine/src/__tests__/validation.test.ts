import {
	describe, it, expect, vi, beforeEach, afterEach,
} from 'vitest';
import RankingUpdateService from '../services/RankingUpdateService';

vi.mock('xss', () => ({ default: (s: string) => s }));

const OLD = process.env.VALIDATE_DESCRIPTIONS;

vi.mock('@shared', async (orig) =>
{
	const mod = await orig();
	return {
		...mod,
		DomainDescriptionValidator: {
			get: () => new (class
			{
				validate() { return { ok: false, errors: [{ instancePath: '/metadata/domain', message: 'invalid' }] } }

				assert(obj: any)
				{
					if (!obj || obj.metadata?.domain === 'bad') throw new Error('DomainDescription validation failed');
				}
			})(),
		},
	};
});

describe('ranking-engine validation', () =>
{
	beforeEach(() => { vi.restoreAllMocks() });

	afterEach(() => { process.env.VALIDATE_DESCRIPTIONS = OLD });

	it('throws on invalid input when flag on', async () =>
	{
		process.env.VALIDATE_DESCRIPTIONS = '1';
		const svc = new RankingUpdateService() as any;
		vi.spyOn(svc, 'fetchDomainData').mockResolvedValue({ metadata: { domain: 'bad' }, ranking: {}, crawl: {} });

		await expect(svc.handleSingleRankingUpdate({ domain: 'x' })).rejects.toThrow();
	});

	it('passes with valid input and validates post-score', async () =>
	{
		process.env.VALIDATE_DESCRIPTIONS = '1';
		const svc = new RankingUpdateService() as any;
		vi.spyOn(svc, 'fetchDomainData').mockResolvedValue({ metadata: { domain: 'good.com', category: { primary: 'general' } }, ranking: {}, crawl: { lastCrawled: new Date().toISOString(), crawlType: 'quick' } });
		vi.spyOn(svc.compositeRanker, 'calculateCompositeScore').mockReturnValue({ overallScore: 42 });
		vi.spyOn(svc, 'storeDomainRanking').mockResolvedValue(undefined);
		vi.spyOn(svc, 'updateRankingHistory').mockResolvedValue(undefined);
		vi.spyOn(svc, 'triggerRelatedUpdates').mockResolvedValue(undefined);

		await expect(svc.handleSingleRankingUpdate({ domain: 'x' })).resolves.toBeUndefined();
	});

	it('does not block when flag off', async () =>
	{
		process.env.VALIDATE_DESCRIPTIONS = '0';
		const svc = new RankingUpdateService() as any;
		vi.spyOn(svc, 'fetchDomainData').mockResolvedValue({ metadata: { domain: 'bad' }, ranking: {}, crawl: {} });
		vi.spyOn(svc.compositeRanker, 'calculateCompositeScore').mockReturnValue({ overallScore: 1 });
		vi.spyOn(svc, 'storeDomainRanking').mockResolvedValue(undefined);
		vi.spyOn(svc, 'updateRankingHistory').mockResolvedValue(undefined);
		vi.spyOn(svc, 'triggerRelatedUpdates').mockResolvedValue(undefined);

		await expect(svc.handleSingleRankingUpdate({ domain: 'x' })).resolves.toBeUndefined();
	});
});
