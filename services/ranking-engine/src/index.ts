import {
	<PERSON><PERSON>anager, JobQueue, Logger, Config, Constants,
} from '@shared';
import {
	CompositeRanker, DomainData, CompositeScore, RankingResult,
} from './algorithms/CompositeRanker';
import { RankingUpdateService } from './services/RankingUpdateService';

const logger = Logger.getLogger('RankingEngine');
const config = Config;

interface RankingJobData
{
  domain: string;
  rankingType: string;
  category?: string;
}

interface BatchRankingJobData
{
  domains: string[];
  rankingType: string;
  category?: string;
}

interface DomainScores
{
  performanceScore: number;
  securityScore: number;
  seoScore: number;
  technicalScore: number;
  backlinkScore: number;
  overallScore?: number;
}

interface RankingWeights
{
  PERFORMANCE: number;
  SECURITY: number;
  SEO: number;
  TECHNICAL: number;
  BACKLINKS: number;
}

interface HealthCheckResult
{
  service: string;
  status: string;
  databases: any;
  timestamp: string;
  rankingStats?: any;
  error?: string;
}

/**
 * Ranking Engine Service
 * Handles domain scoring and ranking calculations
 */
class RankingEngineService
{
	private dbManager: DatabaseManager;

	private jobQueue: JobQueue;

	private isRunning: boolean;

	private rankingWeights: RankingWeights;

	private compositeRanker: CompositeRanker;

	private rankingUpdateService: RankingUpdateService;

	constructor()
	{
		this.dbManager = new DatabaseManager();
		this.jobQueue = new JobQueue();
		this.isRunning = false;
		this.rankingWeights = config.get('RANKING_WEIGHTS');
		this.compositeRanker = new CompositeRanker(this.rankingWeights);
		this.rankingUpdateService = new RankingUpdateService();
	}

	/**
   * Initialize the ranking engine service
   */
	async initialize(): Promise<void>
	{
		try
		{
			logger.info('Initializing Ranking Engine Service...');

			// Validate configuration
			config.validate();

			// Initialize database connections
			await this.dbManager.initialize();

			// Initialize job queue
			await this.jobQueue.initialize();

			// Setup job consumers
			await this.setupJobConsumers();

			// Initialize ranking update service
			await this.rankingUpdateService.initialize();

			logger.info('Ranking Engine Service initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Ranking Engine Service:', error);
			throw error;
		}
	}

	/**
   * Setup job queue consumers
   */
	async setupJobConsumers(): Promise<void>
	{
		// Ranking update job consumer
		await this.jobQueue.createConsumer(
			Constants.JOB_QUEUES.RANKING_UPDATE,
			this.handleRankingUpdateJob.bind(this),
			{ concurrency: 2 },
		);

		// Batch ranking update consumer
		await this.jobQueue.createConsumer(
			'queue:ranking:batch',
			this.handleBatchRankingJob.bind(this),
			{ concurrency: 1 },
		);

		logger.info('Job consumers set up successfully');
	}

	/**
   * Handle ranking update job
   */
	async handleRankingUpdateJob(jobData: RankingJobData, message: any): Promise<void>
	{
		const { domain, rankingType, category } = jobData;

		logger.info(`Processing ranking update for domain: ${domain}, type: ${rankingType}`);

		try
		{
			// Fetch domain data
			const domainData = await this.fetchDomainData(domain);
			if (!domainData)
			{
				throw new Error(`No data found for domain: ${domain}`);
			}

			// Calculate composite score using new algorithm
			const compositeScore = this.compositeRanker.calculateCompositeScore(domainData);

			// Update domain ranking in database
			await this.storeDomainRanking(compositeScore, rankingType, category);

			// Trigger global/category ranking recalculation if needed
			if (rankingType === 'global' || category)
			{
				await this.scheduleRankingRecalculation(rankingType, category);
			}

			// Trigger Manticore sync
			await this.jobQueue.publishManticoreSyncJob(domain);

			logger.info(`Ranking update completed for domain: ${domain}`, {
				overallScore: compositeScore.overallScore,
				grade: compositeScore.grade,
			});
		}
		catch (error)
		{
			logger.error(`Ranking update failed for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
   * Handle batch ranking job for multiple domains
   */
	async handleBatchRankingJob(jobData: BatchRankingJobData, message: any): Promise<void>
	{
		const { domains, rankingType, category } = jobData;

		logger.info(`Processing batch ranking update for ${domains.length} domains, type: ${rankingType}`);

		try
		{
			// Fetch all domain data
			const domainDataList: DomainData[] = [];
			for (const domain of domains)
			{
				const domainData = await this.fetchDomainData(domain);
				if (domainData)
				{
					domainDataList.push(domainData);
				}
			}

			if (domainDataList.length === 0)
			{
				logger.warn('No valid domain data found for batch ranking');
				return;
			}

			// Calculate rankings
			let rankings: RankingResult[];
			if (category)
			{
				rankings = this.compositeRanker.calculateCategoryRankings(domainDataList, category);
			}
			else
			{
				rankings = this.compositeRanker.calculateGlobalRankings(domainDataList);
			}

			// Store rankings in database
			await this.storeRankings(rankings, rankingType, category);

			// Get ranking statistics
			const stats = this.compositeRanker.getRankingStatistics(rankings);

			logger.info('Batch ranking update completed', {
				domainsProcessed: domainDataList.length,
				rankingsCalculated: rankings.length,
				averageScore: stats.averageScore,
				gradeDistribution: stats.gradeDistribution,
			});
		}
		catch (error)
		{
			logger.error('Batch ranking update failed:', error);
			throw error;
		}
	}

	/**
   * Calculate domain scores based on analysis data
   */
	async calculateDomainScores(domain: string): Promise<DomainScores>
	{
		logger.info(`Calculating scores for domain: ${domain}`);

		try
		{
			// Fetch domain analysis data from ScyllaDB
			const analysisData = await this.fetchDomainAnalysisData(domain);

			if (!analysisData)
			{
				throw new Error(`No analysis data found for domain: ${domain}`);
			}

			// Calculate individual scores
			const performanceScore = await this.calculatePerformanceScore(analysisData);
			const securityScore = await this.calculateSecurityScore(analysisData);
			const seoScore = await this.calculateSEOScore(analysisData);
			const technicalScore = await this.calculateTechnicalScore(analysisData);
			const backlinkScore = await this.calculateBacklinkScore(domain);

			const scores: DomainScores = {
				performanceScore,
				securityScore,
				seoScore,
				technicalScore,
				backlinkScore,
			};

			// Calculate overall score using weights
			scores.overallScore = this.calculateOverallScore(scores);

			logger.info(`Scores calculated for domain: ${domain}`, scores);
			return scores;
		}
		catch (error)
		{
			logger.error(`Failed to calculate scores for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
   * Fetch comprehensive domain data for ranking calculation
   */
	async fetchDomainData(domain: string): Promise<DomainData | null>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();
			const maria = this.dbManager.getMariaClient();

			// Fetch domain analysis data from ScyllaDB
			const analysisQuery = `
        SELECT * FROM domain_analysis
        WHERE domain = ?
        LIMIT 1
      `;
			const analysisResult = await scylla.execute(analysisQuery, [domain]);
			const analysisData = analysisResult.rows[0];

			if (!analysisData)
			{
				logger.warn(`No analysis data found for domain: ${domain}`);
				return null;
			}

			// Fetch backlink data from MariaDB
			const backlinkQuery = `
        SELECT
          COUNT(*) as total_backlinks,
          COUNT(DISTINCT source_domain) as unique_domains,
          AVG(link_quality_score) as avg_authority,
          SUM(CASE WHEN link_type = 'follow' THEN 1 ELSE 0 END) as follow_links,
          SUM(CASE WHEN link_type != 'follow' THEN 1 ELSE 0 END) as nofollow_links
        FROM backlinks
        WHERE target_domain = ? AND is_active = 1
      `;
			const backlinkResult = await maria.execute(backlinkQuery, [domain]);
			const backlinkData = (backlinkResult as any)[0];

			// Get top referring domains
			const topReferrersQuery = `
        SELECT source_domain, COUNT(*) as link_count
        FROM backlinks
        WHERE target_domain = ? AND is_active = 1
        GROUP BY source_domain
        ORDER BY link_count DESC
        LIMIT 10
      `;
			const topReferrersResult = await maria.execute(topReferrersQuery, [domain]);
			const topReferringDomains = (topReferrersResult as any[]).map((row: any) => row.source_domain);

			// Transform data to DomainData format
			const domainData: DomainData = {
				domain,
				category: analysisData.category,
				country: analysisData.server_info?.country,
				performanceMetrics: {
					loadTime: analysisData.performance_metrics?.load_time,
					firstContentfulPaint: analysisData.performance_metrics?.fcp,
					largestContentfulPaint: analysisData.performance_metrics?.lcp,
					cumulativeLayoutShift: analysisData.performance_metrics?.cls,
					firstInputDelay: analysisData.performance_metrics?.fid,
					speedIndex: analysisData.performance_metrics?.speed_index,
					responseTime: analysisData.crawl_duration_ms,
					pageSize: analysisData.technical_metrics?.page_size,
					resourceCount: analysisData.technical_metrics?.resource_count,
				},
				coreWebVitals: {
					lcp: analysisData.performance_metrics?.lcp,
					fid: analysisData.performance_metrics?.fid,
					cls: analysisData.performance_metrics?.cls,
					fcp: analysisData.performance_metrics?.fcp,
					ttfb: analysisData.performance_metrics?.ttfb,
				},
				securityMetrics: {
					sslGrade: analysisData.ssl_certificate?.grade,
					sslIssuer: analysisData.ssl_certificate?.issuer,
					sslExpiration: analysisData.ssl_certificate?.expiration ? new Date(analysisData.ssl_certificate.expiration) : undefined,
					usesHttps: analysisData.server_info?.uses_https === 'true',
					securityHeaders: analysisData.security_metrics?.security_headers,
				},
				seoMetrics: {
					metaTags: {
						title: analysisData.seo_metrics?.title,
						description: analysisData.seo_metrics?.description,
						keywords: analysisData.seo_metrics?.keywords ? Array.from(analysisData.seo_metrics.keywords) : undefined,
						canonical: analysisData.seo_metrics?.canonical,
						robots: analysisData.seo_metrics?.robots,
					},
					robotsTxt: {
						exists: analysisData.seo_metrics?.robots_txt_exists || false,
						isValid: analysisData.seo_metrics?.robots_txt_compliant || false,
						allowsCrawling: analysisData.seo_metrics?.robots_allows_crawling || false,
						hasDisallowRules: analysisData.seo_metrics?.robots_has_disallow || false,
						hasSitemapReference: analysisData.seo_metrics?.robots_has_sitemap || false,
						userAgents: [],
					},
					sitemap: {
						exists: analysisData.seo_metrics?.sitemap_exists || false,
						isValid: analysisData.seo_metrics?.sitemap_valid || false,
						urlCount: analysisData.seo_metrics?.sitemap_url_count || 0,
						hasImages: analysisData.seo_metrics?.sitemap_has_images || false,
						hasVideos: analysisData.seo_metrics?.sitemap_has_videos || false,
						hasNews: analysisData.seo_metrics?.sitemap_has_news || false,
					},
					contentMetrics: {
						wordCount: analysisData.content_metrics?.content_length || 0,
						headingStructure: {
							h1Count: analysisData.content_metrics?.h1_count || 0,
							h2Count: analysisData.content_metrics?.h2_count || 0,
							h3Count: analysisData.content_metrics?.h3_count || 0,
							h4Count: analysisData.content_metrics?.h4_count || 0,
							h5Count: analysisData.content_metrics?.h5_count || 0,
							h6Count: analysisData.content_metrics?.h6_count || 0,
							hasProperHierarchy: analysisData.content_metrics?.proper_heading_hierarchy || false,
						},
						imageCount: analysisData.content_metrics?.image_count || 0,
						imageAltTextRatio: analysisData.content_metrics?.alt_text_ratio || 0,
						internalLinkCount: analysisData.content_metrics?.internal_links || 0,
						externalLinkCount: analysisData.content_metrics?.external_links || 0,
						readabilityScore: analysisData.content_metrics?.readability_score,
						languageDetected: analysisData.language_detected,
					},
					technicalSEO: {
						pageLoadSpeed: analysisData.performance_metrics?.load_time / 1000 || 5,
						mobileOptimized: analysisData.mobile_friendly_score > 0.7,
						httpsEnabled: analysisData.server_info?.uses_https === 'true',
						hasCanonical: analysisData.seo_metrics?.has_canonical || false,
						hasHreflang: analysisData.seo_metrics?.has_hreflang || false,
						breadcrumbsPresent: analysisData.seo_metrics?.has_breadcrumbs || false,
						schemaMarkupPresent: analysisData.seo_metrics?.has_schema || false,
					},
				},
				technicalMetrics: {
					dnsResponseTime: analysisData.technical_metrics?.dns_response_time,
					serverResponseTime: analysisData.technical_metrics?.server_response_time,
					supportsIpv6: analysisData.server_info?.supports_ipv6 === 'true',
					hasCdn: analysisData.server_info?.has_cdn === 'true',
					compressionEnabled: analysisData.technical_metrics?.compression_enabled === 'true',
					keepAliveEnabled: analysisData.technical_metrics?.keep_alive_enabled === 'true',
					serverSoftware: analysisData.server_info?.server_software,
					hostingProvider: analysisData.server_info?.hosting_provider,
					uptimePercentage: analysisData.technical_metrics?.uptime_percentage,
				},
				backlinkMetrics: {
					totalBacklinks: backlinkData.total_backlinks || 0,
					uniqueDomains: backlinkData.unique_domains || 0,
					averageAuthority: backlinkData.avg_authority || 0,
					followLinks: backlinkData.follow_links || 0,
					nofollowLinks: backlinkData.nofollow_links || 0,
					topReferringDomains,
				},
				trafficEstimate: analysisData.traffic_estimate,
				lastUpdated: analysisData.last_updated ? new Date(analysisData.last_updated) : undefined,
			};

			return domainData;
		}
		catch (error)
		{
			logger.error(`Failed to fetch domain data for: ${domain}:`, error);
			return null;
		}
	}

	/**
   * Fetch domain analysis data from database (legacy method)
   */
	async fetchDomainAnalysisData(domain: string): Promise<any>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const query = `
        SELECT * FROM domain_analysis
        WHERE domain = ?
        LIMIT 1
      `;

			const result = await scylla.execute(query, [domain]);
			return result.rows[0] || null;
		}
		catch (error)
		{
			logger.error(`Failed to fetch analysis data for domain: ${domain}:`, error);
			return null;
		}
	}

	/**
   * Calculate performance score based on Core Web Vitals and load times
   */
	async calculatePerformanceScore(analysisData: any): Promise<number>
	{
		let score = 0.5; // Base score

		try
		{
			// Response time scoring (0-1000ms = 1.0, 1000-3000ms = 0.5-1.0, >3000ms = 0.1-0.5)
			const responseTime = analysisData.crawl_duration_ms || 5000;
			if (responseTime <= 1000)
			{
				score += 0.3;
			}
			else if (responseTime <= 3000)
			{
				score += 0.3 * (1 - (responseTime - 1000) / 2000);
			}
			else
			{
				score += 0.1;
			}

			// Core Web Vitals scoring (if available)
			if (analysisData.technical_metrics?.core_web_vitals)
			{
				const cwv = analysisData.technical_metrics.core_web_vitals;

				// LCP scoring (good: <2.5s, needs improvement: 2.5-4s, poor: >4s)
				if (cwv.lcp <= 2.5) score += 0.1;
				else if (cwv.lcp <= 4.0) score += 0.05;

				// FID scoring (good: <100ms, needs improvement: 100-300ms, poor: >300ms)
				if (cwv.fid <= 0.1) score += 0.05;
				else if (cwv.fid <= 0.3) score += 0.025;

				// CLS scoring (good: <0.1, needs improvement: 0.1-0.25, poor: >0.25)
				if (cwv.cls <= 0.1) score += 0.05;
				else if (cwv.cls <= 0.25) score += 0.025;
			}

			return Math.min(1.0, Math.max(0.0, score));
		}
		catch (error)
		{
			logger.error('Error calculating performance score:', error);
			return 0.5;
		}
	}

	/**
   * Calculate security score based on SSL, headers, and security practices
   */
	async calculateSecurityScore(analysisData: any): Promise<number>
	{
		let score = 0.3; // Base score

		try
		{
			// SSL certificate scoring
			if (analysisData.server_info?.ssl_grade)
			{
				const sslGrade = analysisData.server_info.ssl_grade;
				switch (sslGrade)
				{
					case 'A+': score += 0.4; break;
					case 'A': score += 0.35; break;
					case 'B': score += 0.25; break;
					case 'C': score += 0.15; break;
					default: score += 0.05; break;
				}
			}

			// HTTPS usage
			if (analysisData.server_info?.uses_https === 'true')
			{
				score += 0.2;
			}

			// Security headers
			if (analysisData.technical_metrics?.security_headers)
			{
				const headers = analysisData.technical_metrics.security_headers;
				if (headers.hsts) score += 0.05;
				if (headers.csp) score += 0.05;
				if (headers.xframe) score += 0.025;
				if (headers.xss_protection) score += 0.025;
			}

			return Math.min(1.0, Math.max(0.0, score));
		}
		catch (error)
		{
			logger.error('Error calculating security score:', error);
			return 0.5;
		}
	}

	/**
   * Calculate SEO score based on meta tags, robots.txt, sitemap, etc.
   */
	async calculateSEOScore(analysisData: any): Promise<number>
	{
		let score = 0.2; // Base score

		try
		{
			const seoMetrics = analysisData.seo_metrics || {};

			// Robots.txt scoring
			if (seoMetrics.robots_txt_exists) score += 0.1;
			if (seoMetrics.robots_txt_compliant) score += 0.1;

			// Sitemap scoring
			if (seoMetrics.sitemap_urls && seoMetrics.sitemap_urls.length > 0)
			{
				score += 0.15;
			}

			// Meta tags scoring
			if (seoMetrics.has_title) score += 0.1;
			if (seoMetrics.has_description) score += 0.1;
			if (seoMetrics.has_keywords) score += 0.05;

			// Content quality indicators
			if (seoMetrics.content_length > 500) score += 0.1;
			if (seoMetrics.has_headings) score += 0.05;
			if (seoMetrics.has_alt_tags) score += 0.05;

			// Technical SEO
			if (seoMetrics.has_canonical) score += 0.05;
			if (seoMetrics.mobile_friendly) score += 0.05;

			return Math.min(1.0, Math.max(0.0, score));
		}
		catch (error)
		{
			logger.error('Error calculating SEO score:', error);
			return 0.5;
		}
	}

	/**
   * Calculate technical score based on DNS, hosting, and infrastructure
   */
	async calculateTechnicalScore(analysisData: any): Promise<number>
	{
		let score = 0.3; // Base score

		try
		{
			const technicalMetrics = analysisData.technical_metrics || {};
			const serverInfo = analysisData.server_info || {};

			// DNS configuration scoring
			if (serverInfo.supports_ipv6 === 'true') score += 0.1;
			if (serverInfo.has_email === 'true') score += 0.05;
			if (serverInfo.has_cdn === 'true') score += 0.15;

			// Server response scoring
			if (technicalMetrics.dns_analysis_time < 100) score += 0.05;
			if (technicalMetrics.dns_errors_count === '0') score += 0.1;

			// Infrastructure scoring
			if (serverInfo.dns_provider && serverInfo.dns_provider !== 'unknown')
			{
				score += 0.05;
			}

			// Hosting quality indicators
			if (serverInfo.has_cloudflare === 'true') score += 0.1;
			if (technicalMetrics.uptime_percentage > 99.5) score += 0.1;

			return Math.min(1.0, Math.max(0.0, score));
		}
		catch (error)
		{
			logger.error('Error calculating technical score:', error);
			return 0.5;
		}
	}

	/**
   * Calculate backlink score based on external link data
   */
	async calculateBacklinkScore(domain: string): Promise<number>
	{
		try
		{
			const maria = this.dbManager.getMariaClient();

			const query = `
        SELECT
          COUNT(*) as backlink_count,
          AVG(domain_authority) as avg_authority,
          COUNT(DISTINCT referring_domain) as unique_domains
        FROM backlinks
        WHERE target_domain = ?
      `;

			const result = await maria.execute(query, [domain]);
			const backlinkData = (result as any)[0];

			let score = 0.1; // Base score

			// Backlink count scoring
			const backlinkCount = backlinkData.backlink_count || 0;
			if (backlinkCount > 1000) score += 0.4;
			else if (backlinkCount > 100) score += 0.3;
			else if (backlinkCount > 10) score += 0.2;
			else if (backlinkCount > 0) score += 0.1;

			// Domain authority scoring
			const avgAuthority = backlinkData.avg_authority || 0;
			if (avgAuthority > 80) score += 0.3;
			else if (avgAuthority > 60) score += 0.2;
			else if (avgAuthority > 40) score += 0.1;
			else if (avgAuthority > 20) score += 0.05;

			// Unique domains scoring
			const uniqueDomains = backlinkData.unique_domains || 0;
			if (uniqueDomains > 100) score += 0.2;
			else if (uniqueDomains > 50) score += 0.15;
			else if (uniqueDomains > 10) score += 0.1;
			else if (uniqueDomains > 0) score += 0.05;

			return Math.min(1.0, Math.max(0.0, score));
		}
		catch (error)
		{
			logger.error('Error calculating backlink score:', error);
			return 0.3; // Default score when backlink data is unavailable
		}
	}

	/**
   * Calculate overall score using weighted average
   */
	calculateOverallScore(scores: DomainScores): number
	{
		const {
			performanceScore,
			securityScore,
			seoScore,
			technicalScore,
			backlinkScore,
		} = scores;

		const overallScore = (
			(performanceScore * this.rankingWeights.PERFORMANCE)
      + (securityScore * this.rankingWeights.SECURITY)
      + (seoScore * this.rankingWeights.SEO)
      + (technicalScore * this.rankingWeights.TECHNICAL)
      + (backlinkScore * this.rankingWeights.BACKLINKS)
		);

		return Math.round(overallScore * 1000) / 1000; // Round to 3 decimal places
	}

	/**
   * Store domain ranking in database
   */
	async storeDomainRanking(compositeScore: CompositeScore, rankingType: string, category?: string): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			// Update domain_analysis table with new scores
			const updateAnalysisQuery = `
        UPDATE domain_analysis
        SET
          global_rank = ?,
          category_rank = ?,
          overall_score = ?,
          performance_score = ?,
          security_score = ?,
          seo_score = ?,
          technical_score = ?,
          backlink_score = ?,
          last_updated = ?
        WHERE domain = ?
      `;

			await scylla.execute(updateAnalysisQuery, [
				compositeScore.globalRank || 0,
				compositeScore.categoryRank || 0,
				compositeScore.overallScore,
				compositeScore.scores.performance,
				compositeScore.scores.security,
				compositeScore.scores.seo,
				compositeScore.scores.technical,
				compositeScore.scores.backlinks,
				new Date(),
				compositeScore.domain,
			]);

			// Store ranking history
			const historyQuery = `
        INSERT INTO domain_ranking_history (domain, date, ranking_type, rank, score)
        VALUES (?, ?, ?, ?, ?)
      `;

			const today = new Date().toISOString().split('T')[0];
			await scylla.execute(historyQuery, [
				compositeScore.domain,
				today,
				rankingType,
				compositeScore.globalRank || compositeScore.categoryRank || 0,
				compositeScore.overallScore,
			]);

			logger.info(`Domain ranking stored for: ${compositeScore.domain}`, {
				overallScore: compositeScore.overallScore,
				grade: compositeScore.grade,
				rankingType,
			});
		}
		catch (error)
		{
			logger.error(`Failed to store domain ranking for: ${compositeScore.domain}:`, error);
			throw error;
		}
	}

	/**
   * Store multiple rankings in database
   */
	async storeRankings(rankings: RankingResult[], rankingType: string, category?: string): Promise<void>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			// Prepare batch statements
			const queries = [];
			const today = new Date().toISOString().split('T')[0];

			for (const ranking of rankings)
			{
				// Update domain_rankings table
				const rankingKey = category ? `category:${category}` : 'global';

				queries.push({
					query: `
            INSERT INTO domain_rankings (ranking_type, rank, domain, overall_score, last_updated)
            VALUES (?, ?, ?, ?, ?)
          `,
					params: [rankingKey, ranking.rank, ranking.domain, ranking.score, new Date()],
				});

				// Update domain_analysis with rank
				const rankField = category ? 'category_rank' : 'global_rank';
				queries.push({
					query: `
            UPDATE domain_analysis
            SET ${rankField} = ?, last_updated = ?
            WHERE domain = ?
          `,
					params: [ranking.rank, new Date(), ranking.domain],
				});

				// Store ranking history
				queries.push({
					query: `
            INSERT INTO domain_ranking_history (domain, date, ranking_type, rank, score)
            VALUES (?, ?, ?, ?, ?)
          `,
					params: [ranking.domain, today, rankingKey, ranking.rank, ranking.score],
				});
			}

			// Execute batch
			for (const queryData of queries)
			{
				await scylla.execute(queryData.query, queryData.params);
			}

			logger.info(`Stored ${rankings.length} rankings for type: ${rankingType}`, {
				category: category || 'global',
				topDomain: rankings[0]?.domain,
				topScore: rankings[0]?.score,
			});
		}
		catch (error)
		{
			logger.error(`Failed to store rankings for type: ${rankingType}:`, error);
			throw error;
		}
	}

	/**
   * Schedule ranking recalculation
   */
	async scheduleRankingRecalculation(rankingType: string, category?: string): Promise<void>
	{
		try
		{
			// Get all domains for recalculation
			const domains = await this.getDomainsForRanking(category);

			if (domains.length === 0)
			{
				logger.warn(`No domains found for ranking recalculation: ${rankingType}`);
				return;
			}

			// Schedule batch ranking job
			const batchJobData: BatchRankingJobData = {
				domains,
				rankingType,
				category,
			};

			await this.jobQueue.publishJob('queue:ranking:batch', batchJobData, {
				priority: 1,
				delay: 5000, // 5 second delay to allow current job to complete
			});

			logger.info(`Scheduled ranking recalculation for ${domains.length} domains`, {
				rankingType,
				category,
			});
		}
		catch (error)
		{
			logger.error('Failed to schedule ranking recalculation:', error);
		}
	}

	/**
   * Get domains for ranking calculation
   */
	async getDomainsForRanking(category?: string): Promise<string[]>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			let query = 'SELECT domain FROM domain_analysis';
			const params: any[] = [];

			if (category)
			{
				query += ' WHERE category = ?';
				params.push(category);
			}

			query += ' LIMIT 10000'; // Reasonable limit for batch processing

			const result = await scylla.execute(query, params);
			return result.rows.map((row: any) => row.domain);
		}
		catch (error)
		{
			logger.error('Failed to get domains for ranking:', error);
			return [];
		}
	}

	/**
   * Update domain ranking in database (legacy method)
   */
	async updateDomainRanking(domain: string, scores: DomainScores, rankingType: string): Promise<void>
	{
		try
		{
			logger.info(`Updating ranking for domain: ${domain}, type: ${rankingType}`);

			// Placeholder for database update
			// In real implementation, this would update ScyllaDB
			const rankingData = {
				domain,
				rankingType,
				...scores,
				lastUpdated: new Date().toISOString(),
			};

			logger.info(`Ranking updated for domain: ${domain}`, rankingData);
		}
		catch (error)
		{
			logger.error(`Failed to update ranking for domain: ${domain}:`, error);
			throw error;
		}
	}

	/**
   * Recalculate global rankings
   */
	async recalculateGlobalRankings(): Promise<void>
	{
		logger.info('Starting global rankings recalculation...');

		try
		{
			// Get all domains
			const domains = await this.getDomainsForRanking();

			if (domains.length === 0)
			{
				logger.warn('No domains found for global ranking recalculation');
				return;
			}

			// Schedule batch ranking job
			const batchJobData: BatchRankingJobData = {
				domains,
				rankingType: 'global',
			};

			await this.jobQueue.publishJob('queue:ranking:batch', batchJobData, {
				priority: 1,
			});

			logger.info(`Scheduled global rankings recalculation for ${domains.length} domains`);
		}
		catch (error)
		{
			logger.error('Failed to recalculate global rankings:', error);
			throw error;
		}
	}

	/**
   * Recalculate category rankings
   */
	async recalculateCategoryRankings(category: string): Promise<void>
	{
		logger.info(`Starting category rankings recalculation for: ${category}`);

		try
		{
			// Get domains for category
			const domains = await this.getDomainsForRanking(category);

			if (domains.length === 0)
			{
				logger.warn(`No domains found for category ranking recalculation: ${category}`);
				return;
			}

			// Schedule batch ranking job
			const batchJobData: BatchRankingJobData = {
				domains,
				rankingType: 'category',
				category,
			};

			await this.jobQueue.publishJob('queue:ranking:batch', batchJobData, {
				priority: 1,
			});

			logger.info(`Scheduled category rankings recalculation for ${domains.length} domains in category: ${category}`);
		}
		catch (error)
		{
			logger.error(`Failed to recalculate category rankings for ${category}:`, error);
			throw error;
		}
	}

	/**
   * Get current rankings for a category or global
   */
	async getCurrentRankings(category?: string, limit: number = 100): Promise<RankingResult[]>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const rankingType = category ? `category:${category}` : 'global';
			const query = `
        SELECT domain, rank, overall_score, last_updated
        FROM domain_rankings
        WHERE ranking_type = ?
        ORDER BY rank ASC
        LIMIT ?
      `;

			const result = await scylla.execute(query, [rankingType, limit]);

			return result.rows.map((row: any) => ({
				domain: row.domain,
				rank: row.rank,
				score: row.overall_score,
				category,
			}));
		}
		catch (error)
		{
			logger.error('Failed to get current rankings:', error);
			return [];
		}
	}

	/**
   * Get ranking history for trend analysis
   */
	async getRankingHistory(domain: string, days: number = 30): Promise<any[]>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);

			const query = `
        SELECT date, ranking_type, rank, score
        FROM domain_ranking_history
        WHERE domain = ? AND date >= ?
        ORDER BY date DESC
      `;

			const result = await scylla.execute(query, [domain, startDate.toISOString().split('T')[0]]);
			return result.rows;
		}
		catch (error)
		{
			logger.error(`Failed to get ranking history for ${domain}:`, error);
			return [];
		}
	}

	/**
   * Update ranking weights and recalculate
   */
	async updateRankingWeights(newWeights: Partial<RankingWeights>): Promise<void>
	{
		try
		{
			// Update composite ranker weights
			this.compositeRanker.updateWeights(newWeights);

			// Update local weights
			this.rankingWeights = { ...this.rankingWeights, ...newWeights };

			// Schedule global ranking recalculation
			await this.recalculateGlobalRankings();

			logger.info('Ranking weights updated and recalculation scheduled', {
				newWeights,
				currentWeights: this.rankingWeights,
			});
		}
		catch (error)
		{
			logger.error('Failed to update ranking weights:', error);
			throw error;
		}
	}

	/**
   * Start the ranking engine service
   */
	async start(): Promise<void>
	{
		try
		{
			this.isRunning = true;
			logger.info('Ranking Engine Service started and listening for jobs');

			// Keep the service running
			while (this.isRunning)
			{
				await this.delay(1000);
			}
		}
		catch (error)
		{
			logger.error('Error in ranking engine service:', error);
			throw error;
		}
	}

	/**
   * Stop the ranking engine service
   */
	async stop(): Promise<void>
	{
		this.isRunning = false;
		logger.info('Ranking Engine Service stopped');
	}

	/**
   * Graceful shutdown
   */
	async shutdown(): Promise<void>
	{
		try
		{
			logger.info('Shutting down Ranking Engine Service...');
			await this.stop();
			await this.jobQueue.shutdown();
			await this.dbManager.close();
			logger.info('Ranking Engine Service shut down successfully');
		}
		catch (error)
		{
			logger.error('Error during shutdown:', error);
			throw error;
		}
	}

	/**
   * Health check
   */
	async healthCheck(): Promise<HealthCheckResult>
	{
		try
		{
			// Get basic ranking statistics
			const globalRankings = await this.getCurrentRankings(undefined, 10);
			const rankingStats = this.compositeRanker.getRankingStatistics(globalRankings);

			const health: HealthCheckResult = {
				service: 'ranking-engine',
				status: this.isRunning ? 'running' : 'stopped',
				databases: await this.dbManager.healthCheck(),
				timestamp: new Date().toISOString(),
				rankingStats: {
					totalRankedDomains: globalRankings.length,
					averageScore: rankingStats.averageScore,
					topDomain: globalRankings[0]?.domain,
					topScore: globalRankings[0]?.score,
					weights: this.compositeRanker.getWeights(),
				},
			};

			return health;
		}
		catch (error)
		{
			logger.error('Error in health check:', error);
			return {
				service: 'ranking-engine',
				status: this.isRunning ? 'running' : 'stopped',
				databases: await this.dbManager.healthCheck(),
				timestamp: new Date().toISOString(),
				error: (error as Error).message,
			};
		}
	}

	/**
   * Get ranking engine statistics
   */
	async getRankingStatistics(): Promise<any>
	{
		try
		{
			const globalRankings = await this.getCurrentRankings(undefined, 1000);
			const stats = this.compositeRanker.getRankingStatistics(globalRankings);

			// Get category statistics
			const categoryStats: Record<string, any> = {};
			const categories = await this.getActiveCategories();

			for (const category of categories)
			{
				const categoryRankings = await this.getCurrentRankings(category, 100);
				categoryStats[category] = this.compositeRanker.getRankingStatistics(categoryRankings);
			}

			return {
				global: stats,
				categories: categoryStats,
				weights: this.compositeRanker.getWeights(),
				lastUpdated: new Date().toISOString(),
			};
		}
		catch (error)
		{
			logger.error('Failed to get ranking statistics:', error);
			throw error;
		}
	}

	/**
   * Get active categories
   */
	async getActiveCategories(): Promise<string[]>
	{
		try
		{
			const scylla = this.dbManager.getScyllaClient();

			const query = `
        SELECT DISTINCT category
        FROM domain_analysis
        WHERE category IS NOT NULL
        LIMIT 100
      `;

			const result = await scylla.execute(query);
			return result.rows.map((row: any) => row.category).filter(Boolean);
		}
		catch (error)
		{
			logger.error('Failed to get active categories:', error);
			return [];
		}
	}

	/**
   * Trigger ranking update for a single domain
   */
	async triggerDomainRankingUpdate(domain: string, priority: 'low' | 'medium' | 'high' = 'medium'): Promise<string>
	{
		return await this.rankingUpdateService.triggerDomainRankingUpdate(domain, {
			type: 'manual',
			priority,
		});
	}

	/**
   * Trigger batch ranking update for multiple domains
   */
	async triggerBatchRankingUpdate(
		domains: string[],
		rankingType: 'global' | 'category' = 'global',
		category?: string,
		priority: 'low' | 'medium' | 'high' = 'medium',
	): Promise<string>
	{
		return await this.rankingUpdateService.triggerBatchRankingUpdate(domains, rankingType, category, priority);
	}

	/**
   * Get ranking trend analysis for a domain
   */
	async getRankingTrend(
		domain: string,
		rankingType: string = 'global',
		period: string = '30d',
		category?: string,
	): Promise<any>
	{
		return await this.rankingUpdateService.calculateRankingTrend(domain, rankingType, period, category);
	}

	/**
   * Get batch status
   */
	getBatchStatus(batchId: string): any
	{
		return this.rankingUpdateService.getBatchStatus(batchId);
	}

	/**
   * Get all active batches
   */
	getActiveBatches(): any[]
	{
		return this.rankingUpdateService.getActiveBatches();
	}

	/**
   * Clean up completed batches
   */
	cleanupCompletedBatches(): void
	{
		this.rankingUpdateService.cleanupCompletedBatches();
	}

	/**
   * Delay utility
   */
	delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

// Initialize and start the service
async function main(): Promise<void>
{
	const rankingEngine = new RankingEngineService();

	try
	{
		await rankingEngine.initialize();

		// Graceful shutdown handling
		process.on('SIGTERM', async () =>
		{
			await rankingEngine.shutdown();
			process.exit(0);
		});

		process.on('SIGINT', async () =>
		{
			await rankingEngine.shutdown();
			process.exit(0);
		});

		await rankingEngine.start();
	}
	catch (error)
	{
		logger.error('Failed to start Ranking Engine Service:', error);
		process.exit(1);
	}
}

// Start the service if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`)
{
	main();
}

export default RankingEngineService;
