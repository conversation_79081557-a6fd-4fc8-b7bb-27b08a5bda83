#!/usr/bin/env tsx

/**
 * Ranking Update Service Demo
 *
 * This script demonstrates the key features of the RankingUpdateService:
 * 1. Triggering single domain ranking updates
 * 2. Triggering batch ranking updates
 * 3. Tracking ranking history and trends
 * 4. Managing batch operations
 */

import { Logger } from '@shared';
import { RankingUpdateService } from '../services/RankingUpdateService';

const logger = Logger.getLogger('RankingUpdateDemo');

async function demonstrateRankingUpdates(): Promise<void>
{
	logger.info('Starting Ranking Update Service Demo...');

	const rankingUpdateService = new RankingUpdateService();

	try
	{
		// Initialize the service
		await rankingUpdateService.initialize();
		logger.info('✅ Ranking Update Service initialized');

		// Demo 1: Trigger single domain ranking update
		logger.info('\n📊 Demo 1: Single Domain Ranking Update');
		const singleDomainJobId = await rankingUpdateService.triggerDomainRankingUpdate(
			'example.com',
			{
				type: 'manual',
				priority: 'high',
				metadata: {
					reason: 'Demo update',
					requestedBy: 'demo-script',
				},
			},
		);
		logger.info(`Single domain update triggered with job ID: ${singleDomainJobId}`);

		// Demo 2: Trigger batch ranking update
		logger.info('\n📈 Demo 2: Batch Ranking Update');
		const domains = [
			'google.com',
			'facebook.com',
			'amazon.com',
			'microsoft.com',
			'apple.com',
		];

		const batchId = await rankingUpdateService.triggerBatchRankingUpdate(
			domains,
			'global',
			undefined,
			'medium',
		);
		logger.info(`Batch ranking update triggered with batch ID: ${batchId}`);

		// Demo 3: Monitor batch status
		logger.info('\n📋 Demo 3: Batch Status Monitoring');
		const batchStatus = rankingUpdateService.getBatchStatus(batchId);
		if (batchStatus)
		{
			logger.info('Batch Status:', {
				batchId: batchStatus.batchId,
				status: batchStatus.status,
				totalCount: batchStatus.totalCount,
				processedCount: batchStatus.processedCount,
				errors: batchStatus.errors.length,
			});
		}

		// Demo 4: Category-specific batch update
		logger.info('\n🏷️ Demo 4: Category-Specific Ranking Update');
		const techDomains = [
			'github.com',
			'stackoverflow.com',
			'techcrunch.com',
			'wired.com',
		];

		const categoryBatchId = await rankingUpdateService.triggerBatchRankingUpdate(
			techDomains,
			'category',
			'technology',
			'low',
		);
		logger.info(`Category batch update triggered with batch ID: ${categoryBatchId}`);

		// Demo 5: Calculate ranking trend (simulated)
		logger.info('\n📊 Demo 5: Ranking Trend Analysis');
		try
		{
			const trendAnalysis = await rankingUpdateService.calculateRankingTrend(
				'example.com',
				'global',
				'30d',
			);

			logger.info('Ranking Trend Analysis:', {
				domain: trendAnalysis.domain,
				period: trendAnalysis.period,
				trendDirection: trendAnalysis.trendDirection,
				averageRank: trendAnalysis.averageRank,
				bestRank: trendAnalysis.bestRank,
				worstRank: trendAnalysis.worstRank,
				volatility: trendAnalysis.volatility,
				dataPoints: trendAnalysis.rankChanges.length,
			});
		}
		catch (error)
		{
			logger.info('Trend analysis demo (no historical data available):', error.message);
		}

		// Demo 6: List all active batches
		logger.info('\n📊 Demo 6: Active Batches Overview');
		const activeBatches = rankingUpdateService.getActiveBatches();
		logger.info(`Active batches: ${activeBatches.length}`);

		activeBatches.forEach((batch, index) =>
		{
			logger.info(`Batch ${index + 1}:`, {
				batchId: batch.batchId,
				status: batch.status,
				rankingType: batch.rankingType,
				category: batch.category,
				domainsCount: batch.totalCount,
				processed: batch.processedCount,
				errors: batch.errors.length,
			});
		});

		// Demo 7: Health check
		logger.info('\n🏥 Demo 7: Service Health Check');
		const isHealthy = await rankingUpdateService.healthCheck();
		logger.info(`Service health status: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);

		// Demo 8: Cleanup completed batches
		logger.info('\n🧹 Demo 8: Cleanup Completed Batches');
		const batchesBeforeCleanup = rankingUpdateService.getActiveBatches().length;
		rankingUpdateService.cleanupCompletedBatches();
		const batchesAfterCleanup = rankingUpdateService.getActiveBatches().length;
		logger.info(`Batches before cleanup: ${batchesBeforeCleanup}, after cleanup: ${batchesAfterCleanup}`);

		logger.info('\n✅ Ranking Update Service Demo completed successfully!');

		// Summary of features demonstrated
		logger.info('\n📋 Features Demonstrated:');
		logger.info('1. ✅ Single domain ranking update triggers');
		logger.info('2. ✅ Batch ranking update triggers');
		logger.info('3. ✅ Category-specific ranking updates');
		logger.info('4. ✅ Batch status monitoring and tracking');
		logger.info('5. ✅ Ranking trend analysis calculation');
		logger.info('6. ✅ Active batch management');
		logger.info('7. ✅ Service health monitoring');
		logger.info('8. ✅ Batch cleanup operations');

		logger.info('\n🎯 Key Benefits:');
		logger.info('• Efficient batch processing for large-scale ranking updates');
		logger.info('• Historical trend analysis for ranking performance tracking');
		logger.info('• Priority-based job scheduling for optimal resource utilization');
		logger.info('• Comprehensive monitoring and error handling');
		logger.info('• Automatic cleanup of completed operations');
	}
	catch (error)
	{
		logger.error('Demo failed:', error);
		throw error;
	}
}

// Run the demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`)
{
	demonstrateRankingUpdates()
		.then(() =>
		{
			logger.info('Demo completed successfully');
			process.exit(0);
		})
		.catch((error) =>
		{
			logger.error('Demo failed:', error);
			process.exit(1);
		});
}

export { demonstrateRankingUpdates };
