import { Logger } from '@shared';

const logger = Logger.getLogger('SecurityScorer');

export interface SecurityMetrics
{
	sslGrade?: string;
	sslIssuer?: string;
	sslExpiration?: Date;
	usesHttps?: boolean;
	securityHeaders?: SecurityHeaders;
	vulnerabilities?: Vulnerability[];
	certificateChain?: CertificateInfo[];
}

export interface SecurityHeaders
{
	hsts?: boolean;
	csp?: boolean;
	xframe?: boolean;
	xssProtection?: boolean;
	contentTypeOptions?: boolean;
	referrerPolicy?: boolean;
	permissionsPolicy?: boolean;
}

export interface Vulnerability
{
	type: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	description: string;
	cve?: string;
}

export interface CertificateInfo
{
	issuer: string;
	subject: string;
	validFrom: Date;
	validTo: Date;
	signatureAlgorithm: string;
	keySize?: number;
}

/**
 * Security scoring algorithm based on SSL, headers, and security practices
 */
class SecurityScorer
{
	private static readonly WEIGHTS = {
		SSL_CERTIFICATE: 0.35,
		SECURITY_HEADERS: 0.25,
		HTTPS_USAGE: 0.20,
		VULNERABILITIES: 0.20,
	};

	private static readonly SSL_GRADES = {
		'A+': 1.0,
		A: 0.9,
		'A-': 0.8,
		B: 0.7,
		C: 0.5,
		D: 0.3,
		E: 0.2,
		F: 0.1,
		T: 0.0, // Certificate not trusted
	};

	private static readonly VULNERABILITY_PENALTIES = {
		critical: 0.4,
		high: 0.2,
		medium: 0.1,
		low: 0.05,
	};

	/**
	 * Calculate security score based on various security metrics
	 */
	static calculateScore(metrics: SecurityMetrics): number
	{
		try
		{
			let totalScore = 0;

			// SSL Certificate scoring
			const sslScore = this.calculateSSLScore(metrics);
			totalScore += sslScore * this.WEIGHTS.SSL_CERTIFICATE;

			// Security headers scoring
			const headersScore = this.calculateSecurityHeadersScore(metrics.securityHeaders);
			totalScore += headersScore * this.WEIGHTS.SECURITY_HEADERS;

			// HTTPS usage scoring
			const httpsScore = this.calculateHttpsScore(metrics.usesHttps);
			totalScore += httpsScore * this.WEIGHTS.HTTPS_USAGE;

			// Vulnerabilities penalty
			const vulnScore = this.calculateVulnerabilityScore(metrics.vulnerabilities);
			totalScore += vulnScore * this.WEIGHTS.VULNERABILITIES;

			const finalScore = Math.min(1.0, Math.max(0.0, totalScore));

			logger.debug('Security score calculated', {
				sslScore,
				headersScore,
				httpsScore,
				vulnScore,
				finalScore,
				metrics,
			});

			return Math.round(finalScore * 1000) / 1000;
		}
		catch (error)
		{
			logger.error('Error calculating security score:', error);
			return 0.3; // Conservative fallback score
		}
	}

	/**
	 * Calculate SSL certificate score
	 */
	private static calculateSSLScore(metrics: SecurityMetrics): number
	{
		let score = 0;

		// SSL Grade scoring
		if (metrics.sslGrade)
		{
			const gradeScore = this.SSL_GRADES[metrics.sslGrade.toUpperCase() as keyof typeof this.SSL_GRADES] || 0.1;
			score += gradeScore * 0.6; // 60% of SSL score
		}
		else
		{
			// No SSL certificate detected
			return 0.1;
		}

		// Certificate validity scoring
		if (metrics.sslExpiration)
		{
			const now = new Date();
			const expiration = new Date(metrics.sslExpiration);
			const daysUntilExpiry = Math.floor((expiration.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

			if (daysUntilExpiry > 90)
			{
				score += 0.2; // 20% for valid certificate with good expiry
			}
			else if (daysUntilExpiry > 30)
			{
				score += 0.15; // 15% for valid certificate expiring soon
			}
			else if (daysUntilExpiry > 0)
			{
				score += 0.1; // 10% for valid certificate expiring very soon
			}
			// No points for expired certificates
		}

		// Certificate issuer scoring
		if (metrics.sslIssuer)
		{
			const trustedIssuers = [
				'Let\'s Encrypt',
				'DigiCert',
				'Comodo',
				'GlobalSign',
				'GeoTrust',
				'Symantec',
				'Thawte',
				'RapidSSL',
				'Sectigo',
			];

			const isTrustedIssuer = trustedIssuers.some(issuer => metrics.sslIssuer!.toLowerCase().includes(issuer.toLowerCase()));

			if (isTrustedIssuer)
			{
				score += 0.1; // 10% for trusted issuer
			}
		}

		// Certificate chain scoring
		if (metrics.certificateChain && metrics.certificateChain.length > 0)
		{
			score += 0.1; // 10% for proper certificate chain
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate security headers score
	 */
	private static calculateSecurityHeadersScore(headers?: SecurityHeaders): number
	{
		if (!headers)
		{
			return 0.1; // Minimal score for no security headers
		}

		let score = 0;

		// HSTS (HTTP Strict Transport Security) - Critical
		if (headers.hsts)
		{
			score += 0.25;
		}

		// CSP (Content Security Policy) - Important
		if (headers.csp)
		{
			score += 0.2;
		}

		// X-Frame-Options - Important for clickjacking protection
		if (headers.xframe)
		{
			score += 0.15;
		}

		// X-XSS-Protection - Basic XSS protection
		if (headers.xssProtection)
		{
			score += 0.1;
		}

		// X-Content-Type-Options - MIME type sniffing protection
		if (headers.contentTypeOptions)
		{
			score += 0.1;
		}

		// Referrer-Policy - Privacy protection
		if (headers.referrerPolicy)
		{
			score += 0.1;
		}

		// Permissions-Policy (formerly Feature-Policy) - Modern security
		if (headers.permissionsPolicy)
		{
			score += 0.1;
		}

		return Math.min(1.0, score);
	}

	/**
	 * Calculate HTTPS usage score
	 */
	private static calculateHttpsScore(usesHttps?: boolean): number
	{
		if (usesHttps === true)
		{
			return 1.0; // Full score for HTTPS usage
		}
		if (usesHttps === false)
		{
			return 0.1; // Very low score for HTTP only
		}

		return 0.5; // Default when unknown
	}

	/**
	 * Calculate vulnerability score (penalties applied)
	 */
	private static calculateVulnerabilityScore(vulnerabilities?: Vulnerability[]): number
	{
		if (!vulnerabilities || vulnerabilities.length === 0)
		{
			return 1.0; // Full score for no known vulnerabilities
		}

		let score = 1.0;

		// Apply penalties for each vulnerability
		for (const vuln of vulnerabilities)
		{
			const penalty = this.VULNERABILITY_PENALTIES[vuln.severity] || 0.05;
			score -= penalty;
		}

		// Additional penalty for multiple vulnerabilities
		if (vulnerabilities.length > 5)
		{
			score -= 0.1; // Extra penalty for many vulnerabilities
		}

		return Math.max(0.0, score);
	}

	/**
	 * Get security grade based on score
	 */
	static getSecurityGrade(score: number): string
	{
		if (score >= 0.9) return 'A+';
		if (score >= 0.8) return 'A';
		if (score >= 0.7) return 'B';
		if (score >= 0.6) return 'C';
		if (score >= 0.5) return 'D';
		return 'F';
	}

	/**
	 * Get detailed security breakdown
	 */
	static getSecurityBreakdown(metrics: SecurityMetrics): any
	{
		const sslScore = this.calculateSSLScore(metrics);
		const headersScore = this.calculateSecurityHeadersScore(metrics.securityHeaders);
		const httpsScore = this.calculateHttpsScore(metrics.usesHttps);
		const vulnScore = this.calculateVulnerabilityScore(metrics.vulnerabilities);

		const totalScore = (
			sslScore * this.WEIGHTS.SSL_CERTIFICATE
			+ headersScore * this.WEIGHTS.SECURITY_HEADERS
			+ httpsScore * this.WEIGHTS.HTTPS_USAGE
			+ vulnScore * this.WEIGHTS.VULNERABILITIES
		);

		return {
			totalScore: Math.round(totalScore * 1000) / 1000,
			grade: this.getSecurityGrade(totalScore),
			breakdown: {
				sslCertificate: {
					score: Math.round(sslScore * 1000) / 1000,
					weight: this.WEIGHTS.SSL_CERTIFICATE,
					contribution: Math.round(sslScore * this.WEIGHTS.SSL_CERTIFICATE * 1000) / 1000,
					details: {
						grade: metrics.sslGrade || 'Unknown',
						issuer: metrics.sslIssuer || 'Unknown',
						expiration: metrics.sslExpiration,
					},
				},
				securityHeaders: {
					score: Math.round(headersScore * 1000) / 1000,
					weight: this.WEIGHTS.SECURITY_HEADERS,
					contribution: Math.round(headersScore * this.WEIGHTS.SECURITY_HEADERS * 1000) / 1000,
					details: metrics.securityHeaders || {},
				},
				httpsUsage: {
					score: Math.round(httpsScore * 1000) / 1000,
					weight: this.WEIGHTS.HTTPS_USAGE,
					contribution: Math.round(httpsScore * this.WEIGHTS.HTTPS_USAGE * 1000) / 1000,
					details: {
						usesHttps: metrics.usesHttps || false,
					},
				},
				vulnerabilities: {
					score: Math.round(vulnScore * 1000) / 1000,
					weight: this.WEIGHTS.VULNERABILITIES,
					contribution: Math.round(vulnScore * this.WEIGHTS.VULNERABILITIES * 1000) / 1000,
					details: {
						count: metrics.vulnerabilities?.length || 0,
						vulnerabilities: metrics.vulnerabilities || [],
					},
				},
			},
		};
	}

	/**
	 * Get security recommendations based on metrics
	 */
	static getSecurityRecommendations(metrics: SecurityMetrics): string[]
	{
		const recommendations: string[] = [];

		// SSL recommendations
		if (!metrics.sslGrade || this.SSL_GRADES[metrics.sslGrade.toUpperCase() as keyof typeof this.SSL_GRADES] < 0.8)
		{
			recommendations.push('Upgrade SSL certificate to achieve A or A+ grade');
		}

		if (metrics.sslExpiration)
		{
			const now = new Date();
			const expiration = new Date(metrics.sslExpiration);
			const daysUntilExpiry = Math.floor((expiration.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

			if (daysUntilExpiry < 30)
			{
				recommendations.push('SSL certificate expires soon - renew immediately');
			}
			else if (daysUntilExpiry < 90)
			{
				recommendations.push('SSL certificate expires within 90 days - plan renewal');
			}
		}

		// HTTPS recommendations
		if (!metrics.usesHttps)
		{
			recommendations.push('Enable HTTPS for all pages');
		}

		// Security headers recommendations
		if (!metrics.securityHeaders?.hsts)
		{
			recommendations.push('Implement HTTP Strict Transport Security (HSTS)');
		}

		if (!metrics.securityHeaders?.csp)
		{
			recommendations.push('Implement Content Security Policy (CSP)');
		}

		if (!metrics.securityHeaders?.xframe)
		{
			recommendations.push('Add X-Frame-Options header to prevent clickjacking');
		}

		if (!metrics.securityHeaders?.xssProtection)
		{
			recommendations.push('Enable X-XSS-Protection header');
		}

		if (!metrics.securityHeaders?.contentTypeOptions)
		{
			recommendations.push('Add X-Content-Type-Options header');
		}

		// Vulnerability recommendations
		if (metrics.vulnerabilities && metrics.vulnerabilities.length > 0)
		{
			const criticalVulns = metrics.vulnerabilities.filter(v => v.severity === 'critical');
			const highVulns = metrics.vulnerabilities.filter(v => v.severity === 'high');

			if (criticalVulns.length > 0)
			{
				recommendations.push(`Address ${criticalVulns.length} critical security vulnerabilities immediately`);
			}

			if (highVulns.length > 0)
			{
				recommendations.push(`Address ${highVulns.length} high-severity security vulnerabilities`);
			}
		}

		return recommendations;
	}
}

export type {
	SecurityMetrics,
	SecurityHeaders,
	Vulnerability,
	CertificateInfo,
};

export { SecurityScorer };

export default SecurityScorer;
