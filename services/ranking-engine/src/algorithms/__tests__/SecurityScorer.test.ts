import { describe, test, expect } from 'vitest';
import { SecurityScorer } from '../SecurityScorer';

describe('SecurityScorer', () =>
{
	describe('calculateScore', () =>
	{
		test('should calculate security score', () =>
		{
			const metrics = {
				sslGrade: 'A',
				usesHttps: true,
				securityHeaders: {
					hsts: true,
					csp: true,
					xframe: true,
					xssProtection: true,
					contentTypeOptions: true,
					referrerPolicy: true,
					permissionsPolicy: false,
				},
				vulnerabilities: [],
			};

			const score = SecurityScorer.calculateScore(metrics);

			expect(score).toBeGreaterThan(0);
			expect(score).toBeLessThanOrEqual(1);
		});

		test('should handle missing metrics', () =>
		{
			const score = SecurityScorer.calculateScore({});

			expect(score).toBeGreaterThan(0);
			expect(score).toBeLessThanOrEqual(1);
		});
	});

	describe('getSecurityGrade', () =>
	{
		test('should return correct grades', () =>
		{
			expect(SecurityScorer.getSecurityGrade(0.95)).toBe('A+');
			expect(SecurityScorer.getSecurityGrade(0.85)).toBe('A');
			expect(SecurityScorer.getSecurityGrade(0.75)).toBe('B');
			expect(SecurityScorer.getSecurityGrade(0.25)).toBe('F');
		});
	});
});
