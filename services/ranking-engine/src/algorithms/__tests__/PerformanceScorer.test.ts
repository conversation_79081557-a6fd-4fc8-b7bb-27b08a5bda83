import { describe, test, expect } from 'vitest';
import { PerformanceScorer } from '../PerformanceScorer';

describe('PerformanceScorer', () =>
{
	describe('calculateScore', () =>
	{
		test('should calculate performance score', () =>
		{
			const metrics = {
				loadTime: 1200,
				firstContentfulPaint: 800,
				responseTime: 1000,
				pageSize: 2 * 1024 * 1024,
				resourceCount: 45,
			};

			const score = PerformanceScorer.calculateScore(metrics);

			expect(score).toBeGreaterThan(0);
			expect(score).toBeLessThanOrEqual(1);
		});

		test('should handle missing metrics', () =>
		{
			const score = PerformanceScorer.calculateScore({});

			expect(score).toBeGreaterThan(0);
			expect(score).toBeLessThanOrEqual(1);
		});
	});

	describe('getPerformanceGrade', () =>
	{
		test('should return correct grades', () =>
		{
			expect(PerformanceScorer.getPerformanceGrade(0.95)).toBe('A+');
			expect(PerformanceScorer.getPerformanceGrade(0.85)).toBe('A');
			expect(PerformanceScorer.getPerformanceGrade(0.75)).toBe('B');
			expect(PerformanceScorer.getPerformanceGrade(0.25)).toBe('F');
		});
	});
});
