import { describe, test, expect } from 'vitest';
import { SEOScorer } from '../SEOScorer';

describe('SEOScorer', () =>
{
	describe('calculateScore', () =>
	{
		test('should calculate SEO score', () =>
		{
			const metrics = {
				metaTags: {
					title: 'Test Page Title',
					description: 'Test page description for SEO testing purposes.',
					canonical: 'https://example.com/test',
				},
				robotsTxt: {
					exists: true,
					isValid: true,
					allowsCrawling: true,
					hasDisallowRules: false,
					hasSitemapReference: true,
					userAgents: ['*'],
				},
				sitemap: {
					exists: true,
					isValid: true,
					urlCount: 150,
					hasImages: true,
					hasVideos: false,
					hasNews: false,
				},
				contentMetrics: {
					wordCount: 1200,
					headingStructure: {
						h1Count: 1,
						h2Count: 4,
						h3Count: 8,
						h4Count: 2,
						h5Count: 0,
						h6Count: 0,
						hasProperHierarchy: true,
					},
					imageCount: 12,
					imageAltTextRatio: 0.9,
					internalLinkCount: 15,
					externalLinkCount: 5,
					readabilityScore: 65,
					languageDetected: 'en',
				},
				technicalSEO: {
					pageLoadSpeed: 1.2,
					mobileOptimized: true,
					httpsEnabled: true,
					hasCanonical: true,
					hasHreflang: false,
					breadcrumbsPresent: true,
					schemaMarkupPresent: true,
				},
			};

			const score = SEOScorer.calculateScore(metrics);

			expect(score).toBeGreaterThan(0);
			expect(score).toBeLessThanOrEqual(1);
		});

		test('should handle missing metrics', () =>
		{
			const score = SEOScorer.calculateScore({});

			expect(score).toBeGreaterThan(0);
			expect(score).toBeLessThanOrEqual(1);
		});
	});

	describe('getSEOGrade', () =>
	{
		test('should return correct grades', () =>
		{
			expect(SEOScorer.getSEOGrade(0.95)).toBe('A+');
			expect(SEOScorer.getSEOGrade(0.85)).toBe('A');
			expect(SEOScorer.getSEOGrade(0.75)).toBe('B');
			expect(SEOScorer.getSEOGrade(0.25)).toBe('F');
		});
	});
});
