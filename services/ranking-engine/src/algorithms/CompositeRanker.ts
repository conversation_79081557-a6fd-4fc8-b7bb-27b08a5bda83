import { Logger } from '@shared';
import { PerformanceScorer, PerformanceMetrics, CoreWebVitals } from './PerformanceScorer';
import { SecurityScorer, SecurityMetrics } from './SecurityScorer';
import { SEOScorer, SEOMetrics } from './SEOScorer';

const logger = Logger.getLogger('CompositeRanker');

export interface DomainData
{
	domain: string;
	category?: string;
	country?: string;
	performanceMetrics?: PerformanceMetrics;
	coreWebVitals?: CoreWebVitals;
	securityMetrics?: SecurityMetrics;
	seoMetrics?: SEOMetrics;
	technicalMetrics?: TechnicalMetrics;
	backlinkMetrics?: BacklinkMetrics;
	trafficEstimate?: number;
	lastUpdated?: Date;
}

export interface TechnicalMetrics
{
	dnsResponseTime?: number;
	serverResponseTime?: number;
	supportsIpv6?: boolean;
	hasCdn?: boolean;
	compressionEnabled?: boolean;
	keepAliveEnabled?: boolean;
	serverSoftware?: string;
	hostingProvider?: string;
	uptimePercentage?: number;
}

export interface BacklinkMetrics
{
	totalBacklinks: number;
	uniqueDomains: number;
	averageAuthority: number;
	followLinks: number;
	nofollowLinks: number;
	topReferringDomains: string[];
}

export interface RankingWeights
{
	PERFORMANCE: number;
	SECURITY: number;
	SEO: number;
	TECHNICAL: number;
	BACKLINKS: number;
}

export interface CompositeScore
{
	domain: string;
	overallScore: number;
	grade: string;
	globalRank?: number;
	categoryRank?: number;
	scores: {
		performance: number;
		security: number;
		seo: number;
		technical: number;
		backlinks: number;
	};
	breakdown: {
		performance: ScoreBreakdown;
		security: ScoreBreakdown;
		seo: ScoreBreakdown;
		technical: ScoreBreakdown;
		backlinks: ScoreBreakdown;
	};
	lastCalculated: Date;
}

export interface ScoreBreakdown
{
	score: number;
	weight: number;
	contribution: number;
	grade: string;
}

export interface RankingResult
{
	domain: string;
	rank: number;
	score: number;
	category?: string;
	previousRank?: number;
	rankChange?: number;
}

/**
 * Composite ranking algorithm that combines all scoring dimensions
 */
class CompositeRanker
{
	private weights: RankingWeights;

	constructor(weights?: RankingWeights)
	{
		this.weights = weights || {
			PERFORMANCE: 0.25,
			SECURITY: 0.20,
			SEO: 0.20,
			TECHNICAL: 0.15,
			BACKLINKS: 0.20,
		};

		// Validate weights sum to 1.0
		const totalWeight = Object.values(this.weights).reduce((sum, weight) => sum + weight, 0);
		if (Math.abs(totalWeight - 1.0) > 0.001)
		{
			logger.warn('Ranking weights do not sum to 1.0', { weights: this.weights, total: totalWeight });
		}
	}

	/**
	 * Calculate composite score for a single domain
	 */
	calculateCompositeScore(domainData: DomainData): CompositeScore
	{
		try
		{
			// Calculate individual scores
			const performanceScore = this.calculatePerformanceScore(domainData);
			const securityScore = this.calculateSecurityScore(domainData);
			const seoScore = this.calculateSEOScore(domainData);
			const technicalScore = this.calculateTechnicalScore(domainData);
			const backlinkScore = this.calculateBacklinkScore(domainData);

			// Calculate weighted overall score
			const overallScore = (
				performanceScore * this.weights.PERFORMANCE
				+ securityScore * this.weights.SECURITY
				+ seoScore * this.weights.SEO
				+ technicalScore * this.weights.TECHNICAL
				+ backlinkScore * this.weights.BACKLINKS
			);

			const finalScore = Math.round(overallScore * 1000) / 1000;

			const result: CompositeScore = {
				domain: domainData.domain,
				overallScore: finalScore,
				grade: this.getOverallGrade(finalScore),
				scores: {
					performance: Math.round(performanceScore * 1000) / 1000,
					security: Math.round(securityScore * 1000) / 1000,
					seo: Math.round(seoScore * 1000) / 1000,
					technical: Math.round(technicalScore * 1000) / 1000,
					backlinks: Math.round(backlinkScore * 1000) / 1000,
				},
				breakdown: {
					performance: {
						score: Math.round(performanceScore * 1000) / 1000,
						weight: this.weights.PERFORMANCE,
						contribution: Math.round(performanceScore * this.weights.PERFORMANCE * 1000) / 1000,
						grade: PerformanceScorer.getPerformanceGrade(performanceScore),
					},
					security: {
						score: Math.round(securityScore * 1000) / 1000,
						weight: this.weights.SECURITY,
						contribution: Math.round(securityScore * this.weights.SECURITY * 1000) / 1000,
						grade: SecurityScorer.getSecurityGrade(securityScore),
					},
					seo: {
						score: Math.round(seoScore * 1000) / 1000,
						weight: this.weights.SEO,
						contribution: Math.round(seoScore * this.weights.SEO * 1000) / 1000,
						grade: SEOScorer.getSEOGrade(seoScore),
					},
					technical: {
						score: Math.round(technicalScore * 1000) / 1000,
						weight: this.weights.TECHNICAL,
						contribution: Math.round(technicalScore * this.weights.TECHNICAL * 1000) / 1000,
						grade: this.getTechnicalGrade(technicalScore),
					},
					backlinks: {
						score: Math.round(backlinkScore * 1000) / 1000,
						weight: this.weights.BACKLINKS,
						contribution: Math.round(backlinkScore * this.weights.BACKLINKS * 1000) / 1000,
						grade: this.getBacklinkGrade(backlinkScore),
					},
				},
				lastCalculated: new Date(),
			};

			logger.debug('Composite score calculated', {
				domain: domainData.domain,
				overallScore: finalScore,
				scores: result.scores,
			});

			return result;
		}
		catch (error)
		{
			logger.error('Error calculating composite score:', error);
			throw error;
		}
	}

	/**
	 * Calculate performance score using PerformanceScorer
	 */
	private calculatePerformanceScore(domainData: DomainData): number
	{
		if (!domainData.performanceMetrics)
		{
			return 0.5; // Default score when no performance data
		}

		return PerformanceScorer.calculateScore(
			domainData.performanceMetrics,
			domainData.coreWebVitals,
		);
	}

	/**
	 * Calculate security score using SecurityScorer
	 */
	private calculateSecurityScore(domainData: DomainData): number
	{
		if (!domainData.securityMetrics)
		{
			return 0.3; // Conservative default for security
		}

		return SecurityScorer.calculateScore(domainData.securityMetrics);
	}

	/**
	 * Calculate SEO score using SEOScorer
	 */
	private calculateSEOScore(domainData: DomainData): number
	{
		if (!domainData.seoMetrics)
		{
			return 0.5; // Default score when no SEO data
		}

		return SEOScorer.calculateScore(domainData.seoMetrics);
	}

	/**
	 * Calculate technical score based on infrastructure metrics
	 */
	private calculateTechnicalScore(domainData: DomainData): number
	{
		if (!domainData.technicalMetrics)
		{
			return 0.5; // Default score when no technical data
		}

		const metrics = domainData.technicalMetrics;
		let score = 0.3; // Base score

		// DNS response time scoring
		if (metrics.dnsResponseTime !== undefined)
		{
			if (metrics.dnsResponseTime <= 50)
			{
				score += 0.15; // Excellent DNS performance
			}
			else if (metrics.dnsResponseTime <= 100)
			{
				score += 0.1; // Good DNS performance
			}
			else if (metrics.dnsResponseTime <= 200)
			{
				score += 0.05; // Fair DNS performance
			}
		}

		// Server response time scoring
		if (metrics.serverResponseTime !== undefined)
		{
			if (metrics.serverResponseTime <= 200)
			{
				score += 0.15; // Excellent server response
			}
			else if (metrics.serverResponseTime <= 500)
			{
				score += 0.1; // Good server response
			}
			else if (metrics.serverResponseTime <= 1000)
			{
				score += 0.05; // Fair server response
			}
		}

		// IPv6 support
		if (metrics.supportsIpv6)
		{
			score += 0.1;
		}

		// CDN usage
		if (metrics.hasCdn)
		{
			score += 0.15;
		}

		// Compression enabled
		if (metrics.compressionEnabled)
		{
			score += 0.05;
		}

		// Keep-alive enabled
		if (metrics.keepAliveEnabled)
		{
			score += 0.05;
		}

		// Uptime percentage
		if (metrics.uptimePercentage !== undefined)
		{
			if (metrics.uptimePercentage >= 99.9)
			{
				score += 0.1; // Excellent uptime
			}
			else if (metrics.uptimePercentage >= 99.5)
			{
				score += 0.07; // Good uptime
			}
			else if (metrics.uptimePercentage >= 99.0)
			{
				score += 0.05; // Fair uptime
			}
		}

		return Math.min(1.0, Math.max(0.0, score));
	}

	/**
	 * Calculate backlink score based on link metrics
	 */
	private calculateBacklinkScore(domainData: DomainData): number
	{
		if (!domainData.backlinkMetrics)
		{
			return 0.3; // Default score when no backlink data
		}

		const metrics = domainData.backlinkMetrics;
		let score = 0.1; // Base score

		// Total backlinks scoring
		const totalBacklinks = metrics.totalBacklinks || 0;
		if (totalBacklinks > 10000)
		{
			score += 0.3;
		}
		else if (totalBacklinks > 1000)
		{
			score += 0.25;
		}
		else if (totalBacklinks > 100)
		{
			score += 0.2;
		}
		else if (totalBacklinks > 10)
		{
			score += 0.15;
		}
		else if (totalBacklinks > 0)
		{
			score += 0.1;
		}

		// Unique domains scoring
		const uniqueDomains = metrics.uniqueDomains || 0;
		if (uniqueDomains > 1000)
		{
			score += 0.25;
		}
		else if (uniqueDomains > 100)
		{
			score += 0.2;
		}
		else if (uniqueDomains > 50)
		{
			score += 0.15;
		}
		else if (uniqueDomains > 10)
		{
			score += 0.1;
		}
		else if (uniqueDomains > 0)
		{
			score += 0.05;
		}

		// Average authority scoring
		const avgAuthority = metrics.averageAuthority || 0;
		if (avgAuthority > 80)
		{
			score += 0.2;
		}
		else if (avgAuthority > 60)
		{
			score += 0.15;
		}
		else if (avgAuthority > 40)
		{
			score += 0.1;
		}
		else if (avgAuthority > 20)
		{
			score += 0.05;
		}

		// Follow vs nofollow ratio
		const totalLinks = metrics.followLinks + metrics.nofollowLinks;
		if (totalLinks > 0)
		{
			const followRatio = metrics.followLinks / totalLinks;
			if (followRatio > 0.8)
			{
				score += 0.15; // High follow ratio is good
			}
			else if (followRatio > 0.6)
			{
				score += 0.1;
			}
			else if (followRatio > 0.4)
			{
				score += 0.05;
			}
		}

		return Math.min(1.0, Math.max(0.0, score));
	}

	/**
	 * Calculate global rankings for a list of domains
	 */
	calculateGlobalRankings(domains: DomainData[]): RankingResult[]
	{
		try
		{
			logger.info(`Calculating global rankings for ${domains.length} domains`);

			// Calculate composite scores for all domains
			const scoredDomains = domains.map(domain => ({
				domain: domain.domain,
				score: this.calculateCompositeScore(domain).overallScore,
				category: domain.category,
			}));

			// Sort by score (highest first)
			scoredDomains.sort((a, b) => b.score - a.score);

			// Assign ranks
			const rankings: RankingResult[] = scoredDomains.map((domain, index) => ({
				domain: domain.domain,
				rank: index + 1,
				score: domain.score,
				category: domain.category,
			}));

			logger.info(`Global rankings calculated for ${rankings.length} domains`);
			return rankings;
		}
		catch (error)
		{
			logger.error('Error calculating global rankings:', error);
			throw error;
		}
	}

	/**
	 * Calculate category-based rankings
	 */
	calculateCategoryRankings(domains: DomainData[], category: string): RankingResult[]
	{
		try
		{
			logger.info(`Calculating category rankings for category: ${category}`);

			// Filter domains by category
			const categoryDomains = domains.filter(domain => domain.category === category);

			if (categoryDomains.length === 0)
			{
				logger.warn(`No domains found for category: ${category}`);
				return [];
			}

			// Calculate composite scores for category domains
			const scoredDomains = categoryDomains.map(domain => ({
				domain: domain.domain,
				score: this.calculateCompositeScore(domain).overallScore,
				category: domain.category,
			}));

			// Sort by score (highest first)
			scoredDomains.sort((a, b) => b.score - a.score);

			// Assign category ranks
			const rankings: RankingResult[] = scoredDomains.map((domain, index) => ({
				domain: domain.domain,
				rank: index + 1,
				score: domain.score,
				category,
			}));

			logger.info(`Category rankings calculated for ${rankings.length} domains in category: ${category}`);
			return rankings;
		}
		catch (error)
		{
			logger.error(`Error calculating category rankings for ${category}:`, error);
			throw error;
		}
	}

	/**
	 * Calculate rankings with rank change tracking
	 */
	calculateRankingsWithHistory(
		domains: DomainData[],
		previousRankings?: Map<string, number>,
		category?: string,
	): RankingResult[]
	{
		try
		{
			// Calculate current rankings
			const currentRankings = category
				? this.calculateCategoryRankings(domains, category)
				: this.calculateGlobalRankings(domains);

			// Add rank change information if previous rankings available
			if (previousRankings)
			{
				currentRankings.forEach((ranking) =>
				{
					const previousRank = previousRankings.get(ranking.domain);
					if (previousRank !== undefined)
					{
						ranking.previousRank = previousRank;
						ranking.rankChange = previousRank - ranking.rank; // Positive = moved up
					}
				});
			}

			return currentRankings;
		}
		catch (error)
		{
			logger.error('Error calculating rankings with history:', error);
			throw error;
		}
	}

	/**
	 * Get overall grade based on composite score
	 */
	private getOverallGrade(score: number): string
	{
		if (score >= 0.9) return 'A+';
		if (score >= 0.8) return 'A';
		if (score >= 0.7) return 'B';
		if (score >= 0.6) return 'C';
		if (score >= 0.5) return 'D';
		return 'F';
	}

	/**
	 * Get technical grade based on technical score
	 */
	private getTechnicalGrade(score: number): string
	{
		if (score >= 0.9) return 'A+';
		if (score >= 0.8) return 'A';
		if (score >= 0.7) return 'B';
		if (score >= 0.6) return 'C';
		if (score >= 0.5) return 'D';
		return 'F';
	}

	/**
	 * Get backlink grade based on backlink score
	 */
	private getBacklinkGrade(score: number): string
	{
		if (score >= 0.9) return 'A+';
		if (score >= 0.8) return 'A';
		if (score >= 0.7) return 'B';
		if (score >= 0.6) return 'C';
		if (score >= 0.5) return 'D';
		return 'F';
	}

	/**
	 * Update ranking weights
	 */
	updateWeights(newWeights: Partial<RankingWeights>): void
	{
		this.weights = { ...this.weights, ...newWeights };

		// Validate weights sum to 1.0
		const totalWeight = Object.values(this.weights).reduce((sum, weight) => sum + weight, 0);
		if (Math.abs(totalWeight - 1.0) > 0.001)
		{
			logger.warn('Updated ranking weights do not sum to 1.0', {
				weights: this.weights,
				total: totalWeight,
			});
		}

		logger.info('Ranking weights updated', { weights: this.weights });
	}

	/**
	 * Get current ranking weights
	 */
	getWeights(): RankingWeights
	{
		return { ...this.weights };
	}

	/**
	 * Get ranking statistics for a set of domains
	 */
	getRankingStatistics(rankings: RankingResult[]): any
	{
		if (rankings.length === 0)
		{
			return {
				totalDomains: 0,
				averageScore: 0,
				medianScore: 0,
				topScore: 0,
				bottomScore: 0,
				gradeDistribution: {},
			};
		}

		const scores = rankings.map(r => r.score).sort((a, b) => b - a);
		const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
		const medianScore = scores[Math.floor(scores.length / 2)];

		// Grade distribution
		const gradeDistribution: Record<string, number> = {};
		rankings.forEach((ranking) =>
		{
			const grade = this.getOverallGrade(ranking.score);
			gradeDistribution[grade] = (gradeDistribution[grade] || 0) + 1;
		});

		return {
			totalDomains: rankings.length,
			averageScore: Math.round(averageScore * 1000) / 1000,
			medianScore: Math.round(medianScore * 1000) / 1000,
			topScore: scores[0],
			bottomScore: scores[scores.length - 1],
			gradeDistribution,
		};
	}
}

export type {
	DomainData,
	TechnicalMetrics,
	BacklinkMetrics,
	RankingWeights,
	CompositeScore,
	ScoreBreakdown,
	RankingResult,
};

export { CompositeRanker };

export default CompositeRanker;
