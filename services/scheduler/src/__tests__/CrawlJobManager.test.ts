import {
	describe, test, it, expect, beforeEach, afterEach, vi,
} from 'vitest';
import CrawlJobManager, { CrawlJobRequest } from '../CrawlJobManager';
import JobScheduler from '../JobScheduler';

// Mock the shared modules
vi.mock('@shared', () => ({
	DatabaseManager: vi.fn().mockImplementation(() => ({
		initialize: vi.fn().mockResolvedValue(undefined),
		close: vi.fn().mockResolvedValue(undefined),
		scyllaClient: {
			execute: vi.fn().mockResolvedValue({ rows: [] }),
		},
		healthCheck: vi.fn().mockResolvedValue({ status: 'healthy' }),
	})),
	Logger: {
		getLogger: vi.fn(() => ({
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
		})),
	},
	Config: {
		getAll: vi.fn(() => ({
			REDIS_HOST: 'localhost',
			REDIS_PORT: '6379',
			REDIS_DB: '0',
		})),
	},
	Constants: {
		JOB_QUEUES: {
			DOMAIN_CRAWL: 'queue:domain:crawl',
			RANKING_UPDATE: 'queue:ranking:update',
			TRAFFIC_ANALYSIS: 'queue:traffic:analysis',
			BACKLINK_ANALYSIS: 'queue:backlink:analysis',
			MANTICORE_SYNC: 'queue:manticore:sync',
			MAINTENANCE: 'queue:maintenance',
		},
	},
}));

// Mock redis-smq
vi.mock('redis-smq', () => ({
	Producer: vi.fn().mockImplementation(() => ({
		run: vi.fn().mockResolvedValue(undefined),
		produce: vi.fn().mockResolvedValue(undefined),
		shutdown: vi.fn().mockResolvedValue(undefined),
	})),
	Consumer: vi.fn().mockImplementation(() => ({
		consume: vi.fn(),
		run: vi.fn().mockResolvedValue(undefined),
		shutdown: vi.fn().mockResolvedValue(undefined),
	})),
}));

describe('CrawlJobManager', () =>
{
	let jobScheduler: JobScheduler;
	let crawlJobManager: CrawlJobManager;

	beforeEach(async () =>
	{
		jobScheduler = new JobScheduler();
		await jobScheduler.initialize();

		crawlJobManager = new CrawlJobManager(jobScheduler);
		await crawlJobManager.initialize();
	});

	afterEach(async () =>
	{
		if (crawlJobManager)
		{
			await crawlJobManager.shutdown();
		}
		if (jobScheduler)
		{
			await jobScheduler.shutdown();
		}
	});

	describe('job creation', () =>
	{
		it('should create a crawl job successfully', async () =>
		{
			const request: CrawlJobRequest = {
				domain: 'example.com',
				crawlType: 'full',
				priority: 'medium',
				requestedBy: 'test',
				metadata: { test: true },
			};

			const jobId = await crawlJobManager.createCrawlJob(request);

			expect(jobId).toBeDefined();
			expect(typeof jobId).toBe('string');

			// Verify job status was created
			const jobStatus = crawlJobManager.getJobStatus(jobId);

			expect(jobStatus).toBeDefined();
			expect(jobStatus?.domain).toBe('example.com');
			expect(jobStatus?.crawlType).toBe('full');
			expect(jobStatus?.priority).toBe('medium');
		});

		it('should create batch crawl jobs', async () =>
		{
			const requests: CrawlJobRequest[] = [
				{
					domain: 'example1.com',
					crawlType: 'quick',
					priority: 'high',
				},
				{
					domain: 'example2.com',
					crawlType: 'security',
					priority: 'low',
				},
			];

			const jobIds = await crawlJobManager.createBatchCrawlJobs(requests);

			expect(jobIds).toHaveLength(2);
			expect(jobIds.every(id => typeof id === 'string')).toBe(true);

			// Verify both jobs were created
			const job1Status = crawlJobManager.getJobStatus(jobIds[0]);
			const job2Status = crawlJobManager.getJobStatus(jobIds[1]);

			expect(job1Status?.domain).toBe('example1.com');
			expect(job2Status?.domain).toBe('example2.com');
		});
	});

	describe('job status management', () =>
	{
		let jobId: string;

		beforeEach(async () =>
		{
			const request: CrawlJobRequest = {
				domain: 'test.com',
				crawlType: 'full',
				priority: 'medium',
			};
			jobId = await crawlJobManager.createCrawlJob(request);
		});

		it('should get job status by ID', () =>
		{
			const status = crawlJobManager.getJobStatus(jobId);

			expect(status).toBeDefined();
			expect(status?.id).toBe(jobId);
		});

		it('should get job statuses by domain', () =>
		{
			const statuses = crawlJobManager.getJobStatusesByDomain('test.com');

			expect(statuses).toHaveLength(1);
			expect(statuses[0].domain).toBe('test.com');
		});

		it('should update job status', async () =>
		{
			await crawlJobManager.updateJobStatus(jobId, 'processing', {
				startedAt: new Date(),
				progress: 50,
			});

			const status = crawlJobManager.getJobStatus(jobId);

			expect(status?.status).toBe('processing');
			expect(status?.progress).toBe(50);
			expect(status?.startedAt).toBeDefined();
		});
	});

	describe('job progress tracking', () =>
	{
		let jobId: string;

		beforeEach(async () =>
		{
			const request: CrawlJobRequest = {
				domain: 'progress-test.com',
				crawlType: 'full',
				priority: 'medium',
			};
			jobId = await crawlJobManager.createCrawlJob(request);
		});

		it('should update job progress', async () =>
		{
			await crawlJobManager.updateJobProgress(
				jobId,
				'progress-test.com',
				'dns_lookup',
				25,
				'Performing DNS lookup',
				{ recordsFound: 5 },
			);

			const progress = crawlJobManager.getJobProgress(jobId);

			expect(progress).toHaveLength(1);
			expect(progress[0].stage).toBe('dns_lookup');
			expect(progress[0].progress).toBe(25);
			expect(progress[0].message).toBe('Performing DNS lookup');
		});

		it('should maintain progress history', async () =>
		{
			// Add multiple progress updates
			await crawlJobManager.updateJobProgress(jobId, 'progress-test.com', 'stage1', 25, 'Stage 1');
			await crawlJobManager.updateJobProgress(jobId, 'progress-test.com', 'stage2', 50, 'Stage 2');
			await crawlJobManager.updateJobProgress(jobId, 'progress-test.com', 'stage3', 75, 'Stage 3');

			const progress = crawlJobManager.getJobProgress(jobId);

			expect(progress).toHaveLength(3);
			expect(progress.map(p => p.stage)).toEqual(['stage1', 'stage2', 'stage3']);
		});
	});

	describe('job control operations', () =>
	{
		let jobId: string;

		beforeEach(async () =>
		{
			const request: CrawlJobRequest = {
				domain: 'control-test.com',
				crawlType: 'full',
				priority: 'medium',
			};
			jobId = await crawlJobManager.createCrawlJob(request);
		});

		it('should cancel a job', async () =>
		{
			const result = await crawlJobManager.cancelJob(jobId);

			expect(result).toBe(true);

			const status = crawlJobManager.getJobStatus(jobId);

			expect(status?.status).toBe('cancelled');
		});

		it('should not cancel a processing job', async () =>
		{
			// Set job to processing
			await crawlJobManager.updateJobStatus(jobId, 'processing');

			const result = await crawlJobManager.cancelJob(jobId);

			expect(result).toBe(false);
		});

		it('should retry a failed job', async () =>
		{
			// Set job to failed
			await crawlJobManager.updateJobStatus(jobId, 'failed', {
				errorMessage: 'Test error',
				retryCount: 1,
			});

			const result = await crawlJobManager.retryJob(jobId);

			expect(result).toBe(true);

			const status = crawlJobManager.getJobStatus(jobId);

			expect(status?.status).toBe('queued');
			expect(status?.retryCount).toBe(2);
		});
	});

	describe('statistics and monitoring', () =>
	{
		it('should return job statistics by status', async () =>
		{
			// Create jobs with different statuses
			const request1: CrawlJobRequest = { domain: 'stats1.com', crawlType: 'full', priority: 'medium' };
			const request2: CrawlJobRequest = { domain: 'stats2.com', crawlType: 'quick', priority: 'high' };

			const jobId1 = await crawlJobManager.createCrawlJob(request1);
			const jobId2 = await crawlJobManager.createCrawlJob(request2);

			await crawlJobManager.updateJobStatus(jobId1, 'completed');
			await crawlJobManager.updateJobStatus(jobId2, 'failed');

			const stats = crawlJobManager.getJobStatsByStatus();

			expect(stats.completed).toBeGreaterThan(0);
			expect(stats.failed).toBeGreaterThan(0);
		});

		it('should return queue statistics', async () =>
		{
			const queueStats = await crawlJobManager.getQueueStats();

			expect(Array.isArray(queueStats)).toBe(true);
		});

		it('should perform health check', async () =>
		{
			const health = await crawlJobManager.healthCheck();

			expect(health).toHaveProperty('status');
			expect(health).toHaveProperty('activeJobs');
			expect(health).toHaveProperty('queueStats');
		});
	});

	describe('cleanup operations', () =>
	{
		it('should cleanup old jobs', async () =>
		{
			// Create a job and mark it as completed
			const request: CrawlJobRequest = {
				domain: 'cleanup-test.com',
				crawlType: 'full',
				priority: 'medium',
			};
			const jobId = await crawlJobManager.createCrawlJob(request);
			await crawlJobManager.updateJobStatus(jobId, 'completed');

			// Cleanup should not remove recent jobs
			const cleanedCount = await crawlJobManager.cleanupOldJobs(30);

			expect(typeof cleanedCount).toBe('number');
		});
	});
});
