import HealthChecker from '@shared/monitoring/HealthChecker';
import MetricsCollector from '@shared/monitoring/MetricsCollector';
import { logger } from '@shared';

const schedulerLogger = logger.getLogger('SchedulerHealth');
const healthChecker = new HealthChecker('scheduler', process.env.npm_package_version || '1.0.0');
const metricsCollector = new MetricsCollector();

class SchedulerHealthService
{
	/**
	 * Get health status for scheduler service
	 */
	async getHealthStatus(includeMetrics: boolean = false)
	{
		try
		{
			const baseHealth = await healthChecker.getHealthStatus(includeMetrics);

			// Add scheduler-specific health checks
			const schedulerServices = await this.checkSchedulerServices();

			return {
				...baseHealth,
				services: {
					...baseHealth.services,
					...schedulerServices,
				},
			};
		}
		catch (error)
		{
			schedulerLogger.error('Scheduler health check failed:', error);
			throw error;
		}
	}

	/**
	 * Check scheduler-specific services
	 */
	private async checkSchedulerServices()
	{
		const services: any = {};
		const timestamp = new Date().toISOString();

		// Check job queue status
		try
		{
			const queueStatus = await this.checkJobQueues();
			services.jobQueues = queueStatus;
		}
		catch (error)
		{
			services.jobQueues = {
				status: 'unhealthy',
				lastCheck: timestamp,
				error: (error as Error).message,
			};
		}

		// Check cron jobs status
		try
		{
			const cronStatus = await this.checkCronJobs();
			services.cronJobs = cronStatus;
		}
		catch (error)
		{
			services.cronJobs = {
				status: 'degraded',
				lastCheck: timestamp,
				error: (error as Error).message,
			};
		}

		// Check worker processes
		try
		{
			const workerStatus = await this.checkWorkers();
			services.workers = workerStatus;
		}
		catch (error)
		{
			services.workers = {
				status: 'degraded',
				lastCheck: timestamp,
				error: (error as Error).message,
			};
		}

		return services;
	}

	/**
	 * Check job queue status
	 */
	private async checkJobQueues()
	{
		const timestamp = new Date().toISOString();

		try
		{
			// This would check the actual job queue status
			// For now, we'll simulate queue health checks
			const queueNames = [
				'domain-crawl',
				'ranking-update',
				'data-sync',
				'maintenance',
			];

			const queueStats = {
				totalQueues: queueNames.length,
				activeQueues: queueNames.length,
				pendingJobs: 0, // This would be actual pending job count
				processingJobs: 0, // This would be actual processing job count
				failedJobs: 0, // This would be actual failed job count
			};

			return {
				status: 'healthy',
				lastCheck: timestamp,
				details: queueStats,
			};
		}
		catch (error)
		{
			return {
				status: 'unhealthy',
				lastCheck: timestamp,
				error: (error as Error).message,
			};
		}
	}

	/**
	 * Check cron jobs status
	 */
	private async checkCronJobs()
	{
		const timestamp = new Date().toISOString();

		try
		{
			// This would check the status of scheduled cron jobs
			const cronJobs = [
				{
					name: 'domain-refresh', schedule: '0 */6 * * *', lastRun: new Date(), status: 'active',
				},
				{
					name: 'ranking-recalc', schedule: '0 2 * * *', lastRun: new Date(), status: 'active',
				},
				{
					name: 'cleanup-old-data', schedule: '0 3 * * 0', lastRun: new Date(), status: 'active',
				},
			];

			return {
				status: 'healthy',
				lastCheck: timestamp,
				details: {
					totalJobs: cronJobs.length,
					activeJobs: cronJobs.filter(job => job.status === 'active').length,
					jobs: cronJobs,
				},
			};
		}
		catch (error)
		{
			return {
				status: 'degraded',
				lastCheck: timestamp,
				error: (error as Error).message,
			};
		}
	}

	/**
	 * Check worker processes
	 */
	private async checkWorkers()
	{
		const timestamp = new Date().toISOString();

		try
		{
			// This would check the status of worker processes
			const workerStats = {
				totalWorkers: 4,
				activeWorkers: 4,
				idleWorkers: 2,
				busyWorkers: 2,
				averageJobTime: 5000, // milliseconds
			};

			return {
				status: 'healthy',
				lastCheck: timestamp,
				details: workerStats,
			};
		}
		catch (error)
		{
			return {
				status: 'degraded',
				lastCheck: timestamp,
				error: (error as Error).message,
			};
		}
	}

	/**
	 * Get scheduler-specific metrics
	 */
	async getSchedulerMetrics()
	{
		try
		{
			const summary = await metricsCollector.getMetricsSummary([
				'jobs_scheduled_total',
				'jobs_processed_total',
				'job_processing_duration',
				'queue_size',
				'worker_utilization',
				'cron_executions_total',
			]);

			return {
				timestamp: new Date().toISOString(),
				summary,
			};
		}
		catch (error)
		{
			schedulerLogger.error('Failed to get scheduler metrics:', error);
			throw error;
		}
	}

	/**
	 * Check if scheduler is ready to process jobs
	 */
	async isReady(): Promise<boolean>
	{
		try
		{
			const health = await this.getHealthStatus(false);

			// Scheduler is ready if Redis is healthy and job queues are working
			const criticalServices = ['redis', 'jobQueues'];
			const criticalHealthy = criticalServices.every(service => health.services[service]?.status === 'healthy');

			return criticalHealthy && health.status !== 'unhealthy';
		}
		catch (error)
		{
			schedulerLogger.error('Scheduler readiness check failed:', error);
			return false;
		}
	}

	/**
	 * Record job scheduling metrics
	 */
	recordJobSchedulingMetrics(jobType: string, priority: string, success: boolean)
	{
		metricsCollector.counter('jobs_scheduled_total', 1, {
			job_type: jobType,
			priority,
			success: success.toString(),
		});
	}

	/**
	 * Record job processing metrics
	 */
	recordJobProcessingMetrics(jobType: string, duration: number, success: boolean)
	{
		metricsCollector.counter('jobs_processed_total', 1, {
			job_type: jobType,
			success: success.toString(),
		});

		metricsCollector.timer('job_processing_duration', duration, {
			job_type: jobType,
		});
	}

	/**
	 * Record queue metrics
	 */
	recordQueueMetrics(queueName: string, size: number, processingCount: number)
	{
		metricsCollector.gauge('queue_size', size, {
			queue: queueName,
		});

		metricsCollector.gauge('queue_processing_count', processingCount, {
			queue: queueName,
		});
	}

	/**
	 * Record worker metrics
	 */
	recordWorkerMetrics(workerId: string, utilization: number, jobsProcessed: number)
	{
		metricsCollector.gauge('worker_utilization', utilization, {
			worker_id: workerId,
		});

		metricsCollector.counter('worker_jobs_processed', jobsProcessed, {
			worker_id: workerId,
		});
	}

	/**
	 * Record cron execution metrics
	 */
	recordCronExecutionMetrics(jobName: string, duration: number, success: boolean)
	{
		metricsCollector.counter('cron_executions_total', 1, {
			job_name: jobName,
			success: success.toString(),
		});

		metricsCollector.timer('cron_execution_duration', duration, {
			job_name: jobName,
		});
	}

	/**
	 * Cleanup resources
	 */
	async cleanup()
	{
		await metricsCollector.stop();
	}
}

const schedulerHealthService = new SchedulerHealthService();

export { SchedulerHealthService, schedulerHealthService };

export default schedulerHealthService;
