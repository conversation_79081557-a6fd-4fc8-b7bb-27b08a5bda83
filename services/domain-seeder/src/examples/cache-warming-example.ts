import type DatabaseManager from '@shared/database/DatabaseManager';
import { CompositeDomainRepository, type CacheWarmingSource } from '../repositories';

/**
 * Example demonstrating Redis cache warming functionality
 * This shows how to warm the cache with domains from multiple sources
 */

/**
 * Example cache warming source that simulates a domain list
 */
class ExampleDomainSource implements CacheWarmingSource
{
	name = 'example-domains';

	private domains = [
		'example.com',
		'test.com',
		'demo.com',
		'sample.org',
		'placeholder.net',
		'mock.io',
		'fake.co',
		'dummy.info',
		'temp.biz',
		'trial.me',
	];

	async getDomains(offset: number, limit: number): Promise<string[]>
	{
		const end = Math.min(offset + limit, this.domains.length);
		return this.domains.slice(offset, end);
	}

	async getTotalCount(): Promise<number>
	{
		return this.domains.length;
	}
}

/**
 * Example cache warming source that simulates popular domains
 */
class PopularDomainsSource implements CacheWarmingSource
{
	name = 'popular-domains';

	private popularDomains = [
		'google.com',
		'facebook.com',
		'youtube.com',
		'amazon.com',
		'wikipedia.org',
		'twitter.com',
		'instagram.com',
		'linkedin.com',
		'reddit.com',
		'netflix.com',
	];

	async getDomains(offset: number, limit: number): Promise<string[]>
	{
		const end = Math.min(offset + limit, this.popularDomains.length);
		return this.popularDomains.slice(offset, end);
	}

	async getTotalCount(): Promise<number>
	{
		return this.popularDomains.length;
	}
}

/**
 * Example function demonstrating cache warming
 */
export async function demonstrateCacheWarming(dbManager: DatabaseManager): Promise<void>
{
	console.log('🚀 Starting Redis Cache Warming Example');

	// Create composite repository with cache enabled
	const repository = new CompositeDomainRepository(dbManager, {
		enableRedisCache: true,
		redisCacheConfig: {
			keyPrefix: 'example:domain:',
			positiveTtl: 3600, // 1 hour for example
			negativeTtl: 1800, // 30 minutes for example
			enableWarmup: true,
			enableMetrics: true,
			maxWarmupDomains: 50,
			warmupBatchSize: 5,
		},
	});

	try
	{
		// Get initial metrics
		console.log('📊 Initial cache metrics:');
		const initialMetrics = repository.getMetrics();
		console.log(`  Cache hits: ${initialMetrics.cacheMetrics.totalHits}`);
		console.log(`  Cache misses: ${initialMetrics.cacheMetrics.totalMisses}`);
		console.log(`  Hit rate: ${(initialMetrics.cacheMetrics.hitRate * 100).toFixed(2)}%`);

		// Create warming sources
		const sources: CacheWarmingSource[] = [
			new ExampleDomainSource(),
			new PopularDomainsSource(),
		];

		console.log('\n🔥 Starting cache warmup...');
		const warmupStart = Date.now();

		// Warm up the cache
		await repository.warmupCache(sources);

		const warmupDuration = Date.now() - warmupStart;
		console.log(`✅ Cache warmup completed in ${warmupDuration}ms`);

		// Test cache performance after warmup
		console.log('\n🧪 Testing cache performance...');
		const testDomains = [
			'example.com', // Should be cached
			'google.com', // Should be cached
			'unknown-domain.com', // Should not be cached
		];

		const testStart = Date.now();
		const results = await repository.batchHasDomains(testDomains);
		const testDuration = Date.now() - testStart;

		console.log(`⚡ Batch check completed in ${testDuration}ms`);
		console.log('Results:');
		results.forEach((exists, domain) =>
		{
			console.log(`  ${domain}: ${exists ? '✅ exists' : '❌ not found'}`);
		});

		// Get final metrics
		console.log('\n📊 Final cache metrics:');
		const finalMetrics = repository.getMetrics();
		console.log(`  Cache hits: ${finalMetrics.cacheMetrics.totalHits}`);
		console.log(`  Cache misses: ${finalMetrics.cacheMetrics.totalMisses}`);
		console.log(`  Hit rate: ${(finalMetrics.cacheMetrics.hitRate * 100).toFixed(2)}%`);
		console.log(`  Average response time: ${finalMetrics.cacheMetrics.averageResponseTime.toFixed(2)}ms`);
		console.log(`  Estimated cache size: ${finalMetrics.cacheMetrics.estimatedSize} keys`);

		// Demonstrate cache invalidation
		console.log('\n🗑️  Demonstrating cache invalidation...');
		await repository.invalidateCache([
			{
				name: 'example-invalidation',
				pattern: 'example:domain:example*',
				deleteKeys: true,
			},
		]);

		console.log('✅ Cache invalidation completed');

		// Test performance after invalidation
		const postInvalidationResult = await repository.hasDomain('example.com');
		console.log(`example.com after invalidation: ${postInvalidationResult ? '✅ exists' : '❌ not found'}`);

		// Final metrics after invalidation
		const postInvalidationMetrics = repository.getMetrics();
		console.log('\n📊 Metrics after invalidation:');
		console.log(`  Total invalidations: ${postInvalidationMetrics.cacheMetrics.totalInvalidations}`);
	}
	catch (error)
	{
		console.error('❌ Cache warming example failed:', error);
	}
	finally
	{
		// Clean up
		repository.destroy();
		console.log('\n🧹 Cleanup completed');
	}
}

/**
 * Example function demonstrating cache monitoring
 */
export async function demonstrateCacheMonitoring(dbManager: DatabaseManager): Promise<void>
{
	console.log('📈 Starting Cache Monitoring Example');

	const repository = new CompositeDomainRepository(dbManager, {
		enableRedisCache: true,
		redisCacheConfig: {
			keyPrefix: 'monitor:domain:',
			enableMetrics: true,
			metricsInterval: 1000, // 1 second for demo
		},
	});

	try
	{
		// Simulate various cache operations
		const testDomains = [
			'monitor-test-1.com',
			'monitor-test-2.com',
			'monitor-test-3.com',
		];

		console.log('🔄 Performing cache operations...');

		// Perform multiple operations to generate metrics
		for (let i = 0; i < 5; i++)
		{
			await repository.batchHasDomains(testDomains);
			await new Promise(resolve => setTimeout(resolve, 200)); // Small delay
		}

		// Monitor metrics over time
		console.log('\n📊 Monitoring cache metrics:');
		for (let i = 0; i < 3; i++)
		{
			const metrics = repository.getMetrics();
			console.log(`\nSnapshot ${i + 1}:`);
			console.log(`  Total operations: ${metrics.cacheMetrics.totalGets}`);
			console.log(`  Hit rate: ${(metrics.cacheMetrics.hitRate * 100).toFixed(2)}%`);
			console.log(`  Average response time: ${metrics.cacheMetrics.averageResponseTime.toFixed(2)}ms`);
			console.log(`  Error count: ${metrics.cacheMetrics.errorCount}`);

			if (i < 2)
			{
				// Perform more operations
				await repository.batchHasDomains([`dynamic-${i}.com`]);
				await new Promise(resolve => setTimeout(resolve, 1000));
			}
		}

		console.log('\n✅ Cache monitoring completed');
	}
	catch (error)
	{
		console.error('❌ Cache monitoring example failed:', error);
	}
	finally
	{
		repository.destroy();
		console.log('🧹 Monitoring cleanup completed');
	}
}

// Export for use in other examples or tests
export { ExampleDomainSource, PopularDomainsSource };
