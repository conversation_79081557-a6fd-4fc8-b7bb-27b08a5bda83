import { Command } from 'commander';
import { logger, DatabaseManager } from '@shared';
import type { LogQuery } from '../logging';
import { ComprehensiveLoggingService } from '../logging';

type LogsOptions =
{
	component?: string;
	operation?: string;
	level?: string;
	since?: string;
	limit?: number;
	correlationId?: string;
	format?: 'json' | 'table';
	analyze?: boolean;
	metrics?: boolean;
	audit?: boolean;
	archive?: string;
	retention?: boolean;
};

async function logsCommand(options: LogsOptions): Promise<void>
{
	const serviceLogger = logger.getLogger('domain-seeder');
	const dbManager = new DatabaseManager();

	try
	{
		await dbManager.initialize();
		const redis = dbManager.getRedisClient();

		const loggingService = new ComprehensiveLoggingService(
			serviceLogger,
			redis,
			{ serviceName: 'domain-seeder' },
		);

		if (options.retention)
		{
			await handleRetentionCommand(loggingService);
			return;
		}

		if (options.archive)
		{
			await handleArchiveCommand(loggingService, options.archive);
			return;
		}

		if (options.metrics)
		{
			await handleMetricsCommand(loggingService, options);
			return;
		}

		if (options.analyze)
		{
			await handleAnalyzeCommand(loggingService, options);
			return;
		}

		if (options.audit)
		{
			await handleAuditCommand(loggingService, options);
			return;
		}

		if (options.correlationId)
		{
			await handleCorrelationTrail(loggingService, options.correlationId, options);
			return;
		}

		// Default: query logs
		await handleLogQuery(loggingService, options);
	}
	catch (error)
	{
		serviceLogger.error('Logs command failed', { error: error.message });
		process.exit(1);
	}
	finally
	{
		await dbManager.close();
	}
}

async function handleLogQuery(
	loggingService: ComprehensiveLoggingService,
	options: LogsOptions,
): Promise<void>
{
	const query: LogQuery = {
		limit: options.limit || 50,
	};

	if (options.component)
	{
		query.components = [options.component];
	}

	if (options.operation)
	{
		query.operations = [options.operation];
	}

	if (options.level)
	{
		query.logLevels = [options.level as any];
	}

	if (options.since)
	{
		query.timeRange = {
			start: new Date(options.since),
			end: new Date(),
		};
	}

	const logs = await loggingService.queryLogs(query);

	if (options.format === 'json')
	{
		console.log(JSON.stringify(logs, null, 2));
	}
	else
	{
		console.log('\n📋 Log Entries:');
		console.log('================');

		if (logs.length === 0)
		{
			console.log('No logs found matching the criteria.');
			return;
		}

		for (const log of logs)
		{
			const timestamp = log.timestamp.toISOString();
			const level = log.level.toUpperCase().padEnd(5);
			const component = log.component.padEnd(20);

			console.log(`${timestamp} [${level}] ${component} ${log.message}`);

			if (log.correlationId)
			{
				console.log(`  └─ Correlation ID: ${log.correlationId}`);
			}

			if (log.error)
			{
				console.log(`  └─ Error: ${log.error.name}: ${log.error.message}`);
			}

			if (Object.keys(log.metadata).length > 0)
			{
				console.log(`  └─ Metadata: ${JSON.stringify(log.metadata)}`);
			}

			console.log('');
		}

		console.log(`\nShowing ${logs.length} log entries`);
	}
}

async function handleAuditCommand(
	loggingService: ComprehensiveLoggingService,
	options: LogsOptions,
): Promise<void>
{
	const query: any = {
		limit: options.limit || 50,
	};

	if (options.component)
	{
		query.component = options.component;
	}

	if (options.operation)
	{
		query.operation = options.operation;
	}

	const auditEvents = await loggingService.queryAuditEvents(query);

	if (options.format === 'json')
	{
		console.log(JSON.stringify(auditEvents, null, 2));
	}
	else
	{
		console.log('\n🔍 Audit Events:');
		console.log('=================');

		if (auditEvents.length === 0)
		{
			console.log('No audit events found matching the criteria.');
			return;
		}

		for (const event of auditEvents)
		{
			const timestamp = event.timestamp.toISOString();
			const type = event.type.padEnd(20);
			const outcome = event.outcome.toUpperCase();

			console.log(`${timestamp} [${type}] ${event.component}.${event.operation} - ${outcome}`);

			if (event.correlationId)
			{
				console.log(`  └─ Correlation ID: ${event.correlationId}`);
			}

			if (event.duration)
			{
				console.log(`  └─ Duration: ${event.duration}ms`);
			}

			if (event.details && Object.keys(event.details).length > 0)
			{
				console.log(`  └─ Details: ${JSON.stringify(event.details, null, 2)}`);
			}

			console.log('');
		}

		console.log(`\nShowing ${auditEvents.length} audit events`);
	}
}

async function handleCorrelationTrail(
	loggingService: ComprehensiveLoggingService,
	correlationId: string,
	options: LogsOptions,
): Promise<void>
{
	const trail = await loggingService.getCorrelationTrail(correlationId);

	if (options.format === 'json')
	{
		console.log(JSON.stringify(trail, null, 2));
	}
	else
	{
		console.log(`\n🔗 Correlation Trail: ${correlationId}`);
		console.log('=====================================');

		if (trail.timeline.length === 0)
		{
			console.log('No events found for this correlation ID.');
			return;
		}

		for (const item of trail.timeline)
		{
			const timestamp = item.timestamp.toISOString();
			const type = item.type.toUpperCase().padEnd(5);

			if (item.type === 'log')
			{
				const log = item.event as any;
				const level = log.level.toUpperCase().padEnd(5);
				console.log(`${timestamp} [${type}] [${level}] ${log.component}: ${log.message}`);
			}
			else
			{
				const audit = item.event as any;
				const outcome = audit.outcome.toUpperCase().padEnd(7);
				console.log(`${timestamp} [${type}] [${outcome}] ${audit.component}.${audit.operation}`);

				if (audit.duration)
				{
					console.log(`  └─ Duration: ${audit.duration}ms`);
				}
			}
		}

		console.log(`\nTotal events: ${trail.timeline.length} (${trail.logs.length} logs, ${trail.auditEvents.length} audit events)`);
	}
}

async function handleMetricsCommand(
	loggingService: ComprehensiveLoggingService,
	options: LogsOptions,
): Promise<void>
{
	const endTime = new Date();
	const startTime = options.since
		? new Date(options.since)
		: new Date(endTime.getTime() - 24 * 60 * 60 * 1000); // Last 24 hours

	const components = options.component ? [options.component] : undefined;

	const metrics = await loggingService.generateMetrics(startTime, endTime, components);
	const performanceMetrics = loggingService.getPerformanceMetrics();

	if (options.format === 'json')
	{
		console.log(JSON.stringify({ metrics, performanceMetrics }, null, 2));
	}
	else
	{
		console.log('\n📊 System Metrics:');
		console.log('==================');
		console.log(`Time Range: ${startTime.toISOString()} to ${endTime.toISOString()}`);
		console.log(`Total Events: ${metrics.totalEvents}`);
		console.log(`Error Rate: ${(metrics.errorRate * 100).toFixed(2)}%`);
		console.log(`Average Processing Time: ${Math.round(metrics.averageProcessingTime)}ms`);

		console.log('\n📈 Events by Type:');
		for (const [type, count] of Object.entries(metrics.eventsByType))
		{
			console.log(`  ${type}: ${count}`);
		}

		console.log('\n🏗️ Events by Component:');
		for (const [component, count] of Object.entries(metrics.eventsByComponent))
		{
			console.log(`  ${component}: ${count}`);
		}

		console.log('\n🎯 Discovery Metrics:');
		console.log(`  Total Domains: ${metrics.discoveryMetrics.totalDomains}`);
		console.log(`  Unique Sources: ${metrics.discoveryMetrics.uniqueSources}`);
		console.log(`  Average Confidence: ${metrics.discoveryMetrics.averageConfidence.toFixed(2)}`);

		console.log('\n⚡ Performance Metrics:');
		console.log(`  Average Memory Usage: ${Math.round(metrics.performanceMetrics.averageMemoryUsageMB)}MB`);
		console.log(`  Peak Memory Usage: ${Math.round(metrics.performanceMetrics.peakMemoryUsageMB)}MB`);

		if (Object.keys(performanceMetrics).length > 0)
		{
			console.log('\n🚀 Operation Performance:');
			for (const [operation, stats] of Object.entries(performanceMetrics))
			{
				console.log(`  ${operation}:`);
				console.log(`    Count: ${stats.count}`);
				console.log(`    Average: ${stats.average}ms`);
				console.log(`    Min: ${stats.min}ms`);
				console.log(`    Max: ${stats.max}ms`);
				console.log(`    P95: ${stats.p95}ms`);
			}
		}
	}
}

async function handleAnalyzeCommand(
	loggingService: ComprehensiveLoggingService,
	options: LogsOptions,
): Promise<void>
{
	const endTime = new Date();
	const startTime = options.since
		? new Date(options.since)
		: new Date(endTime.getTime() - 24 * 60 * 60 * 1000); // Last 24 hours

	const components = options.component ? [options.component] : undefined;

	const analysis = await loggingService.analyzeLogs(startTime, endTime, components);

	if (options.format === 'json')
	{
		console.log(JSON.stringify(analysis, null, 2));
	}
	else
	{
		console.log('\n🔍 Log Analysis:');
		console.log('================');

		console.log('\n📋 Summary:');
		console.log(`  Total Logs: ${analysis.summary.totalLogs}`);
		console.log(`  Time Span: ${Math.round(analysis.summary.timeSpan / (60 * 60 * 1000))} hours`);
		console.log(`  Error Count: ${analysis.summary.errorCount}`);
		console.log(`  Warning Count: ${analysis.summary.warningCount}`);
		console.log(`  Unique Correlation IDs: ${analysis.summary.uniqueCorrelationIds}`);
		console.log(`  Unique Components: ${analysis.summary.uniqueComponents}`);

		if (analysis.anomalies.length > 0)
		{
			console.log('\n⚠️ Anomalies Detected:');
			for (const anomaly of analysis.anomalies)
			{
				const severity = anomaly.severity.toUpperCase();
				console.log(`  [${severity}] ${anomaly.type}: ${anomaly.description}`);
				console.log(`    Timestamp: ${anomaly.timestamp.toISOString()}`);
				console.log(`    Affected Components: ${anomaly.affectedComponents.join(', ')}`);
				console.log('    Suggested Actions:');
				for (const action of anomaly.suggestedActions)
				{
					console.log(`      - ${action}`);
				}
				console.log('');
			}
		}

		if (analysis.insights.length > 0)
		{
			console.log('\n💡 Insights:');
			for (const insight of analysis.insights)
			{
				const confidence = Math.round(insight.confidence * 100);
				console.log(`  [${insight.category.toUpperCase()}] ${insight.insight} (${confidence}% confidence)`);
			}
		}

		console.log('\n📈 Top Components by Activity:');
		for (const activity of analysis.trends.componentActivity.slice(0, 5))
		{
			console.log(`  ${activity.component}: ${activity.count} events`);
		}

		console.log('\n🔄 Top Operations by Frequency:');
		for (const operation of analysis.trends.operationFrequency.slice(0, 5))
		{
			console.log(`  ${operation.operation}: ${operation.count} times`);
		}
	}
}

async function handleRetentionCommand(
	loggingService: ComprehensiveLoggingService,
): Promise<void>
{
	console.log('\n🗄️ Applying Retention Policies...');

	const stats = await loggingService.applyRetentionPolicies();

	console.log('\n📊 Retention Statistics:');
	console.log('========================');
	console.log(`Total Entries: ${stats.totalEntries}`);
	console.log(`Archived Entries: ${stats.archivedEntries}`);
	console.log(`Deleted Entries: ${stats.deletedEntries}`);
	console.log(`Active Entries: ${stats.activeEntries}`);
	console.log(`Archives Size: ${Math.round(stats.archivesSizeBytes / 1024 / 1024)}MB`);
	console.log(`Oldest Entry: ${stats.oldestEntry.toISOString()}`);
	console.log(`Newest Entry: ${stats.newestEntry.toISOString()}`);

	console.log('\n📋 Policy Results:');
	for (const policy of stats.retentionPolicies)
	{
		console.log(`  ${policy.name}:`);
		console.log(`    Applied to: ${policy.appliedEntries} entries`);
		console.log(`    Archived: ${policy.archivedEntries} entries`);
		console.log(`    Deleted: ${policy.deletedEntries} entries`);
	}
}

async function handleArchiveCommand(
	loggingService: ComprehensiveLoggingService,
	action: string,
): Promise<void>
{
	if (action === 'list')
	{
		const archives = await loggingService.listArchives();

		console.log('\n📦 Available Archives:');
		console.log('======================');

		if (archives.length === 0)
		{
			console.log('No archives found.');
			return;
		}

		for (const archive of archives)
		{
			console.log(`Archive ID: ${archive.id}`);
			console.log(`  Created: ${archive.createdAt.toISOString()}`);
			console.log(`  Time Range: ${archive.timeRange.start.toISOString()} to ${archive.timeRange.end.toISOString()}`);
			console.log(`  Policy: ${archive.policy}`);
			console.log(`  Entries: ${archive.entryCount}`);
			console.log(`  Size: ${Math.round(archive.fileSizeBytes / 1024)}KB`);
			console.log(`  Compressed: ${archive.compressed ? 'Yes' : 'No'}`);
			console.log(`  Checksum: ${archive.checksumMD5}`);
			console.log('');
		}
	}
	else
	{
		console.log(`Unknown archive action: ${action}`);
		console.log('Available actions: list');
	}
}

// Create the command
const logsCmd = new Command('logs')
	.description('Query and analyze domain-seeder logs')
	.option('-c, --component <component>', 'Filter by component')
	.option('-o, --operation <operation>', 'Filter by operation')
	.option('-l, --level <level>', 'Filter by log level (debug, info, warn, error)')
	.option('-s, --since <date>', 'Show logs since date (ISO format)')
	.option('-n, --limit <number>', 'Limit number of results', '50')
	.option('--correlation-id <id>', 'Show correlation trail for specific ID')
	.option('-f, --format <format>', 'Output format (json, table)', 'table')
	.option('--analyze', 'Analyze logs for patterns and anomalies')
	.option('--metrics', 'Show system metrics')
	.option('--audit', 'Show audit events instead of logs')
	.option('--archive <action>', 'Archive operations (list)')
	.option('--retention', 'Apply retention policies')
	.action(logsCommand);

export default logsCmd;
