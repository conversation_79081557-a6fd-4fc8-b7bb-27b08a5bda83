import type { Logger } from '@shared';
import * as fs from 'fs';
import type { SeederConfiguration } from './SeederConfig';
import { SeederConfigurationSchema } from './SeederConfig';
// import * as path from 'path';

type Environment = 'development' | 'staging' | 'production' | 'test';

interface ConfigurationProfile
{
	name: string;
	environment: Environment;
	description: string;
	baseConfig: Partial<SeederConfiguration>;
	overrides: Partial<SeederConfiguration>;
	validation: {
		required: string[];
		warnings: string[];
	};
}

interface ProfileValidationResult
{
	isValid: boolean;
	errors: string[];
	warnings: string[];
	missingRequired: string[];
}

interface ConfigurationMigration
{
	fromVersion: string;
	toVersion: string;
	description: string;
	migrate: (config: any) => any;
}

class ConfigurationProfiles
{
	private profiles: Map<string, ConfigurationProfile> = new Map();

	private migrations: ConfigurationMigration[] = [];

	private logger?: Logger;

	private readonly CONFIG_VERSION = '1.0.0';

	constructor()
	{
		this.initializeDefaultProfiles();
		this.initializeMigrations();
	}

	setLogger(logger: Logger): void
	{
		this.logger = logger;
	}

	private initializeDefaultProfiles(): void
	{
		// Development Profile
		this.profiles.set('development', {
			name: 'development',
			environment: 'development',
			description: 'Development environment with relaxed security and verbose logging',
			baseConfig: {
				environment: {
					nodeEnv: 'development',
					serviceName: 'domain-seeder-dev',
					serviceVersion: '1.0.0-dev',
					port: 3000,
					timezone: 'UTC',
					gracefulShutdownTimeoutMs: 10000,
				},
				seeder: {
					maxNewPerDay: 10000, // Reduced for development
					enqueueBatch: 100,
					enqueueIntervalMs: 5000, // Slower for development
					newQueueMaxDepth: 10000,
					dbCheckBatch: 1000,
					bloomFpRate: 0.01,
					pslUpdateIntervalDays: 1, // More frequent updates
					discoveryTimeoutMs: 60000, // Shorter timeout
					maxConcurrentSources: 2,
					rateLimitWindowMs: 60000,
					rateLimitMaxRequests: 100,
					contentGenerationEnabled: true,
					contentGenerationMode: 'preGenerated',
				},
				monitoring: {
					metricsEnabled: true,
					metricsPort: 9090,
					healthCheckEnabled: true,
					healthCheckPort: 8080,
					alertingEnabled: false, // Disabled for development
					logLevel: 'debug',
					logFormat: 'text',
					logDir: './logs',
					maxLogFiles: 5,
					maxLogSize: '50MB',
				},
				security: {
					credentialRotationIntervalDays: 365, // Relaxed for development
					credentialExpiryWarningDays: 30,
					secretsPath: './secrets',
					tlsEnabled: false,
				},
			},
			overrides: {},
			validation: {
				required: [
					'database.scylla.contactPoints',
					'database.maria.host',
					'database.manticore.host',
					'database.redis.host',
				],
				warnings: [
					'TLS is disabled in development mode',
					'Alerting is disabled in development mode',
				],
			},
		});

		// Staging Profile
		this.profiles.set('staging', {
			name: 'staging',
			environment: 'staging',
			description: 'Staging environment with production-like settings but reduced scale',
			baseConfig: {
				environment: {
					nodeEnv: 'staging',
					serviceName: 'domain-seeder-staging',
					serviceVersion: '1.0.0-staging',
					port: 3000,
					timezone: 'UTC',
					gracefulShutdownTimeoutMs: 30000,
				},
				seeder: {
					maxNewPerDay: 100000, // Reduced scale for staging
					enqueueBatch: 500,
					enqueueIntervalMs: 2000,
					newQueueMaxDepth: 50000,
					dbCheckBatch: 2500,
					bloomFpRate: 0.01,
					pslUpdateIntervalDays: 7,
					discoveryTimeoutMs: 180000,
					maxConcurrentSources: 2,
					rateLimitWindowMs: 60000,
					rateLimitMaxRequests: 500,
					contentGenerationEnabled: true,
					contentGenerationMode: 'both',
				},
				monitoring: {
					metricsEnabled: true,
					metricsPort: 9090,
					healthCheckEnabled: true,
					healthCheckPort: 8080,
					alertingEnabled: true,
					logLevel: 'info',
					logFormat: 'json',
					logDir: './logs',
					maxLogFiles: 7,
					maxLogSize: '100MB',
				},
				security: {
					credentialRotationIntervalDays: 90,
					credentialExpiryWarningDays: 7,
					secretsPath: './secrets',
					tlsEnabled: true,
				},
			},
			overrides: {},
			validation: {
				required: [
					'database.scylla.contactPoints',
					'database.maria.host',
					'database.manticore.host',
					'database.redis.host',
					'security.encryptionKey',
					'security.tlsCertPath',
					'security.tlsKeyPath',
				],
				warnings: [
					'Staging environment should use production-like credentials',
				],
			},
		});

		// Production Profile
		this.profiles.set('production', {
			name: 'production',
			environment: 'production',
			description: 'Production environment with full scale and security',
			baseConfig: {
				environment: {
					nodeEnv: 'production',
					serviceName: 'domain-seeder',
					serviceVersion: '1.0.0',
					port: 3000,
					timezone: 'UTC',
					gracefulShutdownTimeoutMs: 30000,
				},
				seeder: {
					maxNewPerDay: 500000, // Full scale
					enqueueBatch: 1000,
					enqueueIntervalMs: 1000,
					newQueueMaxDepth: 200000,
					dbCheckBatch: 5000,
					bloomFpRate: 0.01,
					pslUpdateIntervalDays: 7,
					discoveryTimeoutMs: 300000,
					maxConcurrentSources: 3,
					rateLimitWindowMs: 60000,
					rateLimitMaxRequests: 1000,
					contentGenerationEnabled: true,
					contentGenerationMode: 'both',
				},
				monitoring: {
					metricsEnabled: true,
					metricsPort: 9090,
					healthCheckEnabled: true,
					healthCheckPort: 8080,
					alertingEnabled: true,
					logLevel: 'info',
					logFormat: 'json',
					logDir: './logs',
					maxLogFiles: 10,
					maxLogSize: '100MB',
				},
				security: {
					credentialRotationIntervalDays: 90,
					credentialExpiryWarningDays: 7,
					secretsPath: './secrets',
					tlsEnabled: true,
				},
			},
			overrides: {},
			validation: {
				required: [
					'database.scylla.contactPoints',
					'database.maria.host',
					'database.manticore.host',
					'database.redis.host',
					'security.encryptionKey',
					'security.tlsCertPath',
					'security.tlsKeyPath',
					'sources.radar.apiKey',
					'sources.umbrella.apiKey',
					'sources.czds.username',
					'sources.czds.password',
				],
				warnings: [],
			},
		});

		// Test Profile
		this.profiles.set('test', {
			name: 'test',
			environment: 'test',
			description: 'Test environment with minimal resources and fast execution',
			baseConfig: {
				environment: {
					nodeEnv: 'test',
					serviceName: 'domain-seeder-test',
					serviceVersion: '1.0.0-test',
					port: 3001,
					timezone: 'UTC',
					gracefulShutdownTimeoutMs: 5000,
				},
				seeder: {
					maxNewPerDay: 1000, // Very limited for tests
					enqueueBatch: 10,
					enqueueIntervalMs: 100, // Fast for tests
					newQueueMaxDepth: 100,
					dbCheckBatch: 50,
					bloomFpRate: 0.1, // Higher false positive rate for speed
					pslUpdateIntervalDays: 365, // Rarely update in tests
					discoveryTimeoutMs: 10000, // Short timeout
					maxConcurrentSources: 1,
					rateLimitWindowMs: 10000,
					rateLimitMaxRequests: 50,
					contentGenerationEnabled: false, // Disabled for speed
					contentGenerationMode: 'preGenerated',
				},
				monitoring: {
					metricsEnabled: false, // Disabled for tests
					metricsPort: 9091,
					healthCheckEnabled: true,
					healthCheckPort: 8081,
					alertingEnabled: false,
					logLevel: 'error', // Minimal logging
					logFormat: 'text',
					logDir: './test-logs',
					maxLogFiles: 2,
					maxLogSize: '10MB',
				},
				security: {
					credentialRotationIntervalDays: 365,
					credentialExpiryWarningDays: 30,
					secretsPath: './test-secrets',
					tlsEnabled: false,
				},
			},
			overrides: {},
			validation: {
				required: [
					'database.scylla.contactPoints',
					'database.maria.host',
					'database.manticore.host',
					'database.redis.host',
				],
				warnings: [
					'Test environment has reduced functionality',
					'Content generation is disabled in test mode',
				],
			},
		});
	}

	private initializeMigrations(): void
	{
		// Example migration from version 0.9.0 to 1.0.0
		this.migrations.push({
			fromVersion: '0.9.0',
			toVersion: '1.0.0',
			description: 'Add AI configuration and security settings',
			migrate: (config: any) =>
			{
				// Add AI configuration if missing
				if (!config.ai)
				{
					config.ai = {
						providers: {
							openai: { enabled: false, apiKeys: [], priority: 1 },
							claude: { enabled: false, apiKeys: [], priority: 2 },
							gemini: { enabled: false, apiKeys: [], priority: 3 },
							openrouter: { enabled: false, apiKeys: [], priority: 4 },
						},
						proxy: { enabled: false, ips: [] },
						fallbackEnabled: true,
						maxRetries: 3,
						timeoutMs: 30000,
					};
				}

				// Add security configuration if missing
				if (!config.security)
				{
					config.security = {
						credentialRotationIntervalDays: 90,
						credentialExpiryWarningDays: 7,
						secretsPath: './secrets',
						tlsEnabled: false,
					};
				}

				return config;
			},
		});
	}

	getProfile(name: string): ConfigurationProfile | undefined
	{
		return this.profiles.get(name);
	}

	listProfiles(): ConfigurationProfile[]
	{
		return Array.from(this.profiles.values());
	}

	getProfilesForEnvironment(environment: Environment): ConfigurationProfile[]
	{
		return Array.from(this.profiles.values()).filter(p => p.environment === environment);
	}

	createProfile(profile: ConfigurationProfile): void
	{
		this.profiles.set(profile.name, profile);

		if (this.logger)
		{
			this.logger.info('Configuration profile created', {
				name: profile.name,
				environment: profile.environment,
			});
		}
	}

	updateProfile(name: string, updates: Partial<ConfigurationProfile>): void
	{
		const existingProfile = this.profiles.get(name);
		if (!existingProfile)
		{
			throw new Error(`Profile ${name} not found`);
		}

		const updatedProfile = { ...existingProfile, ...updates };
		this.profiles.set(name, updatedProfile);

		if (this.logger)
		{
			this.logger.info('Configuration profile updated', { name });
		}
	}

	deleteProfile(name: string): boolean
	{
		const deleted = this.profiles.delete(name);

		if (deleted && this.logger)
		{
			this.logger.info('Configuration profile deleted', { name });
		}

		return deleted;
	}

	applyProfile(profileName: string, baseConfig: Partial<SeederConfiguration>): SeederConfiguration
	{
		const profile = this.profiles.get(profileName);
		if (!profile)
		{
			throw new Error(`Profile ${profileName} not found`);
		}

		// Deep merge: baseConfig -> profile.baseConfig -> profile.overrides
		const mergedConfig = this.deepMerge(
			baseConfig,
			profile.baseConfig,
			profile.overrides,
		);

		// Validate the merged configuration
		const validationResult = this.validateProfileConfiguration(profile, mergedConfig);
		if (!validationResult.isValid)
		{
			throw new Error(
				`Profile ${profileName} validation failed: ${validationResult.errors.join(', ')}`,
			);
		}

		if (validationResult.warnings.length > 0 && this.logger)
		{
			this.logger.warn('Profile configuration warnings', {
				profile: profileName,
				warnings: validationResult.warnings,
			});
		}

		// Parse with schema validation
		try
		{
			return SeederConfigurationSchema.parse(mergedConfig);
		}
		catch (error)
		{
			throw new Error(`Profile ${profileName} schema validation failed: ${error.message}`);
		}
	}

	private deepMerge(...objects: any[]): any
	{
		const result = {};

		for (const obj of objects)
		{
			if (!obj) continue;

			for (const [key, value] of Object.entries(obj))
			{
				if (value && typeof value === 'object' && !Array.isArray(value))
				{
					result[key] = this.deepMerge(result[key] || {}, value);
				}
				else
				{
					result[key] = value;
				}
			}
		}

		return result;
	}

	private validateProfileConfiguration(
		profile: ConfigurationProfile,
		config: any,
	): ProfileValidationResult
	{
		const errors: string[] = [];
		const warnings: string[] = [...profile.validation.warnings];
		const missingRequired: string[] = [];

		// Check required fields
		for (const requiredPath of profile.validation.required)
		{
			if (!this.hasNestedProperty(config, requiredPath))
			{
				missingRequired.push(requiredPath);
				errors.push(`Required configuration missing: ${requiredPath}`);
			}
		}

		// Environment-specific validation
		switch (profile.environment)
		{
			case 'production':
				this.validateProductionConfig(config, errors, warnings);
				break;
			case 'staging':
				this.validateStagingConfig(config, errors, warnings);
				break;
			case 'development':
				this.validateDevelopmentConfig(config, errors, warnings);
				break;
			case 'test':
				this.validateTestConfig(config, errors, warnings);
				break;
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			missingRequired,
		};
	}

	private hasNestedProperty(obj: any, path: string): boolean
	{
		const keys = path.split('.');
		let current = obj;

		for (const key of keys)
		{
			if (!current || typeof current !== 'object' || !(key in current))
			{
				return false;
			}
			current = current[key];
		}

		return current !== undefined && current !== null && current !== '';
	}

	private validateProductionConfig(config: any, errors: string[], warnings: string[]): void
	{
		// Security checks
		if (!config.security?.encryptionKey)
		{
			errors.push('Production environment requires encryption key');
		}

		if (!config.security?.tlsEnabled)
		{
			errors.push('Production environment requires TLS to be enabled');
		}

		// Performance checks
		if (config.seeder?.maxNewPerDay < 100000)
		{
			warnings.push('Production maxNewPerDay seems low for production workload');
		}

		// Monitoring checks
		if (!config.monitoring?.alertingEnabled)
		{
			warnings.push('Alerting should be enabled in production');
		}

		if (config.monitoring?.logLevel === 'debug')
		{
			warnings.push('Debug logging may impact production performance');
		}

		// Source checks
		const enabledSources = Object.entries(config.sources || {})
			.filter(([, sourceConfig]: [string, any]) => sourceConfig?.enabled)
			.length;

		if (enabledSources < 3)
		{
			warnings.push('Production should have multiple data sources enabled');
		}
	}

	private validateStagingConfig(config: any, errors: string[], warnings: string[]): void
	{
		// Should be similar to production but with reduced scale
		if (config.seeder?.maxNewPerDay >= 500000)
		{
			warnings.push('Staging maxNewPerDay should be less than production');
		}

		if (!config.security?.tlsEnabled)
		{
			warnings.push('Staging should use TLS for production-like testing');
		}
	}

	private validateDevelopmentConfig(config: any, errors: string[], warnings: string[]): void
	{
		// Development-specific warnings
		if (config.seeder?.maxNewPerDay > 50000)
		{
			warnings.push('Development maxNewPerDay seems high for local development');
		}

		if (config.monitoring?.logLevel !== 'debug')
		{
			warnings.push('Development should use debug logging for troubleshooting');
		}
	}

	private validateTestConfig(config: any, errors: string[], warnings: string[]): void
	{
		// Test environment should be minimal
		if (config.seeder?.maxNewPerDay > 10000)
		{
			warnings.push('Test maxNewPerDay should be minimal for fast test execution');
		}

		if (config.monitoring?.metricsEnabled)
		{
			warnings.push('Metrics collection may slow down tests');
		}
	}

	// Configuration migration utilities
	migrateConfiguration(config: any, fromVersion: string, toVersion: string): any
	{
		let currentConfig = { ...config };
		let currentVersion = fromVersion;

		// Find and apply migrations in sequence
		while (currentVersion !== toVersion)
		{
			const migration = this.migrations.find(m => m.fromVersion === currentVersion);

			if (!migration)
			{
				throw new Error(`No migration path found from ${currentVersion} to ${toVersion}`);
			}

			if (this.logger)
			{
				this.logger.info('Applying configuration migration', {
					from: migration.fromVersion,
					to: migration.toVersion,
					description: migration.description,
				});
			}

			currentConfig = migration.migrate(currentConfig);
			currentVersion = migration.toVersion;
		}

		return currentConfig;
	}

	getAvailableMigrations(): ConfigurationMigration[]
	{
		return [...this.migrations];
	}

	// Profile import/export
	exportProfile(name: string): string
	{
		const profile = this.profiles.get(name);
		if (!profile)
		{
			throw new Error(`Profile ${name} not found`);
		}

		return JSON.stringify(profile, null, 2);
	}

	importProfile(profileJson: string): void
	{
		try
		{
			const profile: ConfigurationProfile = JSON.parse(profileJson);

			// Validate profile structure
			if (!profile.name || !profile.environment || !profile.baseConfig)
			{
				throw new Error('Invalid profile structure');
			}

			this.profiles.set(profile.name, profile);

			if (this.logger)
			{
				this.logger.info('Configuration profile imported', {
					name: profile.name,
					environment: profile.environment,
				});
			}
		}
		catch (error)
		{
			throw new Error(`Failed to import profile: ${error.message}`);
		}
	}

	// File-based profile management
	saveProfileToFile(name: string, filePath: string): void
	{
		const profile = this.profiles.get(name);
		if (!profile)
		{
			throw new Error(`Profile ${name} not found`);
		}

		const profileData = {
			version: this.CONFIG_VERSION,
			profile,
			exportedAt: new Date().toISOString(),
		};

		fs.writeFileSync(filePath, JSON.stringify(profileData, null, 2));

		if (this.logger)
		{
			this.logger.info('Profile saved to file', { name, filePath });
		}
	}

	loadProfileFromFile(filePath: string): void
	{
		if (!fs.existsSync(filePath))
		{
			throw new Error(`Profile file not found: ${filePath}`);
		}

		try
		{
			const fileContent = fs.readFileSync(filePath, 'utf8');
			const profileData = JSON.parse(fileContent);

			// Handle version migration if needed
			let profile = profileData.profile;
			if (profileData.version !== this.CONFIG_VERSION)
			{
				profile = this.migrateConfiguration(
					profile,
					profileData.version,
					this.CONFIG_VERSION,
				);
			}

			this.profiles.set(profile.name, profile);

			if (this.logger)
			{
				this.logger.info('Profile loaded from file', {
					name: profile.name,
					filePath,
					version: profileData.version,
				});
			}
		}
		catch (error)
		{
			throw new Error(`Failed to load profile from file: ${error.message}`);
		}
	}
}

export type {
	Environment,
	ConfigurationProfile,
	ProfileValidationResult,
	ConfigurationMigration,
};

export { ConfigurationProfiles };
