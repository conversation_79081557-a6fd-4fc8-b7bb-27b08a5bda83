import axios, { type AxiosResponse } from 'axios';
import { createReadStream, createWriteStream } from 'node:fs';
import { pipeline } from 'node:stream/promises';
import { createGunzip } from 'zlib';
import { logger } from '@shared';
import type { DiscoveryStrategy } from '../interfaces/DiscoveryEngine';
import type {
	DomainCandidate,
	FetchOptions,
	SourceConnector,
} from '../interfaces/SourceConnector';

type PIRConfigType =
{
	baseUrl: string;
	apiKey: string;
	maxRetries: number;
	retryDelayMs: number;
	requestTimeoutMs: number;
	rateLimitDelayMs: number;
	tempDir: string;
	registrationCutoffHours: number;
	pageSize: number;
};

type PIRDomainInfoType =
{
	domain: string;
	registrationDate: string;
	expirationDate: string;
	registrar: string;
	status: string[];
	nameservers: string[];
	contacts: {
		registrant?: PIRContact;
		admin?: PIRContact;
		tech?: PIRContact;
	};
};

type PIRContactType =
{
	name: string;
	organization?: string;
	email: string;
	country: string;
};

type PIRZoneFileInfoType =
{
	filename: string;
	url: string;
	lastModified: Date;
	size: number;
	checksum: string;
};

type PIRRegistrationEventType =
{
	domain: string;
	eventType: 'create' | 'update' | 'delete' | 'transfer';
	timestamp: string;
	registrar: string;
};

class PIRConnector implements SourceConnector
{
	public readonly name = 'pir';

	public readonly priority = 5;

	public readonly cadence = 'daily' as const;

	private readonly config: PIRConfigType;

	private readonly logger: ReturnType<typeof logger.getLogger>;

	private lastFetchTime: Date | null = null;

	private authToken: string | null = null;

	private tokenExpiry: Date | null = null;

	constructor(config?: Partial<PIRConfigType>)
	{
		this.config = {
			baseUrl: 'https://api.pir.org',
			apiKey: process.env.PIR_API_KEY || '',
			maxRetries: 3,
			retryDelayMs: 1000,
			requestTimeoutMs: 60000,
			rateLimitDelayMs: 200, // Conservative rate limiting for registry API
			tempDir: '/tmp',
			registrationCutoffHours: 48, // Only include domains registered in last 48 hours
			pageSize: 1000,
			...config,
		};

		this.logger = logger.getLogger('PIRConnector');

		if (!this.config.apiKey)
		{
			throw new Error('PIR API key not provided. Set PIR_API_KEY environment variable.');
		}
	}

	supportsStrategy(strategy: DiscoveryStrategy): boolean
	{
		// PIR supports zone-new strategy for newly registered .org domains
		return strategy === 'zone-new';
	}

	async* fetchDomains(options: FetchOptions): AsyncIterable<DomainCandidate>
	{
		this.logger.info('Starting PIR .org domain fetch', { options });

		try
		{
			// Ensure we have a valid authentication token
			await this.ensureAuthenticated();

			// Process domains based on strategy
			const strategy = options.strategy || 'zone-new';

			if (strategy === 'zone-new')
			{
				yield* this.fetchNewlyRegisteredDomains(options);
			}
			else
			{
				this.logger.warn('Unsupported strategy for PIR connector', { strategy });
				return;
			}

			this.lastFetchTime = new Date();
			this.logger.info('PIR domain fetch completed successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to fetch PIR domains', { error: (error as Error).message });
			throw error;
		}
	}

	async getLastUpdate(): Promise<Date | null>
	{
		return this.lastFetchTime;
	}

	async healthCheck(): Promise<boolean>
	{
		try
		{
			await this.ensureAuthenticated();

			// Test API connectivity with a simple status endpoint
			const response = await this.makeRequest('/v1/status');
			return response.status === 200;
		}
		catch (error)
		{
			this.logger.error('PIR health check failed', { error: (error as Error).message });
			return false;
		}
	}

	private async ensureAuthenticated(): Promise<void>
	{
		// Check if we have a valid token
		if (this.authToken && this.tokenExpiry && this.tokenExpiry > new Date())
		{
			return;
		}

		this.logger.info('Authenticating with PIR API');

		const response = await this.makeRequest('/v1/auth/token', {
			method: 'POST',
			data: {
				apiKey: this.config.apiKey,
				scope: 'registry:read',
			},
		});

		this.authToken = response.data.accessToken;

		// PIR tokens typically expire in 24 hours
		this.tokenExpiry = new Date(Date.now() + 23 * 60 * 60 * 1000); // 23 hours to be safe

		this.logger.info('Successfully authenticated with PIR API');
	}

	private async* fetchNewlyRegisteredDomains(options: FetchOptions): AsyncIterable<DomainCandidate>
	{
		const cutoffDate = new Date(Date.now() - this.config.registrationCutoffHours * 60 * 60 * 1000);
		const limit = options.limit;
		const since = options.since || cutoffDate;

		let totalYielded = 0;
		let offset = options.offset || 0;

		this.logger.info('Fetching newly registered .org domains', {
			since: since.toISOString(),
			cutoffHours: this.config.registrationCutoffHours,
		});

		while (true)
		{
			if (limit && totalYielded >= limit)
			{
				break;
			}

			try
			{
				const remainingLimit = limit ? Math.min(this.config.pageSize, limit - totalYielded) : this.config.pageSize;

				const response = await this.makeRequest('/v1/domains/registrations', {
					method: 'GET',
					headers: {
						Authorization: `Bearer ${this.authToken}`,
					},
					params: {
						since: since.toISOString(),
						limit: remainingLimit,
						offset,
						tld: 'org',
						eventType: 'create',
					},
				});

				const registrations = response.data.registrations as PIRRegistrationEventType[];

				if (!registrations || registrations.length === 0)
				{
					this.logger.info('No more registrations found', { offset, totalYielded });
					break;
				}

				this.logger.debug('Processing registration batch', {
					count: registrations.length,
					offset,
					totalYielded,
				});

				for (const registration of registrations)
				{
					if (limit && totalYielded >= limit)
					{
						break;
					}

					// Validate registration is within our cutoff window
					const registrationDate = new Date(registration.timestamp);
					if (registrationDate < since)
					{
						continue;
					}

					const candidate: DomainCandidate = {
						domain: registration.domain.toLowerCase(),
						source: this.name,
						metadata: {
							tld: 'org',
							registrationDate: registration.timestamp,
							registrar: registration.registrar,
							eventType: registration.eventType,
							strategy: 'zone-new',
							fetchDate: new Date().toISOString(),
						},
					};

					yield candidate;
					totalYielded += 1;

					// Add rate limiting delay
					if (this.config.rateLimitDelayMs > 0)
					{
						await this.sleep(this.config.rateLimitDelayMs);
					}
				}

				// Check if we've reached the end of results
				if (registrations.length < this.config.pageSize)
				{
					this.logger.info('Reached end of registration results', { totalYielded });
					break;
				}

				offset += registrations.length;
			}
			catch (error)
			{
				this.logger.error('Failed to fetch registration batch', {
					offset,
					error: (error as Error).message,
				});
				throw error;
			}
		}

		this.logger.info('Newly registered domains fetch completed', { totalYielded });
	}

	private async fetchDomainDetails(domain: string): Promise<PIRDomainInfoType | null>
	{
		try
		{
			const response = await this.makeRequest(`/v1/domains/${domain}`, {
				method: 'GET',
				headers: {
					Authorization: `Bearer ${this.authToken}`,
				},
			});

			return response.data as PIRDomainInfoType;
		}
		catch (error)
		{
			this.logger.warn('Failed to fetch domain details', {
				domain,
				error: (error as Error).message,
			});
			return null;
		}
	}

	private async getZoneFileInfo(): Promise<PIRZoneFileInfo | null>
	{
		try
		{
			const response = await this.makeRequest('/v1/zones/org/info', {
				method: 'GET',
				headers: {
					Authorization: `Bearer ${this.authToken}`,
				},
			});

			return response.data as PIRZoneFileInfo;
		}
		catch (error)
		{
			this.logger.error('Failed to get zone file info', { error: (error as Error).message });
			return null;
		}
	}

	private async downloadZoneFile(zoneInfo: PIRZoneFileInfo): Promise<string>
	{
		const fileName = 'pir-org-zone.txt.gz';
		const filePath = `${this.config.tempDir}/${fileName}`;

		this.logger.info('Downloading PIR .org zone file', {
			url: zoneInfo.url,
			size: zoneInfo.size,
			checksum: zoneInfo.checksum,
		});

		const response = await this.makeRequest(zoneInfo.url, {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${this.authToken}`,
			},
			responseType: 'stream',
		});

		// Save compressed file to disk
		await pipeline(response.data, createWriteStream(filePath));

		// Verify checksum if provided
		if (zoneInfo.checksum)
		{
			const actualChecksum = await this.calculateFileChecksum(filePath);
			if (actualChecksum !== zoneInfo.checksum)
			{
				throw new Error(`Zone file checksum mismatch. Expected: ${zoneInfo.checksum}, Actual: ${actualChecksum}`);
			}
		}

		this.logger.info('Zone file downloaded and verified successfully', { filePath });

		return filePath;
	}

	private async calculateFileChecksum(filePath: string): Promise<string>
	{
		const crypto = await import('crypto');
		const hash = crypto.createHash('sha256');
		const stream = createReadStream(filePath);

		return new Promise((resolve, reject) =>
		{
			stream.on('data', data => hash.update(data));
			stream.on('end', () => resolve(hash.digest('hex')));
			stream.on('error', reject);
		});
	}

	private async* parseZoneFile(
		filePath: string,
		cutoffDate: Date,
		limit?: number,
	): AsyncIterable<DomainCandidate>
	{
		let totalYielded = 0;

		this.logger.info('Parsing PIR .org zone file', {
			filePath,
			cutoffDate: cutoffDate.toISOString(),
		});

		const readStream = createReadStream(filePath);
		const gunzip = createGunzip();

		let lineBuffer = '';
		let processedLines = 0;

		readStream.pipe(gunzip);

		for await (const chunk of gunzip)
		{
			lineBuffer += chunk.toString();
			const lines = lineBuffer.split('\n');

			// Keep the last incomplete line in the buffer
			lineBuffer = lines.pop() || '';

			for (const line of lines)
			{
				processedLines += 1;

				if (limit && totalYielded >= limit)
				{
					return;
				}

				const candidate = this.parseZoneLine(line, cutoffDate);
				if (candidate)
				{
					yield candidate;
					totalYielded += 1;
				}

				// Log progress every 100k lines
				if (processedLines % 100000 === 0)
				{
					this.logger.debug('Zone file parsing progress', {
						processedLines,
						totalYielded,
					});
				}
			}
		}

		// Process any remaining line in buffer
		if (lineBuffer.trim())
		{
			const candidate = this.parseZoneLine(lineBuffer, cutoffDate);
			if (candidate && (!limit || totalYielded < limit))
			{
				yield candidate;
				totalYielded += 1;
			}
		}

		this.logger.info('Zone file parsing completed', {
			processedLines,
			totalYielded,
		});
	}

	private parseZoneLine(line: string, cutoffDate: Date): DomainCandidate | null
	{
		try
		{
			// Skip comments and empty lines
			if (!line.trim() || line.startsWith(';'))
			{
				return null;
			}

			// Basic zone file record parsing
			// Format: <domain> [TTL] [class] <type> <value>
			const parts = line.trim().split(/\s+/);

			if (parts.length < 3)
			{
				return null;
			}

			let domain = parts[0];

			// Remove trailing dot if present
			if (domain.endsWith('.'))
			{
				domain = domain.slice(0, -1);
			}

			// Only process .org domains (case insensitive)
			if (!domain.toLowerCase().endsWith('.org'))
			{
				return null;
			}

			// Extract eTLD+1 (remove subdomains)
			const domainParts = domain.split('.');
			if (domainParts.length >= 2)
			{
				domain = domainParts.slice(-2).join('.');
			}

			// For zone file processing, we can't easily determine registration date
			// from the zone file alone, so we'll include all domains and rely on
			// the registration API for accurate filtering
			return {
				domain: domain.toLowerCase(),
				source: this.name,
				metadata: {
					tld: 'org',
					strategy: 'zone-new',
					zoneFileDate: new Date().toISOString(),
					estimatedRegistration: 'recent',
				},
			};
		}
		catch (error)
		{
			// Skip malformed lines
			return null;
		}
	}

	private async makeRequest(
		url: string,
		config?: Record<string, unknown>,
	): Promise<AxiosResponse>
	{
		const fullUrl = url.startsWith('http') ? url : `${this.config.baseUrl}${url}`;

		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= this.config.maxRetries; attempt += 1)
		{
			try
			{
				this.logger.debug('Making HTTP request', { url: fullUrl, attempt });

				const response = await axios({
					url: fullUrl,
					timeout: this.config.requestTimeoutMs,
					...config,
				});

				return response;
			}
			catch (error)
			{
				lastError = error as Error;
				this.logger.warn('HTTP request failed', {
					url: fullUrl,
					attempt,
					error: (error as Error).message,
				});

				if (attempt < this.config.maxRetries)
				{
					const delay = this.config.retryDelayMs * (2 ** (attempt - 1));
					await this.sleep(delay);
				}
			}
		}

		throw new Error(
			`Failed to make request after ${this.config.maxRetries} attempts: ${lastError?.message}`,
		);
	}

	private sleep(ms: number): Promise<void>
	{
		return new Promise((resolve) =>
		{
			setTimeout(resolve, ms);
		});
	}
}

export type {
	PIRConfigType,
	PIRDomainInfoType,
	PIRContactType,
	PIRZoneFileInfoType,
	PIRRegistrationEventType,
};

export default PIRConnector;
