import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type { DiscoveryEngine } from '../interfaces/DiscoveryEngine';
import type { SourceConnector } from '../interfaces/SourceConnector';
import type { ProvenanceTracker } from '../provenance/ProvenanceTracker';

import SmartDiscoveryScheduler from './SmartDiscoveryScheduler';
import LiveContentScheduler from './LiveContentScheduler';
import SchedulerIntegrationService from '../content/SchedulerIntegrationService';
import IntegratedContentGenerator from '../content/IntegratedContentGenerator';

const logger = sharedLogger.getLogger('SchedulerOrchestrator');

interface OrchestratorConfig
{
	// Discovery scheduling
	discoveryConfig: {
		enableDiscovery: boolean;
		maxConcurrentDiscoveryJobs: number;
		discoveryIntervalMs: number;
	};

	// Live content scheduling
	liveContentConfig: {
		enableLiveContentUpgrades: boolean;
		maxConcurrentUpgrades: number;
		upgradeIntervalMs: number;
		autoScheduleAfterDiscovery: boolean;
	};

	// Integration settings
	integrationConfig: {
		enableMetrics: boolean;
		enableHealthChecks: boolean;
		coordinateScheduling: boolean; // Coordinate between discovery and content scheduling
	};

	// Performance settings
	performanceConfig: {
		maxTotalConcurrentJobs: number;
		backpressureThreshold: number; // Stop scheduling new jobs if queue depth exceeds this
		gracefulShutdownTimeoutMs: number;
	};
}

interface OrchestratorMetrics
{
	// Discovery metrics
	totalDiscoveryJobs: number;
	totalDomainsDiscovered: number;
	discoveryJobsRunning: number;

	// Content upgrade metrics
	totalUpgradeJobs: number;
	totalUpgradesCompleted: number;
	upgradeJobsRunning: number;

	// Integration metrics
	totalJobsScheduled: number;
	totalJobsCompleted: number;
	averageJobDuration: number;
	systemLoad: number; // 0-1 representing current load

	// Health metrics
	isHealthy: boolean;
	lastHealthCheck: Date;
	errorCount: number;
}

interface OrchestratorHealthStatus
{
	overall: 'healthy' | 'degraded' | 'unhealthy';
	components: {
		discoveryScheduler: 'healthy' | 'degraded' | 'unhealthy';
		liveContentScheduler: 'healthy' | 'degraded' | 'unhealthy';
		schedulerIntegration: 'healthy' | 'degraded' | 'unhealthy';
		contentGenerator: 'healthy' | 'degraded' | 'unhealthy';
	};
	metrics: OrchestratorMetrics;
	issues: string[];
	recommendations: string[];
}

/**
 * SchedulerOrchestrator coordinates all scheduling activities for the domain seeder
 *
 * This orchestrator manages:
 * - Discovery job scheduling (SmartDiscoveryScheduler)
 * - Live content upgrade scheduling (LiveContentScheduler)
 * - Integration between discovery and content generation
 * - Overall system health and performance monitoring
 * - Graceful coordination of all scheduling activities
 */
class SchedulerOrchestrator
{
	private readonly logger: Logger;

	private readonly config: OrchestratorConfig;

	// Core schedulers
	private readonly discoveryScheduler: SmartDiscoveryScheduler;

	private readonly liveContentScheduler: LiveContentScheduler;

	private readonly schedulerIntegration: SchedulerIntegrationService;

	private readonly contentGenerator: IntegratedContentGenerator;

	// State management
	private isRunning = false;

	private healthCheckInterval?: NodeJS.Timeout;

	private coordinationInterval?: NodeJS.Timeout;

	// Metrics
	private readonly metrics: OrchestratorMetrics = {
		totalDiscoveryJobs: 0,
		totalDomainsDiscovered: 0,
		discoveryJobsRunning: 0,
		totalUpgradeJobs: 0,
		totalUpgradesCompleted: 0,
		upgradeJobsRunning: 0,
		totalJobsScheduled: 0,
		totalJobsCompleted: 0,
		averageJobDuration: 0,
		systemLoad: 0,
		isHealthy: true,
		lastHealthCheck: new Date(),
		errorCount: 0,
	};

	constructor(
		discoveryEngine: DiscoveryEngine,
		sourceConnectors: Map<string, SourceConnector>,
		config: Partial<OrchestratorConfig> = {},
		provenanceTracker?: ProvenanceTracker,
	)
	{
		this.logger = logger;

		this.config = {
			discoveryConfig: {
				enableDiscovery: config.discoveryConfig?.enableDiscovery ?? true,
				maxConcurrentDiscoveryJobs: config.discoveryConfig?.maxConcurrentDiscoveryJobs ?? 3,
				discoveryIntervalMs: config.discoveryConfig?.discoveryIntervalMs ?? 300000, // 5 minutes
			},
			liveContentConfig: {
				enableLiveContentUpgrades: config.liveContentConfig?.enableLiveContentUpgrades ?? true,
				maxConcurrentUpgrades: config.liveContentConfig?.maxConcurrentUpgrades ?? 5,
				upgradeIntervalMs: config.liveContentConfig?.upgradeIntervalMs ?? 300000, // 5 minutes
				autoScheduleAfterDiscovery: config.liveContentConfig?.autoScheduleAfterDiscovery ?? true,
			},
			integrationConfig: {
				enableMetrics: config.integrationConfig?.enableMetrics ?? true,
				enableHealthChecks: config.integrationConfig?.enableHealthChecks ?? true,
				coordinateScheduling: config.integrationConfig?.coordinateScheduling ?? true,
			},
			performanceConfig: {
				maxTotalConcurrentJobs: config.performanceConfig?.maxTotalConcurrentJobs ?? 10,
				backpressureThreshold: config.performanceConfig?.backpressureThreshold ?? 100,
				gracefulShutdownTimeoutMs: config.performanceConfig?.gracefulShutdownTimeoutMs ?? 30000,
			},
		};

		// Initialize content generator
		this.contentGenerator = new IntegratedContentGenerator({
			enableVersioning: true,
			autoScheduleLiveUpdates: this.config.liveContentConfig.autoScheduleAfterDiscovery,
			contentUpdateBatchSize: 50,
			enableContentMetrics: this.config.integrationConfig.enableMetrics,
		}, provenanceTracker);

		// Initialize scheduler integration service
		this.schedulerIntegration = new SchedulerIntegrationService(
			this.contentGenerator,
			{
				batchSize: 20,
				maxConcurrentJobs: this.config.liveContentConfig.maxConcurrentUpgrades,
				jobIntervalMs: this.config.liveContentConfig.upgradeIntervalMs,
				enableMetrics: this.config.integrationConfig.enableMetrics,
				retryFailedJobs: true,
				maxRetries: 3,
			},
			provenanceTracker,
		);

		// Initialize live content scheduler
		this.liveContentScheduler = new LiveContentScheduler(
			this.contentGenerator,
			this.schedulerIntegration,
			{
				upgradeIntervalMs: this.config.liveContentConfig.upgradeIntervalMs,
				maxConcurrentUpgrades: this.config.liveContentConfig.maxConcurrentUpgrades,
				enableMetrics: this.config.integrationConfig.enableMetrics,
				enableHealthChecks: this.config.integrationConfig.enableHealthChecks,
			},
			provenanceTracker,
		);

		// Initialize discovery scheduler with live content integration
		this.discoveryScheduler = new SmartDiscoveryScheduler(
			discoveryEngine,
			sourceConnectors,
			{
				maxConcurrentJobs: this.config.discoveryConfig.maxConcurrentDiscoveryJobs,
				enableMetrics: this.config.integrationConfig.enableMetrics,
			},
			provenanceTracker,
			undefined, // Use default effectiveness monitor
			this.config.liveContentConfig.autoScheduleAfterDiscovery ? this.liveContentScheduler : undefined,
		);

		this.logger.info('SchedulerOrchestrator initialized', {
			config: this.config,
		});
	}

	/**
	 * Start all scheduling services
	 */
	async start(): Promise<void>
	{
		if (this.isRunning)
		{
			return;
		}

		this.isRunning = true;

		try
		{
			this.logger.info('Starting SchedulerOrchestrator');

			// Start services in dependency order
			if (this.config.liveContentConfig.enableLiveContentUpgrades)
			{
				await this.schedulerIntegration.start();
				await this.liveContentScheduler.start();
			}

			if (this.config.discoveryConfig.enableDiscovery)
			{
				await this.discoveryScheduler.start();
			}

			// Start health monitoring
			if (this.config.integrationConfig.enableHealthChecks)
			{
				this.startHealthMonitoring();
			}

			// Start coordination if enabled
			if (this.config.integrationConfig.coordinateScheduling)
			{
				this.startCoordination();
			}

			this.logger.info('SchedulerOrchestrator started successfully');
		}
		catch (error)
		{
			this.isRunning = false;
			this.logger.error('Failed to start SchedulerOrchestrator', {
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Stop all scheduling services gracefully
	 */
	async stop(): Promise<void>
	{
		if (!this.isRunning)
		{
			return;
		}

		this.logger.info('Stopping SchedulerOrchestrator');

		// Stop coordination and health monitoring
		if (this.coordinationInterval)
		{
			clearInterval(this.coordinationInterval);
			this.coordinationInterval = undefined;
		}

		if (this.healthCheckInterval)
		{
			clearInterval(this.healthCheckInterval);
			this.healthCheckInterval = undefined;
		}

		// Stop services in reverse dependency order
		const shutdownPromises: Promise<void>[] = [];

		if (this.config.discoveryConfig.enableDiscovery)
		{
			shutdownPromises.push(this.discoveryScheduler.stop());
		}

		if (this.config.liveContentConfig.enableLiveContentUpgrades)
		{
			shutdownPromises.push(this.liveContentScheduler.stop());
			shutdownPromises.push(this.schedulerIntegration.stop());
		}

		// Wait for graceful shutdown with timeout
		try
		{
			await Promise.race([
				Promise.all(shutdownPromises),
				new Promise((_, reject) => setTimeout(() => reject(new Error('Shutdown timeout')),
					this.config.performanceConfig.gracefulShutdownTimeoutMs)),
			]);
		}
		catch (error)
		{
			this.logger.warn('Graceful shutdown timeout, forcing stop', {
				error: (error as Error).message,
			});
		}

		this.isRunning = false;
		this.logger.info('SchedulerOrchestrator stopped');
	}

	/**
	 * Get comprehensive health status
	 */
	async getHealthStatus(): Promise<OrchestratorHealthStatus>
	{
		const issues: string[] = [];
		const recommendations: string[] = [];

		// Check discovery scheduler health
		let discoveryHealth: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
		if (this.config.discoveryConfig.enableDiscovery)
		{
			const discoveryMetrics = this.discoveryScheduler.getMetrics();
			const runningJobs = this.discoveryScheduler.getJobsByStatus('running').length;

			if (discoveryMetrics.totalJobsFailed > discoveryMetrics.totalJobsCompleted * 0.1)
			{
				discoveryHealth = 'degraded';
				issues.push('High discovery job failure rate');
				recommendations.push('Check source connector health and network connectivity');
			}

			if (runningJobs > this.config.discoveryConfig.maxConcurrentDiscoveryJobs)
			{
				discoveryHealth = 'unhealthy';
				issues.push('Discovery jobs exceeding concurrency limit');
			}
		}

		// Check live content scheduler health
		let liveContentHealth: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
		if (this.config.liveContentConfig.enableLiveContentUpgrades)
		{
			const liveHealth = await this.liveContentScheduler.getHealthStatus();
			const liveMetrics = this.liveContentScheduler.getMetrics();

			if (!liveHealth.isHealthy)
			{
				liveContentHealth = 'unhealthy';
				issues.push('Live content scheduler unhealthy');
			}
			else if (liveMetrics.successRate < 0.8)
			{
				liveContentHealth = 'degraded';
				issues.push('Low live content upgrade success rate');
				recommendations.push('Check content generation services and network connectivity');
			}
		}

		// Check scheduler integration health
		let schedulerIntegrationHealth: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
		if (this.config.liveContentConfig.enableLiveContentUpgrades)
		{
			const integrationHealth = await this.schedulerIntegration.getHealthStatus();
			const integrationMetrics = this.schedulerIntegration.getMetrics();

			if (!integrationHealth.isHealthy)
			{
				schedulerIntegrationHealth = 'unhealthy';
				issues.push('Scheduler integration service unhealthy');
			}
			else if (integrationMetrics.totalJobsFailed > integrationMetrics.totalJobsCompleted * 0.1)
			{
				schedulerIntegrationHealth = 'degraded';
				issues.push('High scheduler integration job failure rate');
			}
		}

		// Check content generator health (simplified)
		let contentGeneratorHealth: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
		const contentMetrics = this.contentGenerator.getMetrics();
		if (contentMetrics.errorCount > contentMetrics.preGeneratedCount * 0.1)
		{
			contentGeneratorHealth = 'degraded';
			issues.push('High content generation error rate');
			recommendations.push('Check AI provider connectivity and rate limits');
		}

		// Update metrics
		await this.updateMetrics();

		// Determine overall health
		const componentHealths = [discoveryHealth, liveContentHealth, schedulerIntegrationHealth, contentGeneratorHealth];
		let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

		if (componentHealths.includes('unhealthy'))
		{
			overall = 'unhealthy';
		}
		else if (componentHealths.includes('degraded'))
		{
			overall = 'degraded';
		}

		// System-wide checks
		if (this.metrics.systemLoad > 0.9)
		{
			overall = 'degraded';
			issues.push('High system load');
			recommendations.push('Consider reducing concurrent job limits');
		}

		if (this.metrics.errorCount > 10)
		{
			if (overall === 'healthy') overall = 'degraded';
			issues.push('High error count');
		}

		return {
			overall,
			components: {
				discoveryScheduler: discoveryHealth,
				liveContentScheduler: liveContentHealth,
				schedulerIntegration: schedulerIntegrationHealth,
				contentGenerator: contentGeneratorHealth,
			},
			metrics: { ...this.metrics },
			issues,
			recommendations,
		};
	}

	/**
	 * Get orchestrator metrics
	 */
	getMetrics(): OrchestratorMetrics
	{
		return { ...this.metrics };
	}

	/**
	 * Manually trigger coordination between schedulers
	 */
	async coordinateSchedulers(): Promise<void>
	{
		if (!this.config.integrationConfig.coordinateScheduling)
		{
			return;
		}

		try
		{
			// Check system load and adjust scheduling accordingly
			const currentLoad = await this.calculateSystemLoad();

			if (currentLoad > this.config.performanceConfig.backpressureThreshold / 100)
			{
				this.logger.warn('High system load detected, applying backpressure', {
					currentLoad,
					threshold: this.config.performanceConfig.backpressureThreshold / 100,
				});

				// Could implement backpressure logic here
				// For example, temporarily reduce concurrent job limits
			}

			// Coordinate discovery and content scheduling
			if (this.config.liveContentConfig.autoScheduleAfterDiscovery)
			{
				// This coordination is already handled by the SmartDiscoveryScheduler
				// integration with LiveContentScheduler
				this.logger.debug('Discovery-to-content coordination active');
			}

			this.logger.debug('Scheduler coordination completed', {
				systemLoad: currentLoad,
			});
		}
		catch (error)
		{
			this.metrics.errorCount++;
			this.logger.error('Scheduler coordination failed', {
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Get individual scheduler instances (for advanced usage)
	 */
	getSchedulers(): {
		discovery: SmartDiscoveryScheduler;
		liveContent: LiveContentScheduler;
		integration: SchedulerIntegrationService;
		contentGenerator: IntegratedContentGenerator;
		}
	{
		return {
			discovery: this.discoveryScheduler,
			liveContent: this.liveContentScheduler,
			integration: this.schedulerIntegration,
			contentGenerator: this.contentGenerator,
		};
	}

	private startHealthMonitoring(): void
	{
		this.healthCheckInterval = setInterval(async () =>
		{
			try
			{
				const healthStatus = await this.getHealthStatus();
				this.metrics.isHealthy = healthStatus.overall === 'healthy';
				this.metrics.lastHealthCheck = new Date();

				if (healthStatus.overall !== 'healthy')
				{
					this.logger.warn('Health check detected issues', {
						overall: healthStatus.overall,
						issues: healthStatus.issues,
						recommendations: healthStatus.recommendations,
					});
				}
			}
			catch (error)
			{
				this.metrics.errorCount++;
				this.metrics.isHealthy = false;
				this.logger.error('Health check failed', {
					error: (error as Error).message,
				});
			}
		}, 60000); // Check every minute
	}

	private startCoordination(): void
	{
		this.coordinationInterval = setInterval(async () =>
		{
			await this.coordinateSchedulers();
		}, 120000); // Coordinate every 2 minutes
	}

	private async updateMetrics(): Promise<void>
	{
		try
		{
			// Update discovery metrics
			if (this.config.discoveryConfig.enableDiscovery)
			{
				const discoveryMetrics = this.discoveryScheduler.getMetrics();
				this.metrics.totalDiscoveryJobs = discoveryMetrics.totalJobsScheduled;
				this.metrics.discoveryJobsRunning = this.discoveryScheduler.getJobsByStatus('running').length;
			}

			// Update live content metrics
			if (this.config.liveContentConfig.enableLiveContentUpgrades)
			{
				const liveMetrics = this.liveContentScheduler.getMetrics();
				this.metrics.totalUpgradeJobs = liveMetrics.totalUpgradesScheduled;
				this.metrics.totalUpgradesCompleted = liveMetrics.totalUpgradesCompleted;
				this.metrics.upgradeJobsRunning = (await this.liveContentScheduler.getHealthStatus()).runningUpgrades;
			}

			// Update integration metrics
			if (this.config.liveContentConfig.enableLiveContentUpgrades)
			{
				const integrationMetrics = this.schedulerIntegration.getMetrics();
				this.metrics.totalJobsScheduled += integrationMetrics.totalJobsCreated;
				this.metrics.totalJobsCompleted += integrationMetrics.totalJobsCompleted;

				if (integrationMetrics.totalJobsCompleted > 0)
				{
					this.metrics.averageJobDuration = integrationMetrics.averageExecutionTime;
				}
			}

			// Calculate system load
			this.metrics.systemLoad = await this.calculateSystemLoad();
		}
		catch (error)
		{
			this.logger.error('Failed to update metrics', {
				error: (error as Error).message,
			});
		}
	}

	private async calculateSystemLoad(): Promise<number>
	{
		// Comprehensive system load calculation
		let totalLoad = 0;
		let factors = 0;

		// Factor 1: Job concurrency load (weight: 40%)
		const totalRunningJobs = this.metrics.discoveryJobsRunning + this.metrics.upgradeJobsRunning;
		const maxJobs = this.config.performanceConfig.maxTotalConcurrentJobs;
		const jobLoad = Math.min(totalRunningJobs / maxJobs, 1.0);
		totalLoad += jobLoad * 0.4;
		factors += 0.4;

		// Factor 2: System resource load (weight: 30%)
		try
		{
			const os = require('os');
			const loadAvg = os.loadavg()[0]; // 1-minute load average
			const cpuCount = os.cpus().length;
			const cpuLoad = Math.min(loadAvg / cpuCount, 1.0);
			totalLoad += cpuLoad * 0.3;
			factors += 0.3;
		}
		catch (error)
		{
			// If we can't get CPU load, assume moderate load
			totalLoad += 0.5 * 0.3;
			factors += 0.3;
		}

		// Factor 3: Memory pressure (weight: 20%)
		try
		{
			const os = require('os');
			const totalMem = os.totalmem();
			const freeMem = os.freemem();
			const memoryLoad = 1 - (freeMem / totalMem);
			totalLoad += memoryLoad * 0.2;
			factors += 0.2;
		}
		catch (error)
		{
			// If we can't get memory info, assume moderate load
			totalLoad += 0.5 * 0.2;
			factors += 0.2;
		}

		// Factor 4: Queue depth pressure (weight: 10%)
		const avgQueueDepth = (this.metrics.discoveryQueueDepth + this.metrics.upgradeQueueDepth) / 2;
		const maxQueueDepth = 10000; // Reasonable maximum
		const queueLoad = Math.min(avgQueueDepth / maxQueueDepth, 1.0);
		totalLoad += queueLoad * 0.1;
		factors += 0.1;

		return factors > 0 ? Math.min(totalLoad / factors, 1.0) : 0;
	}
}

export type {
	OrchestratorConfig,
	OrchestratorMetrics,
	OrchestratorHealthStatus,
};

export { SchedulerOrchestrator };

export default SchedulerOrchestrator;
