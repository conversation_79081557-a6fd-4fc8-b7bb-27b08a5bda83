/**
 * Live Content Scheduler for upgrading preGenerated domain content to live analysis
 */
import type { Logger } from '@shared';
import { logger as sharedLogger } from '@shared';
import type { ProvenanceTracker } from '../provenance/ProvenanceTracker';
import type IntegratedContentGenerator from '../content/IntegratedContentGenerator';
import type SchedulerIntegrationService from '../content/SchedulerIntegrationService';
import type { GeneratedContent } from '../content/IntegratedContentGenerator';
// import type { JobExecutionResult } from '../content/SchedulerIntegrationService';

const logger = sharedLogger.getLogger('LiveContentScheduler');

type LiveContentSchedulerConfig =
{
	// Scheduling intervals
	upgradeIntervalMs: number; // How often to check for upgrade candidates
	batchSize: number; // How many domains to process per batch

	// Upgrade criteria
	minAgeForUpgrade: number; // Minimum age in hours before considering upgrade
	maxPreGeneratedAge: number; // Maximum age in hours before forcing upgrade
	confidenceThreshold: number; // Minimum confidence score to trigger upgrade

	// Performance tuning
	maxConcurrentUpgrades: number;
	upgradeTimeoutMs: number;
	retryAttempts: number;

	// Quality control
	enableQualityChecks: boolean;
	minContentQualityScore: number;

	// Metrics and monitoring
	enableMetrics: boolean;
	metricsRetentionDays: number;
};

type UpgradeJob =
{
	id: string;
	domain: string;
	priority: 'high' | 'medium' | 'low';
	scheduledAt: Date;
	startedAt?: Date;
	completedAt?: Date;
	status: 'pending' | 'running' | 'completed' | 'failed';
	attempts: number;
	maxAttempts: number;
	errorMessage?: string;
	preGeneratedContent?: GeneratedContent;
	liveContent?: GeneratedContent;
	qualityImprovement?: number;
};

type UpgradeMetrics =
{
	totalUpgradesScheduled: number;
	totalUpgradesCompleted: number;
	totalUpgradesFailed: number;
	avgUpgradeDuration: number;
	avgQualityImprovement: number;
	runningUpgrades: number;
	pendingUpgrades: number;
};

/**
 * LiveContentScheduler manages the upgrade process from preGenerated domain content
 * to live analysis based on priority, age, and confidence thresholds.
 */
class LiveContentScheduler
{
	private readonly logger: Logger;

	private readonly contentGenerator: IntegratedContentGenerator;

	private readonly schedulerIntegration: SchedulerIntegrationService;

	private readonly provenanceTracker?: ProvenanceTracker;

	private readonly config: LiveContentSchedulerConfig;

	// Job management
	private readonly upgradeJobs = new Map<string, UpgradeJob>();

	private readonly runningJobs = new Set<string>();

	private schedulerInterval?: NodeJS.Timeout;

	private isRunning = false;

	// Metrics
	private readonly metrics: UpgradeMetrics = {
		totalUpgradesScheduled: 0,
		totalUpgradesCompleted: 0,
		totalUpgradesFailed: 0,
		avgUpgradeDuration: 0,
		avgQualityImprovement: 0,
		runningUpgrades: 0,
		pendingUpgrades: 0,
	};

	constructor(
		contentGenerator: IntegratedContentGenerator,
		schedulerIntegration: SchedulerIntegrationService,
		config: Partial<LiveContentSchedulerConfig> = {},
		provenanceTracker?: ProvenanceTracker,
	)
	{
		this.logger = logger;
		this.contentGenerator = contentGenerator;
		this.schedulerIntegration = schedulerIntegration;
		this.provenanceTracker = provenanceTracker;

		this.config = {
			upgradeIntervalMs: config.upgradeIntervalMs ?? 300000, // 5 minutes
			batchSize: config.batchSize ?? 10,
			minAgeForUpgrade: config.minAgeForUpgrade ?? 24, // 24 hours
			maxPreGeneratedAge: config.maxPreGeneratedAge ?? 168, // 7 days
			confidenceThreshold: config.confidenceThreshold ?? 0.7,
			maxConcurrentUpgrades: config.maxConcurrentUpgrades ?? 3,
			upgradeTimeoutMs: config.upgradeTimeoutMs ?? 600000, // 10 minutes
			retryAttempts: config.retryAttempts ?? 2,
			enableQualityChecks: config.enableQualityChecks ?? true,
			minContentQualityScore: config.minContentQualityScore ?? 0.6,
			enableMetrics: config.enableMetrics ?? true,
			metricsRetentionDays: config.metricsRetentionDays ?? 30,
		};

		this.logger.info('LiveContentScheduler initialized', {
			config: this.config,
		});
	}

	/**
	 * Start the scheduler
	 */
	async start(): Promise<void>
	{
		if (this.isRunning)
		{
			return;
		}

		this.isRunning = true;

		// Start scheduler interval
		this.schedulerInterval = setInterval(async () =>
		{
			await this.processUpgradeQueue();
		}, this.config.upgradeIntervalMs);

		this.logger.info('LiveContentScheduler started', {
			intervalMs: this.config.upgradeIntervalMs,
		});
	}

	/**
	 * Stop the scheduler
	 */
	async stop(): Promise<void>
	{
		this.isRunning = false;

		if (this.schedulerInterval)
		{
			clearInterval(this.schedulerInterval);
			this.schedulerInterval = undefined;
		}

		// Wait for running jobs to complete
		while (this.runningJobs.size > 0)
		{
			await this.sleep(1000);
		}

		this.logger.info('LiveContentScheduler stopped');
	}

	/**
	 * Process eligible domains for content upgrade
	 */
	async processEligibleDomains(domains: string[]): Promise<void>
	{
		if (!this.isRunning || domains.length === 0)
		{
			return;
		}

		this.logger.info('Processing eligible domains for content upgrade', {
			domainCount: domains.length,
		});

		// Filter domains that are eligible for upgrade
		const eligibleDomains = await this.filterEligibleDomains(domains);

		// Schedule upgrade jobs for eligible domains
		for (const domain of eligibleDomains)
		{
			await this.scheduleUpgradeJob(domain);
		}

		this.logger.info('Scheduled upgrade jobs for eligible domains', {
			totalDomains: domains.length,
			eligibleDomains: eligibleDomains.length,
			scheduledJobs: eligibleDomains.length,
		});
	}

	/**
	 * Get health status
	 */
	async getHealthStatus(): Promise<{
		isHealthy: boolean;
		runningUpgrades: number;
		pendingUpgrades: number;
		failedUpgrades: number;
		lastUpgradeTime?: Date;
	}>
	{
		const runningUpgrades = this.runningJobs.size;
		const pendingUpgrades = Array.from(this.upgradeJobs.values())
			.filter(job => job.status === 'pending').length;
		const failedUpgrades = Array.from(this.upgradeJobs.values())
			.filter(job => job.status === 'failed').length;

		const lastCompletedJob = Array.from(this.upgradeJobs.values())
			.filter(job => job.status === 'completed' && job.completedAt)
			.sort((a, b) => (b.completedAt!.getTime() - a.completedAt!.getTime()))[0];

		const isHealthy = runningUpgrades < this.config.maxConcurrentUpgrades &&
			failedUpgrades < 10; // Arbitrary threshold

		return {
			isHealthy,
			runningUpgrades,
			pendingUpgrades,
			failedUpgrades,
			lastUpgradeTime: lastCompletedJob?.completedAt,
		};
	}

	/**
	 * Get metrics
	 */
	getMetrics(): UpgradeMetrics
	{
		this.metrics.runningUpgrades = this.runningJobs.size;
		this.metrics.pendingUpgrades = Array.from(this.upgradeJobs.values())
			.filter(job => job.status === 'pending').length;

		return { ...this.metrics };
	}

	/**
	 * Get upgrade jobs
	 */
	getUpgradeJobs(): UpgradeJob[]
	{
		return Array.from(this.upgradeJobs.values());
	}

	/**
	 * Get jobs by status
	 */
	getJobsByStatus(status: UpgradeJob['status']): UpgradeJob[]
	{
		return Array.from(this.upgradeJobs.values()).filter(job => job.status === status);
	}

	private async processUpgradeQueue(): Promise<void>
	{
		if (this.runningJobs.size >= this.config.maxConcurrentUpgrades)
		{
			return; // At max concurrency
		}

		const pendingJobs = Array.from(this.upgradeJobs.values())
			.filter(job => job.status === 'pending' && !this.runningJobs.has(job.id))
			.sort((a, b) =>
			{
				// Sort by priority, then by scheduled time
				const priorityOrder = { high: 3, medium: 2, low: 1 };
				const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
				if (priorityDiff !== 0) return priorityDiff;
				return a.scheduledAt.getTime() - b.scheduledAt.getTime();
			});

		const availableSlots = this.config.maxConcurrentUpgrades - this.runningJobs.size;
		const jobsToRun = pendingJobs.slice(0, availableSlots);

		for (const job of jobsToRun)
		{
			// Don't await - let jobs run concurrently
			this.executeUpgradeJob(job).catch((error) =>
			{
				this.logger.error('Upgrade job execution failed', {
					jobId: job.id,
					domain: job.domain,
					error: error.message,
				});
			});
		}
	}

	private async executeUpgradeJob(job: UpgradeJob): Promise<void>
	{
		const startTime = Date.now();
		this.runningJobs.add(job.id);

		try
		{
			job.status = 'running';
			job.startedAt = new Date();
			job.attempts++;

			this.logger.info('Starting content upgrade job', {
				jobId: job.id,
				domain: job.domain,
				priority: job.priority,
				attempt: job.attempts,
			});

			// Generate live content for the domain
			const liveContent = await this.contentGenerator.generateContent({
				domain: job.domain,
				analysisType: 'live',
				includeScreenshots: true,
				includeSEOAnalysis: true,
				includePerformanceMetrics: true,
			});

			job.liveContent = liveContent;

			// Calculate quality improvement if we have preGenerated content
			if (job.preGeneratedContent && this.config.enableQualityChecks)
			{
				job.qualityImprovement = this.calculateQualityImprovement(
					job.preGeneratedContent,
					liveContent,
				);

				// Check if the upgrade meets quality standards
				if (job.qualityImprovement < 0.1) // Less than 10% improvement
				{
					this.logger.warn('Content upgrade shows minimal improvement', {
						jobId: job.id,
						domain: job.domain,
						qualityImprovement: job.qualityImprovement,
					});
				}
			}

			// Execute the upgrade through scheduler integration
			const result = await this.schedulerIntegration.executeContentUpgrade({
				domain: job.domain,
				content: liveContent,
				upgradeType: 'preGenerated-to-live',
				priority: job.priority,
			});

			if (result.success)
			{
				job.status = 'completed';
				job.completedAt = new Date();

				const duration = Date.now() - startTime;
				this.updateMetrics(true, duration, job.qualityImprovement || 0);

				this.logger.info('Content upgrade job completed', {
					jobId: job.id,
					domain: job.domain,
					duration,
					qualityImprovement: job.qualityImprovement,
				});

				// Track provenance if available
				if (this.provenanceTracker)
				{
					await this.provenanceTracker.trackContentUpgrade({
						domain: job.domain,
						fromType: 'preGenerated',
						toType: 'live',
						upgradeTime: job.completedAt,
						qualityImprovement: job.qualityImprovement,
						jobId: job.id,
					});
				}
			}
			else
			{
				throw new Error(result.error || 'Content upgrade failed');
			}
		}
		catch (error)
		{
			const duration = Date.now() - startTime;

			// Handle job failure and retry logic
			if (job.attempts < job.maxAttempts)
			{
				job.status = 'pending';
				// Schedule retry with exponential backoff
				const retryDelay = Math.min(1000 * 2 ** (job.attempts - 1), 3600000); // Max 1 hour
				job.scheduledAt = new Date(Date.now() + retryDelay);

				this.logger.warn('Content upgrade job failed, scheduling retry', {
					jobId: job.id,
					domain: job.domain,
					error: error.message,
					attempt: job.attempts,
					retryAt: job.scheduledAt.toISOString(),
				});
			}
			else
			{
				job.status = 'failed';
				job.completedAt = new Date();
				job.errorMessage = error.message;

				this.updateMetrics(false, duration, 0);

				this.logger.error('Content upgrade job failed permanently', {
					jobId: job.id,
					domain: job.domain,
					error: error.message,
					attempts: job.attempts,
				});
			}
		}
		finally
		{
			this.runningJobs.delete(job.id);
		}
	}

	private async filterEligibleDomains(domains: string[]): Promise<string[]>
	{
		const eligible: string[] = [];

		for (const domain of domains)
		{
			try
			{
				// Check if domain already has a pending or running upgrade job
				const existingJob = Array.from(this.upgradeJobs.values())
					.find(job => job.domain === domain &&
						(job.status === 'pending' || job.status === 'running'));

				if (existingJob)
				{
					continue; // Skip if already being processed
				}

				// Check domain eligibility criteria
				const isEligible = await this.isDomainEligibleForUpgrade(domain);
				if (isEligible)
				{
					eligible.push(domain);
				}
			}
			catch (error)
			{
				this.logger.warn('Error checking domain eligibility', {
					domain,
					error: error.message,
				});
			}
		}

		return eligible;
	}

	private async isDomainEligibleForUpgrade(domain: string): Promise<boolean>
	{
		// This would typically check:
		// 1. Age of preGenerated content
		// 2. Confidence score of existing content
		// 3. Domain priority/importance
		// 4. Resource availability
		// 5. Recent upgrade history

		try
		{
			// Check if domain has pre-generated content
			const hasPreGenerated = await this.checkPreGeneratedContent(domain);
			if (!hasPreGenerated)
			{
				return false; // No pre-generated content to upgrade
			}

			// Check content age
			const contentAge = await this.getContentAge(domain);
			if (contentAge < this.config.minContentAgeHours * 60 * 60 * 1000)
			{
				return false; // Content too fresh
			}

			// Check recent upgrade history
			const recentUpgrade = await this.getRecentUpgrade(domain);
			if (recentUpgrade && (Date.now() - recentUpgrade.getTime()) < this.config.upgradeIntervalHours * 60 * 60 * 1000)
			{
				return false; // Recently upgraded
			}

			// Check resource availability
			if (this.runningJobs.size >= this.config.maxConcurrentUpgrades)
			{
				return false; // Too many concurrent upgrades
			}

			return true;
		}
		catch (error)
		{
			this.logger.error('Error checking upgrade eligibility', {
				domain,
				error: (error as Error).message,
			});
			return false;
		}
	}

	private async scheduleUpgradeJob(domain: string): Promise<void>
	{
		const jobId = `upgrade-${domain}-${Date.now()}`;

		// Determine priority based on domain characteristics
		const priority = await this.determinePriority(domain);

		const job: UpgradeJob = {
			id: jobId,
			domain,
			priority,
			scheduledAt: new Date(),
			status: 'pending',
			attempts: 0,
			maxAttempts: this.config.retryAttempts,
		};

		// Try to get existing preGenerated content for comparison
		try
		{
			job.preGeneratedContent = await this.getExistingContent(domain);
		}
		catch (error)
		{
			this.logger.debug('No existing preGenerated content found', {
				domain,
				error: error.message,
			});
		}

		this.upgradeJobs.set(jobId, job);
		this.metrics.totalUpgradesScheduled++;

		this.logger.debug('Upgrade job scheduled', {
			jobId,
			domain,
			priority,
		});
	}

	private async determinePriority(domain: string): Promise<'high' | 'medium' | 'low'>
	{
		// This would typically consider:
		// 1. Domain ranking/popularity
		// 2. Business importance
		// 3. User request frequency
		// 4. Content staleness

		// Simplified logic for now
		return 'medium';
	}

	private async getExistingContent(domain: string): Promise<GeneratedContent | undefined>
	{
		// This would query the database for existing preGenerated content
		// For now, return undefined
		return undefined;
	}

	private calculateQualityImprovement(
		preGenerated: GeneratedContent,
		live: GeneratedContent,
	): number
	{
		// Calculate improvement based on various quality metrics
		// This is a simplified calculation

		let improvement = 0;
		let factors = 0;

		// Compare content length (more comprehensive content is generally better)
		if (preGenerated.summary && live.summary)
		{
			const lengthImprovement = (live.summary.length - preGenerated.summary.length) / preGenerated.summary.length;
			improvement += Math.max(0, lengthImprovement);
			factors++;
		}

		// Compare confidence scores
		if (preGenerated.confidence !== undefined && live.confidence !== undefined)
		{
			improvement += (live.confidence - preGenerated.confidence);
			factors++;
		}

		// Compare category accuracy (if available)
		if (preGenerated.categories && live.categories)
		{
			const categoryImprovement = live.categories.length > preGenerated.categories.length ? 0.1 : 0;
			improvement += categoryImprovement;
			factors++;
		}

		return factors > 0 ? improvement / factors : 0;
	}

	private async checkPreGeneratedContent(domain: string): Promise<boolean>
	{
		try
		{
			// Check if domain has pre-generated content in the database
			const scyllaClient = this.dbManager.getScyllaClient();
			const result = await scyllaClient.execute(
				'SELECT domain FROM domain_analysis WHERE domain = ? AND pre_generated = true LIMIT 1',
				[domain],
			);
			return result.rows.length > 0;
		}
		catch (error)
		{
			this.logger.error('Error checking pre-generated content', {
				domain,
				error: (error as Error).message,
			});
			return false;
		}
	}

	private async getContentAge(domain: string): Promise<number>
	{
		try
		{
			const scyllaClient = this.dbManager.getScyllaClient();
			const result = await scyllaClient.execute(
				'SELECT generated_at FROM domain_analysis WHERE domain = ? LIMIT 1',
				[domain],
			);

			if (result.rows.length === 0)
			{
				return 0;
			}

			const generatedAt = new Date(result.rows[0].generated_at);
			return Date.now() - generatedAt.getTime();
		}
		catch (error)
		{
			this.logger.error('Error getting content age', {
				domain,
				error: (error as Error).message,
			});
			return 0;
		}
	}

	private async getRecentUpgrade(domain: string): Promise<Date | null>
	{
		try
		{
			const scyllaClient = this.dbManager.getScyllaClient();
			const result = await scyllaClient.execute(
				'SELECT last_upgraded FROM domain_analysis WHERE domain = ? AND last_upgraded IS NOT NULL LIMIT 1',
				[domain],
			);

			if (result.rows.length === 0 || !result.rows[0].last_upgraded)
			{
				return null;
			}

			return new Date(result.rows[0].last_upgraded);
		}
		catch (error)
		{
			this.logger.error('Error getting recent upgrade info', {
				domain,
				error: (error as Error).message,
			});
			return null;
		}
	}

	private updateMetrics(success: boolean, duration: number, qualityImprovement: number): void
	{
		if (success)
		{
			this.metrics.totalUpgradesCompleted++;

			// Update average quality improvement
			const totalCompleted = this.metrics.totalUpgradesCompleted;
			this.metrics.avgQualityImprovement =
				(this.metrics.avgQualityImprovement * (totalCompleted - 1) + qualityImprovement) / totalCompleted;
		}
		else
		{
			this.metrics.totalUpgradesFailed++;
		}

		// Update average duration
		const totalJobs = this.metrics.totalUpgradesCompleted + this.metrics.totalUpgradesFailed;
		if (totalJobs > 0)
		{
			this.metrics.avgUpgradeDuration =
				(this.metrics.avgUpgradeDuration * (totalJobs - 1) + duration) / totalJobs;
		}
	}

	private sleep(ms: number): Promise<void>
	{
		// eslint-disable-next-line no-promise-executor-return
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export type {
	LiveContentSchedulerConfig,
	UpgradeJob,
	UpgradeMetrics,
};

export default LiveContentScheduler;
