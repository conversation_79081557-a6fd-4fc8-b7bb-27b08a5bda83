import { StructuredLogger } from './StructuredLogger';
import { LogAggregator } from './LogAggregator';
import { LogRetentionManager } from './LogRetentionManager';
import { ComprehensiveLoggingService } from './ComprehensiveLoggingService';

export type {
	LogContext,
	AuditEvent,
	ProcessingDecision,
	DiscoveryMetrics,
} from './StructuredLogger';

export type {
	LogQuery,
	LogEntry,
	AggregatedMetrics,
	LogAnalysis,
} from './LogAggregator';

export type {
	RetentionPolicy,
	ArchiveMetadata,
	RetentionStats,
} from './LogRetentionManager';

export type {
	LoggingConfig,
	OperationMetrics,
} from './ComprehensiveLoggingService';

export {
	StructuredLogger,
	LogAggregator,
	LogRetentionManager,
	ComprehensiveLoggingService,
};
