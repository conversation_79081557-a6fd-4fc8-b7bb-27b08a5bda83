import type { Logger, RedisClientWrapper } from '@shared';
import fs from 'node:fs/promises';
import path from 'node:path';
import zlib from 'node:zlib';
import { promisify } from 'node:util';
import type { LogEntry } from './LogAggregator';
import type { AuditEvent } from './StructuredLogger';

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

type RetentionPolicy =
{
	name: string;
	description: string;
	retentionDays: number;
	archiveAfterDays: number;
	compressionEnabled: boolean;
	logLevels?: string[];
	components?: string[];
	auditTypes?: string[];
};

type ArchiveMetadata =
{
	id: string;
	createdAt: Date;
	timeRange:
	{
		start: Date;
		end: Date;
	};
	policy: string;
	compressed: boolean;
	entryCount: number;
	fileSizeBytes: number;
	checksumMD5: string;
};

type RetentionStats =
{
	totalEntries: number;
	archivedEntries: number;
	deletedEntries: number;
	activeEntries: number;
	archivesSizeBytes: number;
	oldestEntry: Date;
	newestEntry: Date;
	retentionPolicies: Array<
	{
		name: string;
		appliedEntries: number;
		archivedEntries: number;
		deletedEntries: number;
	}>;
};

class LogRetentionManager
{
	private logger: Logger;

	private redis: RedisClientWrapper;

	private archiveDirectory: string;

	private retentionPolicies: Map<string, RetentionPolicy>;

	private defaultPolicy: RetentionPolicy;

	constructor(
		logger: Logger,
		redis: RedisClientWrapper,
		archiveDirectory = './logs/archives',
	)
	{
		this.logger = logger;
		this.redis = redis;
		this.archiveDirectory = archiveDirectory;
		this.retentionPolicies = new Map();

		this.defaultPolicy = {
			name: 'default',
			description: 'Default retention policy',
			retentionDays: 30,
			archiveAfterDays: 7,
			compressionEnabled: true,
		};

		this.initializeDefaultPolicies();
		this.ensureArchiveDirectory();
		this.startRetentionScheduler();
	}

	/**
	 * Add a retention policy
	 */
	addRetentionPolicy(policy: RetentionPolicy): void
	{
		this.retentionPolicies.set(policy.name, policy);
		this.logger.info('Retention policy added', {
			policyName: policy.name,
			retentionDays: policy.retentionDays,
			archiveAfterDays: policy.archiveAfterDays,
		});
	}

	/**
	 * Remove a retention policy
	 */
	removeRetentionPolicy(policyName: string): boolean
	{
		if (policyName === 'default')
		{
			throw new Error('Cannot remove default retention policy');
		}

		const removed = this.retentionPolicies.delete(policyName);
		if (removed)
		{
			this.logger.info('Retention policy removed', { policyName });
		}

		return removed;
	}

	/**
	 * Get all retention policies
	 */
	getRetentionPolicies(): RetentionPolicy[]
	{
		return Array.from(this.retentionPolicies.values());
	}

	/**
	 * Apply retention policies to logs and audit events
	 */
	async applyRetentionPolicies(): Promise<RetentionStats>
	{
		this.logger.info('Starting retention policy application');

		const stats: RetentionStats = {
			totalEntries: 0,
			archivedEntries: 0,
			deletedEntries: 0,
			activeEntries: 0,
			archivesSizeBytes: 0,
			oldestEntry: new Date(),
			newestEntry: new Date(0),
			retentionPolicies: [],
		};

		// Process each retention policy
		for (const policy of this.retentionPolicies.values())
		{
			const policyStats = await this.applyPolicy(policy);
			stats.totalEntries += policyStats.totalEntries;
			stats.archivedEntries += policyStats.archivedEntries;
			stats.deletedEntries += policyStats.deletedEntries;
			stats.retentionPolicies.push({
				name: policy.name,
				appliedEntries: policyStats.totalEntries,
				archivedEntries: policyStats.archivedEntries,
				deletedEntries: policyStats.deletedEntries,
			});
		}

		// Calculate remaining active entries
		stats.activeEntries = stats.totalEntries - stats.archivedEntries - stats.deletedEntries;

		// Calculate archive sizes
		stats.archivesSizeBytes = await this.calculateArchiveSize();

		// Find oldest and newest entries
		const { oldest, newest } = await this.findEntryDateRange();
		stats.oldestEntry = oldest;
		stats.newestEntry = newest;

		this.logger.info('Retention policy application completed', {
			totalEntries: stats.totalEntries,
			archivedEntries: stats.archivedEntries,
			deletedEntries: stats.deletedEntries,
			activeEntries: stats.activeEntries,
			archivesSizeMB: Math.round(stats.archivesSizeBytes / 1024 / 1024),
		});

		return stats;
	}

	/**
	 * Archive logs and audit events for a specific time range
	 */
	async archiveTimeRange(
		startDate: Date,
		endDate: Date,
		policyName = 'default',
	): Promise<ArchiveMetadata>
	{
		const policy = this.retentionPolicies.get(policyName) || this.defaultPolicy;

		this.logger.info('Starting manual archive', {
			startDate: startDate.toISOString(),
			endDate: endDate.toISOString(),
			policy: policy.name,
		});

		// Collect logs and audit events in time range
		const logs = await this.collectLogsInTimeRange(startDate, endDate, policy);
		const auditEvents = await this.collectAuditEventsInTimeRange(startDate, endDate, policy);

		// Create archive
		const archiveId = this.generateArchiveId(startDate, endDate, policy.name);
		const archiveData = {
			metadata: {
				id: archiveId,
				createdAt: new Date(),
				timeRange: { start: startDate, end: endDate },
				policy: policy.name,
				compressed: policy.compressionEnabled,
				entryCount: logs.length + auditEvents.length,
			},
			logs,
			auditEvents,
		};

		// Write archive file
		const archiveMetadata = await this.writeArchiveFile(archiveId, archiveData, policy.compressionEnabled);

		// Remove archived entries from Redis
		await this.removeArchivedEntries(logs, auditEvents);

		this.logger.info('Archive created successfully', {
			archiveId,
			entryCount: archiveMetadata.entryCount,
			fileSizeMB: Math.round(archiveMetadata.fileSizeBytes / 1024 / 1024),
			compressed: archiveMetadata.compressed,
		});

		return archiveMetadata;
	}

	/**
	 * Restore archived data for a specific time range
	 */
	async restoreArchive(archiveId: string): Promise<{
		logs: LogEntry[];
		auditEvents: AuditEvent[];
		metadata: ArchiveMetadata;
	}>
	{
		this.logger.info('Starting archive restoration', { archiveId });

		const archiveFile = path.join(this.archiveDirectory, `${archiveId}.json.gz`);
		const uncompressedFile = path.join(this.archiveDirectory, `${archiveId}.json`);

		let archiveData: any;

		try
		{
			// Try compressed file first
			if (await this.fileExists(archiveFile))
			{
				const compressedData = await fs.readFile(archiveFile);
				const decompressedData = await gunzip(compressedData);
				archiveData = JSON.parse(decompressedData.toString());
			}
			// Try uncompressed file
			else if (await this.fileExists(uncompressedFile))
			{
				const fileData = await fs.readFile(uncompressedFile, 'utf-8');
				archiveData = JSON.parse(fileData);
			}
			else
			{
				throw new Error(`Archive file not found: ${archiveId}`);
			}

			// Parse dates in the restored data
			const logs = archiveData.logs.map((log: any) => ({
				...log,
				timestamp: new Date(log.timestamp),
			}));

			const auditEvents = archiveData.auditEvents.map((event: any) => ({
				...event,
				timestamp: new Date(event.timestamp),
			}));

			const metadata = {
				...archiveData.metadata,
				createdAt: new Date(archiveData.metadata.createdAt),
				timeRange: {
					start: new Date(archiveData.metadata.timeRange.start),
					end: new Date(archiveData.metadata.timeRange.end),
				},
			};

			this.logger.info('Archive restored successfully', {
				archiveId,
				logsCount: logs.length,
				auditEventsCount: auditEvents.length,
			});

			return { logs, auditEvents, metadata };
		}
		catch (error)
		{
			this.logger.error('Failed to restore archive', {
				archiveId,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * List available archives
	 */
	async listArchives(): Promise<ArchiveMetadata[]>
	{
		try
		{
			const files = await fs.readdir(this.archiveDirectory);
			const archiveFiles = files.filter(f => f.endsWith('.json') || f.endsWith('.json.gz'));

			const archives: ArchiveMetadata[] = [];

			for (const file of archiveFiles)
			{
				try
				{
					const filePath = path.join(this.archiveDirectory, file);
					const stats = await fs.stat(filePath);

					// Extract archive ID from filename
					const archiveId = file.replace(/\.(json|json\.gz)$/, '');

					// Try to read metadata from the file
					let metadata: ArchiveMetadata;

					if (file.endsWith('.gz'))
					{
						const compressedData = await fs.readFile(filePath);
						const decompressedData = await gunzip(compressedData);
						const archiveData = JSON.parse(decompressedData.toString());
						metadata = archiveData.metadata;
					}
					else
					{
						const fileData = await fs.readFile(filePath, 'utf-8');
						const archiveData = JSON.parse(fileData);
						metadata = archiveData.metadata;
					}

					// Parse dates
					metadata.createdAt = new Date(metadata.createdAt);
					metadata.timeRange.start = new Date(metadata.timeRange.start);
					metadata.timeRange.end = new Date(metadata.timeRange.end);
					metadata.fileSizeBytes = stats.size;

					archives.push(metadata);
				}
				catch (error)
				{
					this.logger.warn('Failed to read archive metadata', {
						file,
						error: error.message,
					});
				}
			}

			return archives.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
		}
		catch (error)
		{
			this.logger.error('Failed to list archives', { error: error.message });
			return [];
		}
	}

	/**
	 * Delete old archives based on retention policy
	 */
	async cleanupOldArchives(): Promise<number>
	{
		const archives = await this.listArchives();
		const now = new Date();
		let deletedCount = 0;

		for (const archive of archives)
		{
			const policy = this.retentionPolicies.get(archive.policy) || this.defaultPolicy;
			const archiveAge = now.getTime() - archive.createdAt.getTime();
			const maxAge = policy.retentionDays * 24 * 60 * 60 * 1000;

			if (archiveAge > maxAge)
			{
				try
				{
					const archiveFile = path.join(this.archiveDirectory, `${archive.id}.json.gz`);
					const uncompressedFile = path.join(this.archiveDirectory, `${archive.id}.json`);

					if (await this.fileExists(archiveFile))
					{
						await fs.unlink(archiveFile);
					}
					else if (await this.fileExists(uncompressedFile))
					{
						await fs.unlink(uncompressedFile);
					}

					deletedCount++;
					this.logger.info('Deleted old archive', {
						archiveId: archive.id,
						ageDays: Math.round(archiveAge / (24 * 60 * 60 * 1000)),
						policy: archive.policy,
					});
				}
				catch (error)
				{
					this.logger.error('Failed to delete old archive', {
						archiveId: archive.id,
						error: error.message,
					});
				}
			}
		}

		return deletedCount;
	}

	/**
	 * Get retention statistics
	 */
	async getRetentionStats(): Promise<RetentionStats>
	{
		// This would be called as part of applyRetentionPolicies
		// For now, return current state
		const archives = await this.listArchives();
		const archivesSizeBytes = archives.reduce((sum, a) => sum + a.fileSizeBytes, 0);

		// Get current entry counts from Redis
		const logKeys = await this.redis.keys('logs:*');
		const auditKeys = await this.redis.keys('audit:*');

		let totalActiveEntries = 0;
		for (const key of [...logKeys, ...auditKeys])
		{
			const count = await this.redis.llen(key);
			totalActiveEntries += count;
		}

		const totalArchivedEntries = archives.reduce((sum, a) => sum + a.entryCount, 0);

		const { oldest, newest } = await this.findEntryDateRange();

		return {
			totalEntries: totalActiveEntries + totalArchivedEntries,
			archivedEntries: totalArchivedEntries,
			deletedEntries: 0, // Would need to track this separately
			activeEntries: totalActiveEntries,
			archivesSizeBytes,
			oldestEntry: oldest,
			newestEntry: newest,
			retentionPolicies: this.getRetentionPolicies().map(p => ({
				name: p.name,
				appliedEntries: 0, // Would need to track this
				archivedEntries: 0,
				deletedEntries: 0,
			})),
		};
	}

	/**
	 * Initialize default retention policies
	 */
	private initializeDefaultPolicies(): void
	{
		// Default policy
		this.retentionPolicies.set('default', this.defaultPolicy);

		// Error logs - keep longer
		this.addRetentionPolicy({
			name: 'error-logs',
			description: 'Extended retention for error logs',
			retentionDays: 90,
			archiveAfterDays: 14,
			compressionEnabled: true,
			logLevels: ['error'],
		});

		// Audit events - keep longer
		this.addRetentionPolicy({
			name: 'audit-events',
			description: 'Extended retention for audit events',
			retentionDays: 180,
			archiveAfterDays: 30,
			compressionEnabled: true,
			auditTypes: ['domain_discovery', 'processing_decision'],
		});

		// Debug logs - shorter retention
		this.addRetentionPolicy({
			name: 'debug-logs',
			description: 'Short retention for debug logs',
			retentionDays: 7,
			archiveAfterDays: 1,
			compressionEnabled: true,
			logLevels: ['debug'],
		});
	}

	/**
	 * Apply a specific retention policy
	 */
	private async applyPolicy(policy: RetentionPolicy): Promise<{
		totalEntries: number;
		archivedEntries: number;
		deletedEntries: number;
	}>
	{
		const now = new Date();
		const archiveCutoff = new Date(now.getTime() - policy.archiveAfterDays * 24 * 60 * 60 * 1000);
		const deleteCutoff = new Date(now.getTime() - policy.retentionDays * 24 * 60 * 60 * 1000);

		let totalEntries = 0;
		let archivedEntries = 0;
		let deletedEntries = 0;

		// Process logs
		const logKeys = await this.redis.keys('logs:*');
		for (const key of logKeys)
		{
			const keyTime = this.extractTimeFromKey(key);
			const entries = await this.redis.lrange(key, 0, -1);

			for (const entryJson of entries)
			{
				try
				{
					const entry: LogEntry = JSON.parse(entryJson);

					// Check if entry matches policy criteria
					if (!this.entryMatchesPolicy(entry, null, policy))
					{
						continue;
					}

					totalEntries++;

					// Archive if older than archive cutoff
					if (entry.timestamp < archiveCutoff && entry.timestamp >= deleteCutoff)
					{
						// Entry would be archived (handled by archiveTimeRange)
						archivedEntries++;
					}
					// Delete if older than retention period
					else if (entry.timestamp < deleteCutoff)
					{
						await this.redis.lrem(key, 1, entryJson);
						deletedEntries++;
					}
				}
				catch (error)
				{
					this.logger.warn('Failed to process log entry for retention', {
						key,
						error: error.message,
					});
				}
			}
		}

		// Process audit events
		const auditKeys = await this.redis.keys('audit:*');
		for (const key of auditKeys)
		{
			const entries = await this.redis.lrange(key, 0, -1);

			for (const entryJson of entries)
			{
				try
				{
					const entry: AuditEvent = JSON.parse(entryJson);

					// Check if entry matches policy criteria
					if (!this.entryMatchesPolicy(null, entry, policy))
					{
						continue;
					}

					totalEntries++;

					// Archive if older than archive cutoff
					if (entry.timestamp < archiveCutoff && entry.timestamp >= deleteCutoff)
					{
						archivedEntries++;
					}
					// Delete if older than retention period
					else if (entry.timestamp < deleteCutoff)
					{
						await this.redis.lrem(key, 1, entryJson);
						deletedEntries++;
					}
				}
				catch (error)
				{
					this.logger.warn('Failed to process audit entry for retention', {
						key,
						error: error.message,
					});
				}
			}
		}

		return { totalEntries, archivedEntries, deletedEntries };
	}

	/**
	 * Check if entry matches policy criteria
	 */
	private entryMatchesPolicy(
		logEntry: LogEntry | null,
		auditEvent: AuditEvent | null,
		policy: RetentionPolicy,
	): boolean
	{
		if (logEntry)
		{
			if (policy.logLevels && !policy.logLevels.includes(logEntry.level))
			{
				return false;
			}

			if (policy.components && !policy.components.includes(logEntry.component))
			{
				return false;
			}
		}

		if (auditEvent)
		{
			if (policy.auditTypes && !policy.auditTypes.includes(auditEvent.type))
			{
				return false;
			}

			if (policy.components && !policy.components.includes(auditEvent.component))
			{
				return false;
			}
		}

		return true;
	}

	/**
	 * Collect logs in time range matching policy
	 */
	private async collectLogsInTimeRange(
		startDate: Date,
		endDate: Date,
		policy: RetentionPolicy,
	): Promise<LogEntry[]>
	{
		const logs: LogEntry[] = [];
		const logKeys = await this.redis.keys('logs:*');

		for (const key of logKeys)
		{
			const entries = await this.redis.lrange(key, 0, -1);

			for (const entryJson of entries)
			{
				try
				{
					const entry: LogEntry = JSON.parse(entryJson);
					entry.timestamp = new Date(entry.timestamp);

					if (entry.timestamp >= startDate &&
						entry.timestamp <= endDate &&
						this.entryMatchesPolicy(entry, null, policy))
					{
						logs.push(entry);
					}
				}
				catch (error)
				{
					this.logger.warn('Failed to parse log entry during collection', {
						key,
						error: error.message,
					});
				}
			}
		}

		return logs.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
	}

	/**
	 * Collect audit events in time range matching policy
	 */
	private async collectAuditEventsInTimeRange(
		startDate: Date,
		endDate: Date,
		policy: RetentionPolicy,
	): Promise<AuditEvent[]>
	{
		const auditEvents: AuditEvent[] = [];
		const auditKeys = await this.redis.keys('audit:*');

		for (const key of auditKeys)
		{
			const entries = await this.redis.lrange(key, 0, -1);

			for (const entryJson of entries)
			{
				try
				{
					const entry: AuditEvent = JSON.parse(entryJson);
					entry.timestamp = new Date(entry.timestamp);

					if (entry.timestamp >= startDate &&
						entry.timestamp <= endDate &&
						this.entryMatchesPolicy(null, entry, policy))
					{
						auditEvents.push(entry);
					}
				}
				catch (error)
				{
					this.logger.warn('Failed to parse audit entry during collection', {
						key,
						error: error.message,
					});
				}
			}
		}

		return auditEvents.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
	}

	/**
	 * Write archive file to disk
	 */
	private async writeArchiveFile(
		archiveId: string,
		archiveData: any,
		compress: boolean,
	): Promise<ArchiveMetadata>
	{
		const jsonData = JSON.stringify(archiveData, null, 2);
		const fileName = compress ? `${archiveId}.json.gz` : `${archiveId}.json`;
		const filePath = path.join(this.archiveDirectory, fileName);

		let fileData: Buffer;
		if (compress)
		{
			fileData = await gzip(Buffer.from(jsonData));
		}
		else
		{
			fileData = Buffer.from(jsonData);
		}

		await fs.writeFile(filePath, fileData);

		// Calculate MD5 checksum
		const crypto = await import('node:crypto');
		const checksumMD5 = crypto.createHash('md5').update(fileData).digest('hex');

		return {
			...archiveData.metadata,
			fileSizeBytes: fileData.length,
			checksumMD5,
		};
	}

	/**
	 * Remove archived entries from Redis
	 */
	private async removeArchivedEntries(logs: LogEntry[], auditEvents: AuditEvent[]): Promise<void>
	{
		// Remove logs
		for (const log of logs)
		{
			const key = this.getTimeBasedKey('logs', log.timestamp);
			await this.redis.lrem(key, 1, JSON.stringify(log));
		}

		// Remove audit events
		for (const event of auditEvents)
		{
			const key = this.getTimeBasedKey('audit', event.timestamp);
			await this.redis.lrem(key, 1, JSON.stringify(event));
		}
	}

	/**
	 * Generate archive ID
	 */
	private generateArchiveId(startDate: Date, endDate: Date, policyName: string): string
	{
		const startStr = startDate.toISOString().split('T')[0];
		const endStr = endDate.toISOString().split('T')[0];
		const timestamp = Date.now();
		return `archive-${policyName}-${startStr}-to-${endStr}-${timestamp}`;
	}

	/**
	 * Generate time-based Redis key
	 */
	private getTimeBasedKey(prefix: string, timestamp: Date): string
	{
		const year = timestamp.getFullYear();
		const month = String(timestamp.getMonth() + 1).padStart(2, '0');
		const day = String(timestamp.getDate()).padStart(2, '0');
		const hour = String(timestamp.getHours()).padStart(2, '0');

		return `${prefix}:${year}-${month}-${day}-${hour}`;
	}

	/**
	 * Extract timestamp from Redis key
	 */
	private extractTimeFromKey(key: string): Date
	{
		const parts = key.split(':');
		const timePart = parts[1]; // Format: YYYY-MM-DD-HH
		const [year, month, day, hour] = timePart.split('-').map(Number);
		return new Date(year, month - 1, day, hour);
	}

	/**
	 * Calculate total archive size
	 */
	private async calculateArchiveSize(): Promise<number>
	{
		try
		{
			const files = await fs.readdir(this.archiveDirectory);
			let totalSize = 0;

			for (const file of files)
			{
				if (file.endsWith('.json') || file.endsWith('.json.gz'))
				{
					const filePath = path.join(this.archiveDirectory, file);
					const stats = await fs.stat(filePath);
					totalSize += stats.size;
				}
			}

			return totalSize;
		}
		catch (error)
		{
			this.logger.error('Failed to calculate archive size', { error: error.message });
			return 0;
		}
	}

	/**
	 * Find oldest and newest entry dates
	 */
	private async findEntryDateRange(): Promise<{ oldest: Date; newest: Date; }>
	{
		let oldest = new Date();
		let newest = new Date(0);

		// Check Redis keys
		const allKeys = await this.redis.keys('logs:*');
		allKeys.push(...await this.redis.keys('audit:*'));

		for (const key of allKeys)
		{
			const keyTime = this.extractTimeFromKey(key);
			if (keyTime < oldest) oldest = keyTime;
			if (keyTime > newest) newest = keyTime;
		}

		// Check archives
		const archives = await this.listArchives();
		for (const archive of archives)
		{
			if (archive.timeRange.start < oldest) oldest = archive.timeRange.start;
			if (archive.timeRange.end > newest) newest = archive.timeRange.end;
		}

		return { oldest, newest };
	}

	/**
	 * Check if file exists
	 */
	private async fileExists(filePath: string): Promise<boolean>
	{
		try
		{
			await fs.access(filePath);
			return true;
		}
		catch
		{
			return false;
		}
	}

	/**
	 * Ensure archive directory exists
	 */
	private async ensureArchiveDirectory(): Promise<void>
	{
		try
		{
			await fs.mkdir(this.archiveDirectory, { recursive: true });
		}
		catch (error)
		{
			this.logger.error('Failed to create archive directory', {
				directory: this.archiveDirectory,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Start retention scheduler
	 */
	private startRetentionScheduler(): void
	{
		// Run retention policies daily at 2 AM
		const runRetention = async () =>
		{
			try
			{
				this.logger.info('Starting scheduled retention policy application');
				const stats = await this.applyRetentionPolicies();

				// Also cleanup old archives
				const deletedArchives = await this.cleanupOldArchives();

				this.logger.info('Scheduled retention completed', {
					...stats,
					deletedArchives,
				});
			}
			catch (error)
			{
				this.logger.error('Scheduled retention failed', { error: error.message });
			}
		};

		// Calculate time until next 2 AM
		const now = new Date();
		const next2AM = new Date();
		next2AM.setHours(2, 0, 0, 0);
		if (next2AM <= now)
		{
			next2AM.setDate(next2AM.getDate() + 1);
		}

		const timeUntilNext2AM = next2AM.getTime() - now.getTime();

		// Schedule first run
		setTimeout(() =>
		{
			runRetention();
			// Then run daily
			setInterval(runRetention, 24 * 60 * 60 * 1000);
		}, timeUntilNext2AM);

		this.logger.info('Retention scheduler started', {
			nextRun: next2AM.toISOString(),
		});
	}
}

export type {
	RetentionPolicy,
	ArchiveMetadata,
	RetentionStats,
};

export {
	LogRetentionManager,
};

export default LogRetentionManager;
