import type { Logger, RedisClientWrapper } from '@shared';
import type { AuditEvent, LogContext } from './StructuredLogger';
// import type { DiscoveredDomain } from '../interfaces/DiscoveryEngine';

type LogQuery =
{
	timeRange?:
	{
		start: Date;
		end: Date;
	};
	components?: string[];
	operations?: string[];
	correlationIds?: string[];
	logLevels?: ('debug' | 'info' | 'warn' | 'error')[];
	searchText?: string;
	limit?: number;
	offset?: number;
};

type LogEntry =
{
	id: string;
	timestamp: Date;
	level: string;
	message: string;
	correlationId: string;
	operation: string;
	component: string;
	service: string;
	metadata: Record<string, any>;
	error?:
	{
		name: string;
		message: string;
		stack?: string;
	};
};

type AggregatedMetrics =
{
	timeRange:
	{
		start: Date;
		end: Date;
	};
	totalEvents: number;
	eventsByType: Record<string, number>;
	eventsByComponent: Record<string, number>;
	eventsByOutcome: Record<string, number>;
	averageProcessingTime: number;
	errorRate: number;
	discoveryMetrics:
	{
		totalDomains: number;
		uniqueSources: number;
		averageConfidence: number;
		topStrategies: Array<{ strategy: string; count: number; }>;
	};
	performanceMetrics:
	{
		averageMemoryUsageMB: number;
		peakMemoryUsageMB: number;
		slowestOperations: Array<
		{
			operation: string;
			component: string;
			duration: number;
			timestamp: Date;
		}>;
	};
};

type LogAnalysis =
{
	summary:
	{
		totalLogs: number;
		timeSpan: number; // milliseconds
		errorCount: number;
		warningCount: number;
		uniqueCorrelationIds: number;
		uniqueComponents: number;
	};
	trends:
	{
		logVolumeOverTime: Array<{ timestamp: Date; count: number; }>;
		errorRateOverTime: Array<{ timestamp: Date; rate: number; }>;
		componentActivity: Array<{ component: string; count: number; }>;
		operationFrequency: Array<{ operation: string; count: number; }>;
	};
	anomalies: Array<
	{
		type: 'error_spike' | 'performance_degradation' | 'unusual_pattern';
		description: string;
		timestamp: Date;
		severity: 'low' | 'medium' | 'high';
		affectedComponents: string[];
		suggestedActions: string[];
	}>;
	insights: Array<
	{
		category: 'performance' | 'reliability' | 'discovery' | 'system';
		insight: string;
		confidence: number;
		supportingData: Record<string, any>;
	}>;
};

class LogAggregator
{
	private logger: Logger;

	private redis: RedisClientWrapper;

	private logBuffer: Map<string, LogEntry>;

	private auditBuffer: Map<string, AuditEvent>;

	private bufferSize: number;

	private flushInterval: number;

	private retentionDays: number;

	constructor(
		logger: Logger,
		redis: RedisClientWrapper,
		bufferSize = 1000,
		flushInterval = 30000, // 30 seconds
		retentionDays = 30,
	)
	{
		this.logger = logger;
		this.redis = redis;
		this.logBuffer = new Map();
		this.auditBuffer = new Map();
		this.bufferSize = bufferSize;
		this.flushInterval = flushInterval;
		this.retentionDays = retentionDays;

		this.startPeriodicFlush();
		this.startRetentionCleanup();
	}

	/**
	 * Add log entry to aggregation buffer
	 */
	addLogEntry(entry: LogEntry): void
	{
		this.logBuffer.set(entry.id, entry);

		if (this.logBuffer.size >= this.bufferSize)
		{
			this.flushLogs().catch((error) =>
			{
				this.logger.error('Failed to flush logs', { error: error.message });
			});
		}
	}

	/**
	 * Add audit event to aggregation buffer
	 */
	addAuditEvent(event: AuditEvent): void
	{
		this.auditBuffer.set(event.id, event);

		if (this.auditBuffer.size >= this.bufferSize)
		{
			this.flushAuditEvents().catch((error) =>
			{
				this.logger.error('Failed to flush audit events', { error: error.message });
			});
		}
	}

	/**
	 * Query logs with filtering and pagination
	 */
	async queryLogs(query: LogQuery): Promise<LogEntry[]>
	{
		const pipeline = this.redis.pipeline();
		const keys: string[] = [];

		// Build Redis keys based on query parameters
		if (query.timeRange)
		{
			const startKey = this.getTimeBasedKey('logs', query.timeRange.start);
			const endKey = this.getTimeBasedKey('logs', query.timeRange.end);

			// Get all keys in time range
			const timeKeys = await this.redis.keys(`logs:${startKey.split(':')[1]}*`);
			keys.push(...timeKeys.filter((key) =>
			{
				const keyTime = this.extractTimeFromKey(key);
				return keyTime >= query.timeRange!.start && keyTime <= query.timeRange!.end;
			}));
		}
		else
		{
			// Get recent logs if no time range specified
			const recentKeys = await this.redis.keys('logs:*');
			keys.push(...recentKeys.slice(-100)); // Last 100 time buckets
		}

		// Fetch log entries
		const logEntries: LogEntry[] = [];
		for (const key of keys)
		{
			const entries = await this.redis.lrange(key, 0, -1);
			for (const entryJson of entries)
			{
				try
				{
					const entry: LogEntry = JSON.parse(entryJson);
					if (this.matchesQuery(entry, query))
					{
						logEntries.push(entry);
					}
				}
				catch (error)
				{
					this.logger.warn('Failed to parse log entry', { key, error: error.message });
				}
			}
		}

		// Sort by timestamp and apply pagination
		logEntries.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

		const offset = query.offset || 0;
		const limit = query.limit || 100;

		return logEntries.slice(offset, offset + limit);
	}

	/**
	 * Query audit events with filtering
	 */
	async queryAuditEvents(query: Partial<AuditEvent> & { limit?: number; offset?: number; }): Promise<AuditEvent[]>
	{
		const keys = await this.redis.keys('audit:*');
		const auditEvents: AuditEvent[] = [];

		for (const key of keys)
		{
			const events = await this.redis.lrange(key, 0, -1);
			for (const eventJson of events)
			{
				try
				{
					const event: AuditEvent = JSON.parse(eventJson);
					if (this.matchesAuditQuery(event, query))
					{
						auditEvents.push(event);
					}
				}
				catch (error)
				{
					this.logger.warn('Failed to parse audit event', { key, error: error.message });
				}
			}
		}

		// Sort by timestamp and apply pagination
		auditEvents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

		const offset = query.offset || 0;
		const limit = query.limit || 100;

		return auditEvents.slice(offset, offset + limit);
	}

	/**
	 * Generate aggregated metrics for a time period
	 */
	async generateMetrics(
		startTime: Date,
		endTime: Date,
		components?: string[],
	): Promise<AggregatedMetrics>
	{
		const auditEvents = await this.queryAuditEvents({
			limit: 10000, // Large limit to get comprehensive data
		});

		const filteredEvents = auditEvents.filter((event) =>
		{
			const inTimeRange = event.timestamp >= startTime && event.timestamp <= endTime;
			const inComponents = !components || components.includes(event.component);
			return inTimeRange && inComponents;
		});

		const totalEvents = filteredEvents.length;
		const eventsByType = this.groupBy(filteredEvents, 'type');
		const eventsByComponent = this.groupBy(filteredEvents, 'component');
		const eventsByOutcome = this.groupBy(filteredEvents, 'outcome');

		const durationsMs = filteredEvents
			.filter(e => e.duration)
			.map(e => e.duration!);
		const averageProcessingTime = durationsMs.length > 0
			? durationsMs.reduce((sum, d) => sum + d, 0) / durationsMs.length
			: 0;

		const errorEvents = filteredEvents.filter(e => e.outcome === 'failure');
		const errorRate = totalEvents > 0 ? errorEvents.length / totalEvents : 0;

		// Discovery metrics
		const discoveryEvents = filteredEvents.filter(e => e.type === 'domain_discovery');
		const totalDomains = discoveryEvents.reduce((sum, e) => sum + (e.details.domainsCount || 0), 0);

		const uniqueSources = new Set(discoveryEvents.map(e => e.details.source)).size;

		const confidenceValues = discoveryEvents.flatMap(e => e.details.domains?.map((d: any) => d.confidence) || []);
		const averageConfidence = confidenceValues.length > 0
			? confidenceValues.reduce((sum, c) => sum + c, 0) / confidenceValues.length
			: 0;

		const strategyCount = this.groupBy(
			discoveryEvents.flatMap(e => e.details.domains || []),
			'strategy',
		);
		const topStrategies = Object.entries(strategyCount)
			.map(([strategy, count]) => ({ strategy, count }))
			.sort((a, b) => b.count - a.count)
			.slice(0, 5);

		// Performance metrics
		const memoryUsages = filteredEvents
			.filter(e => e.resourceUsage?.memoryMB)
			.map(e => e.resourceUsage!.memoryMB);

		const averageMemoryUsageMB = memoryUsages.length > 0
			? memoryUsages.reduce((sum, m) => sum + m, 0) / memoryUsages.length
			: 0;

		const peakMemoryUsageMB = memoryUsages.length > 0
			? Math.max(...memoryUsages)
			: 0;

		const slowestOperations = filteredEvents
			.filter(e => e.duration)
			.sort((a, b) => (b.duration || 0) - (a.duration || 0))
			.slice(0, 10)
			.map(e => ({
				operation: e.operation,
				component: e.component,
				duration: e.duration!,
				timestamp: e.timestamp,
			}));

		return {
			timeRange: { start: startTime, end: endTime },
			totalEvents,
			eventsByType,
			eventsByComponent,
			eventsByOutcome,
			averageProcessingTime,
			errorRate,
			discoveryMetrics: {
				totalDomains,
				uniqueSources,
				averageConfidence,
				topStrategies,
			},
			performanceMetrics: {
				averageMemoryUsageMB,
				peakMemoryUsageMB,
				slowestOperations,
			},
		};
	}

	/**
	 * Analyze logs for patterns, anomalies, and insights
	 */
	async analyzeLogs(
		startTime: Date,
		endTime: Date,
		components?: string[],
	): Promise<LogAnalysis>
	{
		const logs = await this.queryLogs({
			timeRange: { start: startTime, end: endTime },
			components,
			limit: 10000,
		});

		const auditEvents = await this.queryAuditEvents({
			limit: 10000,
		});

		const filteredAuditEvents = auditEvents.filter((event) =>
		{
			const inTimeRange = event.timestamp >= startTime && event.timestamp <= endTime;
			const inComponents = !components || components.includes(event.component);
			return inTimeRange && inComponents;
		});

		// Generate summary
		const errorLogs = logs.filter(l => l.level === 'error');
		const warningLogs = logs.filter(l => l.level === 'warn');
		const uniqueCorrelationIds = new Set(logs.map(l => l.correlationId)).size;
		const uniqueComponents = new Set(logs.map(l => l.component)).size;

		const summary = {
			totalLogs: logs.length,
			timeSpan: endTime.getTime() - startTime.getTime(),
			errorCount: errorLogs.length,
			warningCount: warningLogs.length,
			uniqueCorrelationIds,
			uniqueComponents,
		};

		// Generate trends
		const trends = this.generateTrends(logs, filteredAuditEvents);

		// Detect anomalies
		const anomalies = this.detectAnomalies(logs, filteredAuditEvents);

		// Generate insights
		const insights = this.generateInsights(logs, filteredAuditEvents);

		return {
			summary,
			trends,
			anomalies,
			insights,
		};
	}

	/**
	 * Get correlation trail for a specific correlation ID
	 */
	async getCorrelationTrail(correlationId: string): Promise<{
		logs: LogEntry[];
		auditEvents: AuditEvent[];
		timeline: Array<{
			timestamp: Date;
			type: 'log' | 'audit';
			event: LogEntry | AuditEvent;
		}>;
	}>
	{
		const logs = await this.queryLogs({ correlationIds: [correlationId], limit: 1000 });
		const auditEvents = await this.queryAuditEvents({ correlationId, limit: 1000 });

		// Create combined timeline
		const timeline = [
			...logs.map(log => ({ timestamp: log.timestamp, type: 'log' as const, event: log })),
			...auditEvents.map(event => ({ timestamp: event.timestamp, type: 'audit' as const, event })),
		].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

		return {
			logs,
			auditEvents,
			timeline,
		};
	}

	/**
	 * Flush log buffer to Redis
	 */
	private async flushLogs(): Promise<void>
	{
		if (this.logBuffer.size === 0) return;

		const pipeline = this.redis.pipeline();
		const logsByTime = new Map<string, LogEntry[]>();

		// Group logs by time bucket (hourly)
		for (const log of this.logBuffer.values())
		{
			const timeKey = this.getTimeBasedKey('logs', log.timestamp);
			if (!logsByTime.has(timeKey))
			{
				logsByTime.set(timeKey, []);
			}
			logsByTime.get(timeKey)!.push(log);
		}

		// Store logs in Redis lists by time bucket
		for (const [timeKey, logs] of logsByTime)
		{
			for (const log of logs)
			{
				pipeline.lpush(timeKey, JSON.stringify(log));
			}
			// Set expiration for log retention
			pipeline.expire(timeKey, this.retentionDays * 24 * 60 * 60);
		}

		await pipeline.exec();
		this.logBuffer.clear();
	}

	/**
	 * Flush audit events buffer to Redis
	 */
	private async flushAuditEvents(): Promise<void>
	{
		if (this.auditBuffer.size === 0) return;

		const pipeline = this.redis.pipeline();
		const eventsByTime = new Map<string, AuditEvent[]>();

		// Group events by time bucket (hourly)
		for (const event of this.auditBuffer.values())
		{
			const timeKey = this.getTimeBasedKey('audit', event.timestamp);
			if (!eventsByTime.has(timeKey))
			{
				eventsByTime.set(timeKey, []);
			}
			eventsByTime.get(timeKey)!.push(event);
		}

		// Store events in Redis lists by time bucket
		for (const [timeKey, events] of eventsByTime)
		{
			for (const event of events)
			{
				pipeline.lpush(timeKey, JSON.stringify(event));
			}
			// Set expiration for audit retention
			pipeline.expire(timeKey, this.retentionDays * 24 * 60 * 60);
		}

		await pipeline.exec();
		this.auditBuffer.clear();
	}

	/**
	 * Generate time-based Redis key
	 */
	private getTimeBasedKey(prefix: string, timestamp: Date): string
	{
		const year = timestamp.getFullYear();
		const month = String(timestamp.getMonth() + 1).padStart(2, '0');
		const day = String(timestamp.getDate()).padStart(2, '0');
		const hour = String(timestamp.getHours()).padStart(2, '0');

		return `${prefix}:${year}-${month}-${day}-${hour}`;
	}

	/**
	 * Extract timestamp from Redis key
	 */
	private extractTimeFromKey(key: string): Date
	{
		const parts = key.split(':');
		const timePart = parts[1]; // Format: YYYY-MM-DD-HH
		const [year, month, day, hour] = timePart.split('-').map(Number);
		return new Date(year, month - 1, day, hour);
	}

	/**
	 * Check if log entry matches query
	 */
	private matchesQuery(entry: LogEntry, query: LogQuery): boolean
	{
		if (query.components && !query.components.includes(entry.component))
		{
			return false;
		}

		if (query.operations && !query.operations.includes(entry.operation))
		{
			return false;
		}

		if (query.correlationIds && !query.correlationIds.includes(entry.correlationId))
		{
			return false;
		}

		if (query.logLevels && !query.logLevels.includes(entry.level as any))
		{
			return false;
		}

		if (query.searchText)
		{
			const searchLower = query.searchText.toLowerCase();
			const messageMatch = entry.message.toLowerCase().includes(searchLower);
			const metadataMatch = JSON.stringify(entry.metadata).toLowerCase().includes(searchLower);
			if (!messageMatch && !metadataMatch)
			{
				return false;
			}
		}

		return true;
	}

	/**
	 * Check if audit event matches query
	 */
	private matchesAuditQuery(event: AuditEvent, query: Partial<AuditEvent>): boolean
	{
		if (query.type && event.type !== query.type)
		{
			return false;
		}

		if (query.component && event.component !== query.component)
		{
			return false;
		}

		if (query.operation && event.operation !== query.operation)
		{
			return false;
		}

		if (query.outcome && event.outcome !== query.outcome)
		{
			return false;
		}

		if (query.correlationId && event.correlationId !== query.correlationId)
		{
			return false;
		}

		return true;
	}

	/**
	 * Group array by property
	 */
	private groupBy<T>(array: T[], property: keyof T): Record<string, number>
	{
		return array.reduce((acc, item) =>
		{
			const key = String(item[property]);
			acc[key] = (acc[key] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);
	}

	/**
	 * Generate trend analysis
	 */
	private generateTrends(logs: LogEntry[], auditEvents: AuditEvent[]): LogAnalysis['trends']
	{
		// Log volume over time (hourly buckets)
		const logVolumeOverTime = this.createTimeBuckets(logs, 'hour')
			.map(bucket => ({
				timestamp: bucket.timestamp,
				count: bucket.items.length,
			}));

		// Error rate over time
		const errorRateOverTime = this.createTimeBuckets(logs, 'hour')
			.map((bucket) =>
			{
				const errorCount = bucket.items.filter(l => l.level === 'error').length;
				const rate = bucket.items.length > 0 ? errorCount / bucket.items.length : 0;
				return {
					timestamp: bucket.timestamp,
					rate,
				};
			});

		// Component activity
		const componentActivity = Object.entries(this.groupBy(logs, 'component'))
			.map(([component, count]) => ({ component, count }))
			.sort((a, b) => b.count - a.count);

		// Operation frequency
		const operationFrequency = Object.entries(this.groupBy(auditEvents, 'operation'))
			.map(([operation, count]) => ({ operation, count }))
			.sort((a, b) => b.count - a.count);

		return {
			logVolumeOverTime,
			errorRateOverTime,
			componentActivity,
			operationFrequency,
		};
	}

	/**
	 * Detect anomalies in logs and audit events
	 */
	private detectAnomalies(logs: LogEntry[], auditEvents: AuditEvent[]): LogAnalysis['anomalies']
	{
		const anomalies: LogAnalysis['anomalies'] = [];

		// Error spike detection
		const errorBuckets = this.createTimeBuckets(logs.filter(l => l.level === 'error'), 'hour');
		const avgErrorsPerHour = errorBuckets.reduce((sum, b) => sum + b.items.length, 0) / errorBuckets.length;

		for (const bucket of errorBuckets)
		{
			if (bucket.items.length > avgErrorsPerHour * 3) // 3x average
			{
				const affectedComponents = [...new Set(bucket.items.map(l => l.component))];
				anomalies.push({
					type: 'error_spike',
					description: `Error spike detected: ${bucket.items.length} errors in 1 hour (avg: ${Math.round(avgErrorsPerHour)})`,
					timestamp: bucket.timestamp,
					severity: bucket.items.length > avgErrorsPerHour * 5 ? 'high' : 'medium',
					affectedComponents,
					suggestedActions: [
						'Check system resources',
						'Review recent deployments',
						'Investigate affected components',
					],
				});
			}
		}

		// Performance degradation detection
		const performanceEvents = auditEvents.filter(e => e.duration && e.duration > 0);
		if (performanceEvents.length > 0)
		{
			const avgDuration = performanceEvents.reduce((sum, e) => sum + e.duration!, 0) / performanceEvents.length;
			const slowEvents = performanceEvents.filter(e => e.duration! > avgDuration * 5);

			if (slowEvents.length > 0)
			{
				const affectedComponents = [...new Set(slowEvents.map(e => e.component))];
				anomalies.push({
					type: 'performance_degradation',
					description: `Performance degradation detected: ${slowEvents.length} operations took >5x average time`,
					timestamp: new Date(Math.max(...slowEvents.map(e => e.timestamp.getTime()))),
					severity: slowEvents.length > performanceEvents.length * 0.1 ? 'high' : 'medium',
					affectedComponents,
					suggestedActions: [
						'Check database performance',
						'Review system load',
						'Optimize slow operations',
					],
				});
			}
		}

		return anomalies;
	}

	/**
	 * Generate insights from log analysis
	 */
	private generateInsights(logs: LogEntry[], auditEvents: AuditEvent[]): LogAnalysis['insights']
	{
		const insights: LogAnalysis['insights'] = [];

		// Discovery performance insight
		const discoveryEvents = auditEvents.filter(e => e.type === 'domain_discovery');
		if (discoveryEvents.length > 0)
		{
			const totalDomains = discoveryEvents.reduce((sum, e) => sum + (e.details.domainsCount || 0), 0);
			const avgDomainsPerRun = totalDomains / discoveryEvents.length;

			insights.push({
				category: 'discovery',
				insight: `Average discovery rate: ${Math.round(avgDomainsPerRun)} domains per run`,
				confidence: 0.9,
				supportingData: {
					totalRuns: discoveryEvents.length,
					totalDomains,
					avgDomainsPerRun,
				},
			});
		}

		// Error pattern insight
		const errorLogs = logs.filter(l => l.level === 'error');
		if (errorLogs.length > 0)
		{
			const errorsByComponent = this.groupBy(errorLogs, 'component');
			const topErrorComponent = Object.entries(errorsByComponent)
				.sort(([, a], [, b]) => b - a)[0];

			if (topErrorComponent)
			{
				insights.push({
					category: 'reliability',
					insight: `Component "${topErrorComponent[0]}" accounts for ${Math.round((topErrorComponent[1] / errorLogs.length) * 100)}% of errors`,
					confidence: 0.8,
					supportingData: {
						component: topErrorComponent[0],
						errorCount: topErrorComponent[1],
						totalErrors: errorLogs.length,
						percentage: (topErrorComponent[1] / errorLogs.length) * 100,
					},
				});
			}
		}

		// Performance insight
		const performanceEvents = auditEvents.filter(e => e.duration);
		if (performanceEvents.length > 0)
		{
			const avgDuration = performanceEvents.reduce((sum, e) => sum + e.duration!, 0) / performanceEvents.length;
			const operationsByDuration = this.groupBy(
				performanceEvents.filter(e => e.duration! > avgDuration * 2),
				'operation',
			);

			const slowestOperation = Object.entries(operationsByDuration)
				.sort(([, a], [, b]) => b - a)[0];

			if (slowestOperation)
			{
				insights.push({
					category: 'performance',
					insight: `Operation "${slowestOperation[0]}" frequently exceeds average processing time`,
					confidence: 0.7,
					supportingData: {
						operation: slowestOperation[0],
						slowInstanceCount: slowestOperation[1],
						avgDuration: Math.round(avgDuration),
					},
				});
			}
		}

		return insights;
	}

	/**
	 * Create time buckets for trend analysis
	 */
	private createTimeBuckets<T extends { timestamp: Date }>(
		items: T[],
		bucketSize: 'hour' | 'day',
	): Array<{ timestamp: Date; items: T[]; }>
	{
		const buckets = new Map<string, T[]>();

		for (const item of items)
		{
			const bucketKey = bucketSize === 'hour'
				? `${item.timestamp.getFullYear()}-${item.timestamp.getMonth()}-${item.timestamp.getDate()}-${item.timestamp.getHours()}`
				: `${item.timestamp.getFullYear()}-${item.timestamp.getMonth()}-${item.timestamp.getDate()}`;

			if (!buckets.has(bucketKey))
			{
				buckets.set(bucketKey, []);
			}
			buckets.get(bucketKey)!.push(item);
		}

		return Array.from(buckets.entries()).map(([key, items]) =>
		{
			const [year, month, day, hour] = key.split('-').map(Number);
			const timestamp = new Date(year, month, day, hour || 0);
			return { timestamp, items };
		}).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
	}

	/**
	 * Start periodic flush of buffers
	 */
	private startPeriodicFlush(): void
	{
		setInterval(async () =>
		{
			try
			{
				await Promise.all([
					this.flushLogs(),
					this.flushAuditEvents(),
				]);
			}
			catch (error)
			{
				this.logger.error('Failed to flush log buffers', { error: error.message });
			}
		}, this.flushInterval);
	}

	/**
	 * Start periodic cleanup of old data
	 */
	private startRetentionCleanup(): void
	{
		setInterval(async () =>
		{
			try
			{
				const cutoffDate = new Date();
				cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);

				// Clean up old log keys
				const logKeys = await this.redis.keys('logs:*');
				const auditKeys = await this.redis.keys('audit:*');

				const keysToDelete = [...logKeys, ...auditKeys].filter((key) =>
				{
					const keyTime = this.extractTimeFromKey(key);
					return keyTime < cutoffDate;
				});

				if (keysToDelete.length > 0)
				{
					await this.redis.del(...keysToDelete);
					this.logger.info('Cleaned up old log data', {
						deletedKeys: keysToDelete.length,
						cutoffDate: cutoffDate.toISOString(),
					});
				}
			}
			catch (error)
			{
				this.logger.error('Failed to clean up old log data', { error: error.message });
			}
		}, 24 * 60 * 60 * 1000); // Run daily
	}
}

export type {
	LogQuery,
	LogEntry,
	AggregatedMetrics,
	LogAnalysis,
};

export { LogAggregator };

export default LogAggregator;
