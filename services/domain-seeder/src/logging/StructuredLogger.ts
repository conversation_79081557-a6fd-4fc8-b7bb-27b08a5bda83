import type { Logger } from '@shared';
import crypto from 'node:crypto';
import type { DiscoveredDomain } from '../interfaces/DiscoveryEngine';

type LogContext =
{
	correlationId: string;
	operation: string;
	component: string;
	stage?: string;
	timestamp: Date;
	metadata?: Record<string, any>;
};

type AuditEvent =
{
	id: string;
	type: 'domain_discovery' | 'processing_decision' | 'system_event' | 'error_event';
	correlationId: string;
	timestamp: Date;
	component: string;
	operation: string;
	details: Record<string, any>;
	outcome: 'success' | 'failure' | 'partial';
	duration?: number;
	resourceUsage?:
	{
		memoryMB: number;
		cpuPercent?: number;
	};
};

type ProcessingDecision =
{
	domain: string;
	decision: 'enqueue' | 'skip' | 'reject' | 'retry';
	reason: string;
	strategy: string;
	confidence: number;
	metadata: Record<string, any>;
};

type DiscoveryMetrics =
{
	candidatesFetched: number;
	candidatesNormalized: number;
	candidatesFiltered: number;
	domainsDiscovered: number;
	domainsEnqueued: number;
	processingTimeMs: number;
	errorCount: number;
};

class StructuredLogger
{
	private logger: Logger;

	private serviceName: string;

	private correlationIdStack: string[];

	private auditEvents: Map<string, AuditEvent>;

	private maxAuditEvents: number;

	private auditRetentionMs: number;

	constructor(
		logger: Logger,
		serviceName: string,
		maxAuditEvents = 10000,
		auditRetentionMs = 7 * 24 * 60 * 60 * 1000, // 7 days
	)
	{
		this.logger = logger;
		this.serviceName = serviceName;
		this.correlationIdStack = [];
		this.auditEvents = new Map();
		this.maxAuditEvents = maxAuditEvents;
		this.auditRetentionMs = auditRetentionMs;

		this.startAuditCleanup();
	}

	/**
	 * Create a new correlation ID for tracking operations
	 */
	createCorrelationId(): string
	{
		return crypto.randomUUID();
	}

	/**
	 * Push a correlation ID onto the stack for nested operations
	 */
	pushCorrelationId(correlationId: string): void
	{
		this.correlationIdStack.push(correlationId);
	}

	/**
	 * Pop the current correlation ID from the stack
	 */
	popCorrelationId(): string | undefined
	{
		return this.correlationIdStack.pop();
	}

	/**
	 * Get the current correlation ID
	 */
	getCurrentCorrelationId(): string
	{
		return this.correlationIdStack[this.correlationIdStack.length - 1] || this.createCorrelationId();
	}

	/**
	 * Create a log context for structured logging
	 */
	createContext(
		operation: string,
		component: string,
		stage?: string,
		metadata?: Record<string, any>,
	): LogContext
	{
		return {
			correlationId: this.getCurrentCorrelationId(),
			operation,
			component,
			stage,
			timestamp: new Date(),
			metadata,
		};
	}

	/**
	 * Log with structured context
	 */
	info(message: string, context: LogContext): void
	{
		this.logger.info({
			message,
			correlationId: context.correlationId,
			operation: context.operation,
			component: context.component,
			stage: context.stage,
			timestamp: context.timestamp.toISOString(),
			service: this.serviceName,
			...context.metadata,
		});
	}

	/**
	 * Log warning with structured context
	 */
	warn(message: string, context: LogContext): void
	{
		this.logger.warn({
			message,
			correlationId: context.correlationId,
			operation: context.operation,
			component: context.component,
			stage: context.stage,
			timestamp: context.timestamp.toISOString(),
			service: this.serviceName,
			...context.metadata,
		});
	}

	/**
	 * Log error with structured context
	 */
	error(message: string, error: Error, context: LogContext): void
	{
		this.logger.error({
			message,
			error: {
				name: error.name,
				message: error.message,
				stack: error.stack,
			},
			correlationId: context.correlationId,
			operation: context.operation,
			component: context.component,
			stage: context.stage,
			timestamp: context.timestamp.toISOString(),
			service: this.serviceName,
			...context.metadata,
		});
	}

	/**
	 * Log debug information with structured context
	 */
	debug(message: string, context: LogContext): void
	{
		this.logger.debug({
			message,
			correlationId: context.correlationId,
			operation: context.operation,
			component: context.component,
			stage: context.stage,
			timestamp: context.timestamp.toISOString(),
			service: this.serviceName,
			...context.metadata,
		});
	}

	/**
	 * Create and store an audit event
	 */
	async auditEvent(
		type: AuditEvent['type'],
		operation: string,
		component: string,
		details: Record<string, any>,
		outcome: AuditEvent['outcome'],
		duration?: number,
	): Promise<string>
	{
		const eventId = crypto.randomUUID();
		const correlationId = this.getCurrentCorrelationId();

		const auditEvent: AuditEvent = {
			id: eventId,
			type,
			correlationId,
			timestamp: new Date(),
			component,
			operation,
			details,
			outcome,
			duration,
			resourceUsage: this.getResourceUsage(),
		};

		// Store audit event
		this.auditEvents.set(eventId, auditEvent);
		this.trimAuditEvents();

		// Log audit event
		this.logger.info({
			auditEventId: eventId,
			auditType: type,
			correlationId,
			operation,
			component,
			outcome,
			duration: duration ? `${duration}ms` : undefined,
			details,
			resourceUsage: auditEvent.resourceUsage,
			service: this.serviceName,
		}, 'Audit Event');

		return eventId;
	}

	/**
	 * Audit domain discovery events
	 */
	async auditDomainDiscovery(
		domains: DiscoveredDomain[],
		source: string,
		strategy: string,
		processingTimeMs: number,
	): Promise<string>
	{
		const details = {
			source,
			strategy,
			domainsCount: domains.length,
			domains: domains.map(d => ({
				domain: d.domain,
				confidence: d.confidence,
				discoveryReason: d.discoveryReason,
				rank: d.rank,
			})),
			averageConfidence: domains.reduce((sum, d) => sum + d.confidence, 0) / domains.length,
			confidenceDistribution: this.calculateConfidenceDistribution(domains),
		};

		return this.auditEvent(
			'domain_discovery',
			'discover_domains',
			'DiscoveryEngine',
			details,
			'success',
			processingTimeMs,
		);
	}

	/**
	 * Audit processing decisions
	 */
	async auditProcessingDecisions(
		decisions: ProcessingDecision[],
		operation: string,
		component: string,
		processingTimeMs: number,
	): Promise<string>
	{
		const decisionSummary = decisions.reduce((acc, decision) =>
		{
			acc[decision.decision] = (acc[decision.decision] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);

		const details = {
			totalDecisions: decisions.length,
			decisionSummary,
			decisions: decisions.map(d => ({
				domain: d.domain,
				decision: d.decision,
				reason: d.reason,
				strategy: d.strategy,
				confidence: d.confidence,
			})),
			averageConfidence: decisions.reduce((sum, d) => sum + d.confidence, 0) / decisions.length,
		};

		return this.auditEvent(
			'processing_decision',
			operation,
			component,
			details,
			'success',
			processingTimeMs,
		);
	}

	/**
	 * Audit system events (startup, shutdown, configuration changes)
	 */
	async auditSystemEvent(
		event: string,
		component: string,
		details: Record<string, any>,
		outcome: AuditEvent['outcome'] = 'success',
	): Promise<string>
	{
		return this.auditEvent(
			'system_event',
			event,
			component,
			details,
			outcome,
		);
	}

	/**
	 * Audit error events
	 */
	async auditErrorEvent(
		error: Error,
		operation: string,
		component: string,
		details: Record<string, any>,
		duration?: number,
	): Promise<string>
	{
		const errorDetails = {
			...details,
			error: {
				name: error.name,
				message: error.message,
				stack: error.stack,
			},
		};

		return this.auditEvent(
			'error_event',
			operation,
			component,
			errorDetails,
			'failure',
			duration,
		);
	}

	/**
	 * Log discovery metrics
	 */
	logDiscoveryMetrics(
		source: string,
		strategy: string,
		metrics: DiscoveryMetrics,
		context: LogContext,
	): void
	{
		this.logger.info({
			message: 'Discovery metrics',
			source,
			strategy,
			metrics: {
				candidatesFetched: metrics.candidatesFetched,
				candidatesNormalized: metrics.candidatesNormalized,
				candidatesFiltered: metrics.candidatesFiltered,
				domainsDiscovered: metrics.domainsDiscovered,
				domainsEnqueued: metrics.domainsEnqueued,
				processingTimeMs: metrics.processingTimeMs,
				errorCount: metrics.errorCount,
				discoveryRate: metrics.domainsDiscovered / metrics.candidatesFetched,
				enqueueRate: metrics.domainsEnqueued / metrics.domainsDiscovered,
			},
			correlationId: context.correlationId,
			operation: context.operation,
			component: context.component,
			service: this.serviceName,
		});
	}

	/**
	 * Get audit events with optional filtering
	 */
	getAuditEvents(filters?: {
		type?: AuditEvent['type'];
		component?: string;
		operation?: string;
		outcome?: AuditEvent['outcome'];
		since?: Date;
		correlationId?: string;
		limit?: number;
	}): AuditEvent[]
	{
		let events = Array.from(this.auditEvents.values());

		if (filters)
		{
			if (filters.type)
			{
				events = events.filter(e => e.type === filters.type);
			}

			if (filters.component)
			{
				events = events.filter(e => e.component === filters.component);
			}

			if (filters.operation)
			{
				events = events.filter(e => e.operation === filters.operation);
			}

			if (filters.outcome)
			{
				events = events.filter(e => e.outcome === filters.outcome);
			}

			if (filters.since)
			{
				events = events.filter(e => e.timestamp >= filters.since!);
			}

			if (filters.correlationId)
			{
				events = events.filter(e => e.correlationId === filters.correlationId);
			}

			if (filters.limit)
			{
				events = events.slice(0, filters.limit);
			}
		}

		return events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
	}

	/**
	 * Get audit trail for a specific correlation ID
	 */
	getAuditTrail(correlationId: string): AuditEvent[]
	{
		return this.getAuditEvents({ correlationId });
	}

	/**
	 * Execute operation with automatic audit logging
	 */
	async withAudit<T>(
		operation: string,
		component: string,
		fn: (context: LogContext) => Promise<T>,
		auditDetails?: Record<string, any>,
	): Promise<T>
	{
		const correlationId = this.createCorrelationId();
		this.pushCorrelationId(correlationId);

		const context = this.createContext(operation, component);
		const startTime = Date.now();

		try
		{
			this.info(`Starting ${operation}`, context);

			const result = await fn(context);
			const duration = Date.now() - startTime;

			this.info(`Completed ${operation}`, {
				...context,
				metadata: { duration: `${duration}ms` },
			});

			await this.auditEvent(
				'system_event',
				operation,
				component,
				{ ...auditDetails, result: typeof result },
				'success',
				duration,
			);

			return result;
		}
		catch (error)
		{
			const duration = Date.now() - startTime;
			const err = error as Error;

			this.error(`Failed ${operation}`, err, context);

			await this.auditErrorEvent(
				err,
				operation,
				component,
				{ ...auditDetails },
				duration,
			);

			throw error;
		}
		finally
		{
			this.popCorrelationId();
		}
	}

	/**
	 * Get resource usage information
	 */
	private getResourceUsage(): AuditEvent['resourceUsage']
	{
		const memoryUsage = process.memoryUsage();
		return {
			memoryMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
		};
	}

	/**
	 * Calculate confidence distribution for discovered domains
	 */
	private calculateConfidenceDistribution(domains: DiscoveredDomain[]): Record<string, number>
	{
		const distribution = {
			'0.0-0.2': 0,
			'0.2-0.4': 0,
			'0.4-0.6': 0,
			'0.6-0.8': 0,
			'0.8-1.0': 0,
		};

		for (const domain of domains)
		{
			const confidence = domain.confidence;
			if (confidence < 0.2) distribution['0.0-0.2']++;
			else if (confidence < 0.4) distribution['0.2-0.4']++;
			else if (confidence < 0.6) distribution['0.4-0.6']++;
			else if (confidence < 0.8) distribution['0.6-0.8']++;
			else distribution['0.8-1.0']++;
		}

		return distribution;
	}

	/**
	 * Trim audit events to stay within limits
	 */
	private trimAuditEvents(): void
	{
		if (this.auditEvents.size > this.maxAuditEvents)
		{
			const sortedEvents = Array.from(this.auditEvents.entries())
				.sort(([, a], [, b]) => a.timestamp.getTime() - b.timestamp.getTime());

			const toRemove = sortedEvents.slice(0, this.auditEvents.size - this.maxAuditEvents);
			for (const [id] of toRemove)
			{
				this.auditEvents.delete(id);
			}
		}
	}

	/**
	 * Start periodic cleanup of old audit events
	 */
	private startAuditCleanup(): void
	{
		setInterval(() =>
		{
			const now = new Date();
			const cutoff = new Date(now.getTime() - this.auditRetentionMs);

			for (const [id, event] of this.auditEvents.entries())
			{
				if (event.timestamp < cutoff)
				{
					this.auditEvents.delete(id);
				}
			}
		}, 60 * 60 * 1000); // Run cleanup every hour
	}
}

export type {
	LogContext,
	AuditEvent,
	ProcessingDecision,
	DiscoveryMetrics,
};

export { StructuredLogger };

export default StructuredLogger;
