import type { Logger } from '@shared';
import type { AIContentRequest, AIContentResponse } from './types';

interface QueuedRequest
{
	id: string;
	request: AIContentRequest;
	resolve: (response: AIContentResponse) => void;
	reject: (error: Error) => void;
	timestamp: number;
	priority: number;
	retryCount: number;
}

interface BatchConfig
{
	maxBatchSize: number;
	maxWaitTime: number; // milliseconds
	enableBatching: boolean;
}

interface QueueStats
{
	pending: number;
	processing: number;
	completed: number;
	failed: number;
	averageWaitTime: number;
	averageProcessingTime: number;
}

/**
 * Request queue for batching and managing AI requests efficiently
 */
class AIRequestQueue
{
	private logger: Logger;

	private config: BatchConfig;

	private queue: QueuedRequest[] = [];

	private processing: Set<string> = new Set();

	private stats: QueueStats = {
		pending: 0,
		processing: 0,
		completed: 0,
		failed: 0,
		averageWaitTime: 0,
		averageProcessingTime: 0,
	};

	private batchTimer?: NodeJS.Timeout;

	private requestIdCounter: number = 0;

	constructor(config: BatchConfig, logger: Logger)
	{
		this.config = config;
		this.logger = logger.child({ component: 'AIRequestQueue' });
	}

	/**
	 * Add a request to the queue
	 */
	async enqueue(
		request: AIContentRequest,
		priority: number = 0,
	): Promise<AIContentResponse>
	{
		return new Promise((resolve, reject) =>
		{
			const queuedRequest: QueuedRequest = {
				id: this.generateRequestId(),
				request,
				resolve,
				reject,
				timestamp: Date.now(),
				priority,
				retryCount: 0,
			};

			// Insert request in priority order (higher priority first)
			const insertIndex = this.queue.findIndex(item => item.priority < priority);
			if (insertIndex === -1)
			{
				this.queue.push(queuedRequest);
			}
			else
			{
				this.queue.splice(insertIndex, 0, queuedRequest);
			}

			this.stats.pending = this.queue.length;

			this.logger.debug('Request enqueued', {
				requestId: queuedRequest.id,
				domain: request.domain,
				priority,
				queueSize: this.queue.length,
			});

			// Start batch processing if enabled
			if (this.config.enableBatching)
			{
				this.scheduleBatchProcessing();
			}
		});
	}

	/**
	 * Process the next batch of requests
	 */
	async processBatch(
		processor: (requests: AIContentRequest[]) => Promise<AIContentResponse[]>,
	): Promise<void>
	{
		if (this.queue.length === 0)
		{
			return;
		}

		const batchSize = Math.min(this.config.maxBatchSize, this.queue.length);
		const batch = this.queue.splice(0, batchSize);

		this.stats.pending = this.queue.length;
		this.stats.processing = batch.length;

		// Mark requests as processing
		for (const request of batch)
		{
			this.processing.add(request.id);
		}

		this.logger.debug('Processing batch', {
			batchSize: batch.length,
			remainingQueue: this.queue.length,
		});

		try
		{
			const startTime = Date.now();
			const requests = batch.map(item => item.request);
			const responses = await processor(requests);

			const processingTime = Date.now() - startTime;
			this.updateProcessingStats(processingTime);

			// Resolve individual requests
			for (let i = 0; i < batch.length; i++)
			{
				const queuedRequest = batch[i];
				const response = responses[i];

				if (response && response.success)
				{
					const waitTime = startTime - queuedRequest.timestamp;
					this.updateWaitTimeStats(waitTime);

					queuedRequest.resolve(response);
					this.stats.completed++;
				}
				else
				{
					// Handle failed request
					this.handleFailedRequest(queuedRequest, response?.error?.message || 'Unknown error');
				}

				this.processing.delete(queuedRequest.id);
			}

			this.stats.processing = this.processing.size;
		}
		catch (error)
		{
			this.logger.error('Batch processing failed', { error: error.message });

			// Reject all requests in the batch
			for (const queuedRequest of batch)
			{
				this.handleFailedRequest(queuedRequest, error.message);
				this.processing.delete(queuedRequest.id);
			}

			this.stats.processing = this.processing.size;
		}
	}

	/**
	 * Process a single request (non-batched)
	 */
	async processSingle(
		processor: (request: AIContentRequest) => Promise<AIContentResponse>,
	): Promise<void>
	{
		if (this.queue.length === 0)
		{
			return;
		}

		const queuedRequest = this.queue.shift()!;
		this.stats.pending = this.queue.length;
		this.processing.add(queuedRequest.id);
		this.stats.processing = this.processing.size;

		this.logger.debug('Processing single request', {
			requestId: queuedRequest.id,
			domain: queuedRequest.request.domain,
		});

		try
		{
			const startTime = Date.now();
			const response = await processor(queuedRequest.request);
			const processingTime = Date.now() - startTime;

			this.updateProcessingStats(processingTime);

			if (response.success)
			{
				const waitTime = startTime - queuedRequest.timestamp;
				this.updateWaitTimeStats(waitTime);

				queuedRequest.resolve(response);
				this.stats.completed++;
			}
			else
			{
				this.handleFailedRequest(queuedRequest, response.error?.message || 'Unknown error');
			}
		}
		catch (error)
		{
			this.handleFailedRequest(queuedRequest, error.message);
		}
		finally
		{
			this.processing.delete(queuedRequest.id);
			this.stats.processing = this.processing.size;
		}
	}

	/**
	 * Get queue statistics
	 */
	getStats(): QueueStats
	{
		return { ...this.stats };
	}

	/**
	 * Get queue size
	 */
	getQueueSize(): number
	{
		return this.queue.length;
	}

	/**
	 * Clear the queue (reject all pending requests)
	 */
	clear(reason: string = 'Queue cleared'): void
	{
		const pendingRequests = [...this.queue];
		this.queue = [];
		this.stats.pending = 0;

		for (const request of pendingRequests)
		{
			request.reject(new Error(reason));
			this.stats.failed++;
		}

		if (this.batchTimer)
		{
			clearTimeout(this.batchTimer);
			this.batchTimer = undefined;
		}

		this.logger.info('Queue cleared', { rejectedRequests: pendingRequests.length, reason });
	}

	/**
	 * Cleanup resources
	 */
	destroy(): void
	{
		this.clear('Queue destroyed');
	}

	private scheduleBatchProcessing(): void
	{
		if (this.batchTimer)
		{
			return; // Already scheduled
		}

		// Process immediately if batch is full
		if (this.queue.length >= this.config.maxBatchSize)
		{
			this.batchTimer = setTimeout(() =>
			{
				this.batchTimer = undefined;
				// Note: Actual processing should be triggered externally
			}, 0);
			return;
		}

		// Schedule processing after max wait time
		this.batchTimer = setTimeout(() =>
		{
			this.batchTimer = undefined;
			// Note: Actual processing should be triggered externally
		}, this.config.maxWaitTime);
	}

	private handleFailedRequest(queuedRequest: QueuedRequest, errorMessage: string): void
	{
		const maxRetries = 3;

		if (queuedRequest.retryCount < maxRetries)
		{
			// Retry the request
			queuedRequest.retryCount++;
			queuedRequest.timestamp = Date.now(); // Reset timestamp for retry

			// Add back to queue with lower priority
			const retryPriority = queuedRequest.priority - 1;
			const insertIndex = this.queue.findIndex(item => item.priority < retryPriority);

			if (insertIndex === -1)
			{
				this.queue.push(queuedRequest);
			}
			else
			{
				this.queue.splice(insertIndex, 0, queuedRequest);
			}

			this.stats.pending = this.queue.length;

			this.logger.debug('Request queued for retry', {
				requestId: queuedRequest.id,
				retryCount: queuedRequest.retryCount,
				error: errorMessage,
			});
		}
		else
		{
			// Max retries exceeded, reject the request
			queuedRequest.reject(new Error(`Max retries exceeded: ${errorMessage}`));
			this.stats.failed++;

			this.logger.warn('Request failed after max retries', {
				requestId: queuedRequest.id,
				retryCount: queuedRequest.retryCount,
				error: errorMessage,
			});
		}
	}

	private updateWaitTimeStats(waitTime: number): void
	{
		const alpha = 0.1; // Exponential moving average factor
		this.stats.averageWaitTime = this.stats.averageWaitTime * (1 - alpha) + waitTime * alpha;
	}

	private updateProcessingStats(processingTime: number): void
	{
		const alpha = 0.1; // Exponential moving average factor
		this.stats.averageProcessingTime = this.stats.averageProcessingTime * (1 - alpha) + processingTime * alpha;
	}

	private generateRequestId(): string
	{
		return `req-${Date.now()}-${++this.requestIdCounter}`;
	}
}

export type { BatchConfig, QueueStats };

export default AIRequestQueue;
