import type { AxiosInstance, AxiosError } from 'axios';
import type { Logger } from '@shared';
import axios from 'axios';

import type {
	AIProviderConfig,
	AIContentRequest,
	AIContentResponse,
} from '../types';
import BaseAIProvider from '../BaseAIProvider';

interface OpenRouterMessage
{
	role: 'system' | 'user' | 'assistant';
	content: string;
}

interface OpenRouterRequest
{
	model: string;
	messages: OpenRouterMessage[];
	max_tokens?: number;
	temperature?: number;
	response_format?: { type: 'json_object' };
}

interface OpenRouterResponse
{
	id: string;
	object: string;
	created: number;
	model: string;
	choices: Array<{
		index: number;
		message: {
			role: string;
			content: string;
		};
		finish_reason: string;
	}>;
	usage: {
		prompt_tokens: number;
		completion_tokens: number;
		total_tokens: number;
	};
}

/**
 * OpenRouter provider implementation for content generation
 * Provides access to multiple models through a single API
 */
class OpenRouterProvider extends BaseAIProvider
{
	private client: AxiosInstance;

	private readonly model: string;

	constructor(config: AIProviderConfig, logger: Logger)
	{
		super('openrouter', config, logger);

		// Use a cost-effective model available on OpenRouter
		this.model = 'anthropic/claude-3-haiku:beta';
		this.client = axios.create({
			baseURL: config.baseUrl || 'https://openrouter.ai/api/v1',
			timeout: config.timeout || 30000,
			headers: {
				'Content-Type': 'application/json',
				'HTTP-Referer': 'https://domain-seeder.local',
				'X-Title': 'Domain Seeder Service',
			},
		});
	}

	protected async performRequest(request: AIContentRequest, requestId: string): Promise<AIContentResponse>
	{
		const apiKey = this.getNextApiKey();
		const prompt = this.buildPrompt(request);

		const openrouterRequest: OpenRouterRequest = {
			model: this.model,
			messages: [
				{
					role: 'system',
					content: 'You are an expert content writer specializing in domain descriptions and SEO content. Always respond with valid JSON.',
				},
				{
					role: 'user',
					content: prompt,
				},
			],
			max_tokens: this.config.maxTokens || 1500,
			temperature: this.config.temperature || 0.7,
			response_format: { type: 'json_object' },
		};

		const response = await this.client.post<OpenRouterResponse>('/chat/completions', openrouterRequest, {
			headers: {
				Authorization: `Bearer ${apiKey}`,
			},
		});

		const content = response.data.choices[0]?.message?.content;
		if (!content)
		{
			throw new Error('No content in OpenRouter response');
		}

		const parsedContent = JSON.parse(content);
		const wordCount = parsedContent.summary?.split(/\s+/).length || 0;

		return {
			success: true,
			content: {
				summary: parsedContent.summary || '',
				category: {
					primary: parsedContent.category?.primary || 'General',
					secondary: parsedContent.category?.secondary,
				},
				tags: parsedContent.tags || [],
				seoSummary: parsedContent.seoSummary || parsedContent.summary?.substring(0, 160) || '',
				wordCount,
				confidence: parsedContent.confidence || 0.8,
			},
			metadata: {
				provider: this.name,
				model: this.model,
				tokensUsed: response.data.usage.total_tokens,
				cost: (this.config.costPerToken || 0) * response.data.usage.total_tokens,
				processingTime: 0, // Will be set by base class
				requestId,
			},
		};
	}

	protected async performHealthCheck(): Promise<boolean>
	{
		try
		{
			const apiKey = this.getNextApiKey();
			const response = await this.client.get('/models', {
				headers: {
					Authorization: `Bearer ${apiKey}`,
				},
				timeout: 5000,
			});

			return response.status === 200;
		}
		catch (error)
		{
			return false;
		}
	}

	protected getErrorCode(error: any): string
	{
		if (axios.isAxiosError(error))
		{
			const axiosError = error as AxiosError;
			switch (axiosError.response?.status)
			{
				case 401:
					return 'INVALID_API_KEY';
				case 402:
					return 'INSUFFICIENT_CREDITS';
				case 429:
					return 'RATE_LIMITED';
				case 500:
				case 502:
				case 503:
				case 504:
					return 'SERVER_ERROR';
				default:
					return 'REQUEST_FAILED';
			}
		}
		return 'UNKNOWN_ERROR';
	}

	protected isRetryableError(error: any): boolean
	{
		if (axios.isAxiosError(error))
		{
			const axiosError = error as AxiosError;
			const status = axiosError.response?.status;

			// Retry on server errors and rate limits (with backoff)
			// Don't retry on insufficient credits
			return status === 429 || (status >= 500 && status <= 599);
		}
		return false;
	}

	private buildPrompt(request: AIContentRequest): string
	{
		const {
			domain, existingContent, requirements, context,
		} = request;

		let prompt = `Generate comprehensive content for the domain "${domain}". `;

		if (existingContent?.summary)
		{
			prompt += `Enhance and expand this existing content: "${existingContent.summary}". `;
		}

		prompt += `Requirements:
- Generate a detailed summary of ${requirements.minWords}-${requirements.maxWords} words
- Classify into primary and optional secondary category
- Generate ${requirements.minTags}+ relevant tags
- Create an SEO-optimized summary (150-160 characters)
- Language: ${requirements.language || 'English'}
- Tone: ${requirements.tone || 'professional'}`;

		if (requirements.includeKeywords?.length)
		{
			prompt += `
- Include these keywords naturally: ${requirements.includeKeywords.join(', ')}`;
		}

		if (context?.industry)
		{
			prompt += `
- Industry context: ${context.industry}`;
		}

		if (context?.audience)
		{
			prompt += `
- Target audience: ${context.audience}`;
		}

		prompt += `

Respond with valid JSON in this exact format:
{
  "summary": "Detailed ${requirements.minWords}+ word description...",
  "category": {
    "primary": "Primary category",
    "secondary": "Optional secondary category"
  },
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "seoSummary": "SEO-optimized 150-160 character summary...",
  "confidence": 0.85
}`;

		return prompt;
	}
}

export default OpenRouterProvider;
