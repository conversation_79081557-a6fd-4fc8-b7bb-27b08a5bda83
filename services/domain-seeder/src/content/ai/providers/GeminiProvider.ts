import axios, { type AxiosInstance, type AxiosError } from 'axios';
import type { Logger } from '@shared';
import type {
	AIProviderConfig,
	AIContentRequest,
	AIContentResponse,
} from '../types';

import BaseAIProvider from '../BaseAIProvider';

interface GeminiContent
{
	parts: Array<{
		text: string;
	}>;
}

interface GeminiRequest
{
	contents: Array<{
		role?: string;
		parts: Array<{
			text: string;
		}>;
	}>;
	generationConfig?: {
		temperature?: number;
		maxOutputTokens?: number;
		responseMimeType?: string;
	};
	systemInstruction?: {
		parts: Array<{
			text: string;
		}>;
	};
}

interface GeminiResponse
{
	candidates: Array<{
		content: GeminiContent;
		finishReason: string;
		index: number;
		safetyRatings: Array<{
			category: string;
			probability: string;
		}>;
	}>;
	usageMetadata: {
		promptTokenCount: number;
		candidatesTokenCount: number;
		totalTokenCount: number;
	};
}

/**
 * Google Gemini provider implementation for content generation
 */
class Gemini<PERSON><PERSON>ider extends BaseAIProvider
{
	private client: AxiosInstance;

	private readonly model: string;

	constructor(config: AIProviderConfig, logger: Logger)
	{
		super('gemini', config, logger);

		this.model = 'gemini-1.5-flash'; // Cost-effective model
		this.client = axios.create({
			baseURL: config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta',
			timeout: config.timeout || 30000,
			headers: {
				'Content-Type': 'application/json',
			},
		});
	}

	protected async performRequest(request: AIContentRequest, requestId: string): Promise<AIContentResponse>
	{
		const apiKey = this.getNextApiKey();
		const prompt = this.buildPrompt(request);

		const geminiRequest: GeminiRequest = {
			contents: [
				{
					role: 'user',
					parts: [
						{
							text: prompt,
						},
					],
				},
			],
			generationConfig: {
				temperature: this.config.temperature || 0.7,
				maxOutputTokens: this.config.maxTokens || 1500,
				responseMimeType: 'application/json',
			},
			systemInstruction: {
				parts: [
					{
						text: 'You are an expert content writer specializing in domain descriptions and SEO content. Always respond with valid JSON only.',
					},
				],
			},
		};

		const response = await this.client.post<GeminiResponse>(
			`/models/${this.model}:generateContent?key=${apiKey}`,
			geminiRequest,
		);

		const content = response.data.candidates[0]?.content?.parts[0]?.text;
		if (!content)
		{
			throw new Error('No content in Gemini response');
		}

		const parsedContent = JSON.parse(content);
		const wordCount = parsedContent.summary?.split(/\s+/).length || 0;

		return {
			success: true,
			content: {
				summary: parsedContent.summary || '',
				category: {
					primary: parsedContent.category?.primary || 'General',
					secondary: parsedContent.category?.secondary,
				},
				tags: parsedContent.tags || [],
				seoSummary: parsedContent.seoSummary || parsedContent.summary?.substring(0, 160) || '',
				wordCount,
				confidence: parsedContent.confidence || 0.8,
			},
			metadata: {
				provider: this.name,
				model: this.model,
				tokensUsed: response.data.usageMetadata.totalTokenCount,
				cost: (this.config.costPerToken || 0) * response.data.usageMetadata.totalTokenCount,
				processingTime: 0, // Will be set by base class
				requestId,
			},
		};
	}

	protected async performHealthCheck(): Promise<boolean>
	{
		try
		{
			const apiKey = this.getNextApiKey();

			// Make a minimal request to test the API
			const testRequest: GeminiRequest = {
				contents: [
					{
						parts: [
							{
								text: 'Hello',
							},
						],
					},
				],
				generationConfig: {
					maxOutputTokens: 10,
				},
			};

			const response = await this.client.post(
				`/models/${this.model}:generateContent?key=${apiKey}`,
				testRequest,
				{ timeout: 5000 },
			);

			return response.status === 200;
		}
		catch (error)
		{
			return false;
		}
	}

	protected getErrorCode(error: any): string
	{
		if (axios.isAxiosError(error))
		{
			const axiosError = error as AxiosError;
			switch (axiosError.response?.status)
			{
				case 400:
					return 'INVALID_REQUEST';
				case 401:
				case 403:
					return 'INVALID_API_KEY';
				case 429:
					return 'RATE_LIMITED';
				case 500:
				case 502:
				case 503:
				case 504:
					return 'SERVER_ERROR';
				default:
					return 'REQUEST_FAILED';
			}
		}
		return 'UNKNOWN_ERROR';
	}

	protected isRetryableError(error: any): boolean
	{
		if (axios.isAxiosError(error))
		{
			const axiosError = error as AxiosError;
			const status = axiosError.response?.status;

			// Retry on server errors and rate limits (with backoff)
			return status === 429 || (status >= 500 && status <= 599);
		}
		return false;
	}

	private buildPrompt(request: AIContentRequest): string
	{
		const {
			domain, existingContent, requirements, context,
		} = request;

		let prompt = `Generate comprehensive content for the domain "${domain}". `;

		if (existingContent?.summary)
		{
			prompt += `Enhance and expand this existing content: "${existingContent.summary}". `;
		}

		prompt += `Requirements:
- Generate a detailed summary of ${requirements.minWords}-${requirements.maxWords} words
- Classify into primary and optional secondary category
- Generate ${requirements.minTags}+ relevant tags
- Create an SEO-optimized summary (150-160 characters)
- Language: ${requirements.language || 'English'}
- Tone: ${requirements.tone || 'professional'}`;

		if (requirements.includeKeywords?.length)
		{
			prompt += `
- Include these keywords naturally: ${requirements.includeKeywords.join(', ')}`;
		}

		if (context?.industry)
		{
			prompt += `
- Industry context: ${context.industry}`;
		}

		if (context?.audience)
		{
			prompt += `
- Target audience: ${context.audience}`;
		}

		prompt += `

Respond with valid JSON in this exact format:
{
  "summary": "Detailed ${requirements.minWords}+ word description...",
  "category": {
    "primary": "Primary category",
    "secondary": "Optional secondary category"
  },
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "seoSummary": "SEO-optimized 150-160 character summary...",
  "confidence": 0.85
}`;

		return prompt;
	}
}

export default GeminiProvider;
