import type RedisClientWrapper from '@shared/database/RedisClient';
import { logger } from '@shared/utils/Logger';

/**
 * Configuration for Redis cache layer
 */
type RedisCacheConfig =
{
	/**
	 * Cache key prefix for domain existence results
	 */
	keyPrefix: string;

	/**
	 * TTL for positive existence results in seconds (24 hours default)
	 */
	positiveTtl: number;

	/**
	 * TTL for negative existence results in seconds (1 hour default)
	 */
	negativeTtl: number;

	/**
	 * Maximum batch size for cache operations
	 */
	maxBatchSize: number;

	/**
	 * Enable cache warming on startup
	 */
	enableWarmup: boolean;

	/**
	 * Cache warming batch size
	 */
	warmupBatchSize: number;

	/**
	 * Maximum number of domains to warm up
	 */
	maxWarmupDomains: number;

	/**
	 * Enable cache metrics collection
	 */
	enableMetrics: boolean;

	/**
	 * Metrics collection interval in milliseconds
	 */
	metricsInterval: number;

	/**
	 * Enable cache invalidation patterns
	 */
	enableInvalidation: boolean;

	/**
	 * Cache compression for large values
	 */
	enableCompression: boolean;
};

/**
 * Default Redis cache configuration
 */
const DEFAULT_REDIS_CACHE_CONFIG: RedisCacheConfig = {
	keyPrefix: 'domain:exists:',
	positiveTtl: 86400, // 24 hours
	negativeTtl: 3600, // 1 hour
	maxBatchSize: 1000,
	enableWarmup: true,
	warmupBatchSize: 500,
	maxWarmupDomains: 100000,
	enableMetrics: true,
	metricsInterval: 60000, // 1 minute
	enableInvalidation: true,
	enableCompression: false,
};

/**
 * Cache metrics for monitoring and performance analysis
 */
type CacheMetrics =
{
	/**
	 * Total number of cache get operations
	 */
	totalGets: number;

	/**
	 * Total number of cache hits
	 */
	totalHits: number;

	/**
	 * Total number of cache misses
	 */
	totalMisses: number;

	/**
	 * Total number of cache set operations
	 */
	totalSets: number;

	/**
	 * Total number of cache invalidations
	 */
	totalInvalidations: number;

	/**
	 * Cache hit rate (0-1)
	 */
	hitRate: number;

	/**
	 * Cache miss rate (0-1)
	 */
	missRate: number;

	/**
	 * Average response time for cache operations in milliseconds
	 */
	averageResponseTime: number;

	/**
	 * Number of cache errors
	 */
	errorCount: number;

	/**
	 * Last metrics update timestamp
	 */
	lastUpdated: Date;

	/**
	 * Cache size estimation (number of keys)
	 */
	estimatedSize: number;

	/**
	 * Memory usage estimation in bytes
	 */
	estimatedMemoryUsage: number;
};

/**
 * Cache warming source interface
 */
type CacheWarmingSource =
{
	/**
	 * Name of the warming source
	 */
	name: string;

	/**
	 * Get domains for cache warming
	 * @param offset - Starting offset for pagination
	 * @param limit - Maximum number of domains to return
	 * @returns Promise resolving to array of domain strings
	 */
	getDomains(offset: number, limit: number): Promise<string[]>;

	/**
	 * Get total count of available domains (optional)
	 * @returns Promise resolving to total count or null if unknown
	 */
	getTotalCount?(): Promise<number | null>;
};

/**
 * Cache invalidation pattern
 */
type InvalidationPattern =
{
	/**
	 * Pattern name for logging
	 */
	name: string;

	/**
	 * Redis key pattern to match
	 */
	pattern: string;

	/**
	 * TTL override for matched keys (optional)
	 */
	ttlOverride?: number;

	/**
	 * Whether to delete or just expire matched keys
	 */
	deleteKeys: boolean;
};

/**
 * Redis-based caching layer for domain existence results
 * Provides high-performance caching with warming, invalidation, and monitoring
 */
class RedisCacheLayer
{
	private readonly logger = logger.getLogger('RedisCacheLayer');

	private readonly config: RedisCacheConfig;

	private readonly redis: RedisClientWrapper;

	// Metrics tracking
	private metrics: CacheMetrics = {
		totalGets: 0,
		totalHits: 0,
		totalMisses: 0,
		totalSets: 0,
		totalInvalidations: 0,
		hitRate: 0,
		missRate: 0,
		averageResponseTime: 0,
		errorCount: 0,
		lastUpdated: new Date(),
		estimatedSize: 0,
		estimatedMemoryUsage: 0,
	};

	// Response time tracking
	private responseTimes: number[] = [];

	private metricsTimer?: NodeJS.Timeout;

	constructor(redis: RedisClientWrapper, config: Partial<RedisCacheConfig> = {})
	{
		this.redis = redis;
		this.config = { ...DEFAULT_REDIS_CACHE_CONFIG, ...config };

		this.logger.info('RedisCacheLayer initialized', {
			config: this.config,
		});

		// Start metrics collection if enabled
		if (this.config.enableMetrics)
		{
			this.startMetricsCollection();
		}
	}

	/**
	 * Get domain existence from cache
	 * @param domain - Domain to check
	 * @returns Promise resolving to boolean if cached, null if not cached
	 */
	async get(domain: string): Promise<boolean | null>
	{
		const startTime = Date.now();
		this.metrics.totalGets++;

		try
		{
			const key = this.buildKey(domain);
			const cached = await this.redis.get(key);

			const responseTime = Date.now() - startTime;
			this.recordResponseTime(responseTime);

			if (cached === null)
			{
				this.metrics.totalMisses++;
				this.logger.debug('Cache miss', { domain, responseTime });
				return null;
			}

			this.metrics.totalHits++;
			const result = cached === 'true';
			this.logger.debug('Cache hit', { domain, result, responseTime });
			return result;
		}
		catch (error)
		{
			this.metrics.errorCount++;
			this.logger.error('Cache get failed', {
				domain,
				error: (error as Error).message,
			});
			return null;
		}
	}

	/**
	 * Get multiple domains from cache in batch
	 * @param domains - Array of domains to check
	 * @returns Promise resolving to Map with cached results
	 */
	async getBatch(domains: string[]): Promise<Map<string, boolean>>
	{
		if (domains.length === 0)
		{
			return new Map();
		}

		const startTime = Date.now();
		this.metrics.totalGets += domains.length;

		try
		{
			const keys = domains.map(domain => this.buildKey(domain));
			const cached = await this.redis.mget(keys);

			const responseTime = Date.now() - startTime;
			this.recordResponseTime(responseTime);

			const results = new Map<string, boolean>();
			let hits = 0;
			let misses = 0;

			cached.forEach((value, index) =>
			{
				if (value !== null)
				{
					results.set(domains[index], value === 'true');
					hits++;
				}
				else
				{
					misses++;
				}
			});

			this.metrics.totalHits += hits;
			this.metrics.totalMisses += misses;

			this.logger.debug('Cache batch get completed', {
				totalDomains: domains.length,
				hits,
				misses,
				responseTime,
			});

			return results;
		}
		catch (error)
		{
			this.metrics.errorCount++;
			this.logger.error('Cache batch get failed', {
				domainsCount: domains.length,
				error: (error as Error).message,
			});
			return new Map();
		}
	}

	/**
	 * Set domain existence in cache
	 * @param domain - Domain to cache
	 * @param exists - Whether domain exists
	 */
	async set(domain: string, exists: boolean): Promise<void>
	{
		const startTime = Date.now();
		this.metrics.totalSets++;

		try
		{
			const key = this.buildKey(domain);
			const value = exists.toString();
			const ttl = exists ? this.config.positiveTtl : this.config.negativeTtl;

			await this.redis.setex(key, ttl, value);

			const responseTime = Date.now() - startTime;
			this.recordResponseTime(responseTime);

			this.logger.debug('Cache set completed', {
				domain,
				exists,
				ttl,
				responseTime,
			});
		}
		catch (error)
		{
			this.metrics.errorCount++;
			this.logger.error('Cache set failed', {
				domain,
				exists,
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Set multiple domains in cache using batch operation
	 * @param results - Map of domain to existence status
	 */
	async setBatch(results: Map<string, boolean>): Promise<void>
	{
		if (results.size === 0)
		{
			return;
		}

		const startTime = Date.now();
		this.metrics.totalSets += results.size;

		try
		{
			const pipeline = this.redis.pipeline();

			results.forEach((exists, domain) =>
			{
				const key = this.buildKey(domain);
				const value = exists.toString();
				const ttl = exists ? this.config.positiveTtl : this.config.negativeTtl;
				pipeline.setex(key, ttl, value);
			});

			await pipeline.exec();

			const responseTime = Date.now() - startTime;
			this.recordResponseTime(responseTime);

			this.logger.debug('Cache batch set completed', {
				resultsCount: results.size,
				responseTime,
			});
		}
		catch (error)
		{
			this.metrics.errorCount++;
			this.logger.error('Cache batch set failed', {
				resultsCount: results.size,
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Warm up cache with domains from multiple sources
	 * @param sources - Array of cache warming sources
	 */
	async warmup(sources: CacheWarmingSource[]): Promise<void>
	{
		if (!this.config.enableWarmup || sources.length === 0)
		{
			return;
		}

		this.logger.info('Starting cache warmup', {
			sources: sources.map(s => s.name),
			maxDomains: this.config.maxWarmupDomains,
			batchSize: this.config.warmupBatchSize,
		});

		const startTime = Date.now();
		let totalWarmed = 0;

		try
		{
			for (const source of sources)
			{
				if (totalWarmed >= this.config.maxWarmupDomains)
				{
					break;
				}

				const sourceStartTime = Date.now();
				let sourceWarmed = 0;
				let offset = 0;

				this.logger.info('Warming from source', { source: source.name });

				while (totalWarmed < this.config.maxWarmupDomains)
				{
					const remainingQuota = this.config.maxWarmupDomains - totalWarmed;
					const batchSize = Math.min(this.config.warmupBatchSize, remainingQuota);

					const domains = await source.getDomains(offset, batchSize);

					if (domains.length === 0)
					{
						break; // No more domains from this source
					}

					// Check which domains are not already cached
					const uncachedDomains = await this.filterUncachedDomains(domains);

					if (uncachedDomains.length > 0)
					{
						// Set all as existing (warmup assumes these are valid domains)
						const warmupResults = new Map<string, boolean>();
						uncachedDomains.forEach((domain) =>
						{
							warmupResults.set(domain, true);
						});

						await this.setBatch(warmupResults);
						sourceWarmed += uncachedDomains.length;
						totalWarmed += uncachedDomains.length;
					}

					offset += domains.length;

					// Log progress periodically
					if (offset % (this.config.warmupBatchSize * 10) === 0)
					{
						this.logger.debug('Warmup progress', {
							source: source.name,
							processed: offset,
							warmed: sourceWarmed,
							totalWarmed,
						});
					}
				}

				const sourceTime = Date.now() - sourceStartTime;
				this.logger.info('Source warmup completed', {
					source: source.name,
					warmed: sourceWarmed,
					totalProcessed: offset,
					duration: sourceTime,
				});
			}

			const totalTime = Date.now() - startTime;
			this.logger.info('Cache warmup completed', {
				totalWarmed,
				sources: sources.length,
				duration: totalTime,
			});
		}
		catch (error)
		{
			this.logger.error('Cache warmup failed', {
				error: (error as Error).message,
				totalWarmed,
			});
		}
	}

	/**
	 * Invalidate cache entries matching patterns
	 * @param patterns - Array of invalidation patterns
	 */
	async invalidate(patterns: InvalidationPattern[]): Promise<void>
	{
		if (!this.config.enableInvalidation || patterns.length === 0)
		{
			return;
		}

		this.logger.info('Starting cache invalidation', {
			patterns: patterns.map(p => ({ name: p.name, pattern: p.pattern })),
		});

		const startTime = Date.now();
		let totalInvalidated = 0;

		try
		{
			for (const pattern of patterns)
			{
				const patternStartTime = Date.now();
				let patternInvalidated = 0;

				this.logger.debug('Processing invalidation pattern', {
					name: pattern.name,
					pattern: pattern.pattern,
				});

				if (pattern.deleteKeys)
				{
					// Delete matching keys
					const deletedCount = await this.redis.deletePattern(pattern.pattern);
					patternInvalidated = deletedCount;
				}
				else
				{
					// Expire matching keys
					const keys = await this.redis.keys(pattern.pattern);
					if (keys.length > 0)
					{
						const pipeline = this.redis.pipeline();
						const ttl = pattern.ttlOverride || 1; // Expire in 1 second

						keys.forEach((key) =>
						{
							pipeline.expire(key, ttl);
						});

						await pipeline.exec();
						patternInvalidated = keys.length;
					}
				}

				const patternTime = Date.now() - patternStartTime;
				totalInvalidated += patternInvalidated;

				this.logger.debug('Pattern invalidation completed', {
					name: pattern.name,
					invalidated: patternInvalidated,
					duration: patternTime,
				});
			}

			this.metrics.totalInvalidations += totalInvalidated;

			const totalTime = Date.now() - startTime;
			this.logger.info('Cache invalidation completed', {
				totalInvalidated,
				patterns: patterns.length,
				duration: totalTime,
			});
		}
		catch (error)
		{
			this.metrics.errorCount++;
			this.logger.error('Cache invalidation failed', {
				error: (error as Error).message,
				totalInvalidated,
			});
		}
	}

	/**
	 * Clear all cache entries with the configured prefix
	 */
	async clear(): Promise<void>
	{
		this.logger.info('Clearing all cache entries');

		try
		{
			const pattern = `${this.config.keyPrefix}*`;
			const deletedCount = await this.redis.deletePattern(pattern);

			this.metrics.totalInvalidations += deletedCount;

			this.logger.info('Cache cleared', { deletedCount });
		}
		catch (error)
		{
			this.metrics.errorCount++;
			this.logger.error('Cache clear failed', {
				error: (error as Error).message,
			});
		}
	}

	/**
	 * Get current cache metrics
	 */
	getMetrics(): CacheMetrics
	{
		// Update calculated metrics
		const totalRequests = this.metrics.totalGets;
		this.metrics.hitRate = totalRequests > 0 ? this.metrics.totalHits / totalRequests : 0;
		this.metrics.missRate = totalRequests > 0 ? this.metrics.totalMisses / totalRequests : 0;
		this.metrics.averageResponseTime = this.calculateAverageResponseTime();
		this.metrics.lastUpdated = new Date();

		return { ...this.metrics };
	}

	/**
	 * Reset cache metrics
	 */
	resetMetrics(): void
	{
		this.metrics = {
			totalGets: 0,
			totalHits: 0,
			totalMisses: 0,
			totalSets: 0,
			totalInvalidations: 0,
			hitRate: 0,
			missRate: 0,
			averageResponseTime: 0,
			errorCount: 0,
			lastUpdated: new Date(),
			estimatedSize: 0,
			estimatedMemoryUsage: 0,
		};

		this.responseTimes = [];

		this.logger.info('Cache metrics reset');
	}

	/**
	 * Get cache configuration
	 */
	getConfig(): RedisCacheConfig
	{
		return { ...this.config };
	}

	/**
	 * Perform cache health check
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			const testKey = `${this.config.keyPrefix}health:check`;
			const testValue = Date.now().toString();

			// Test set operation
			await this.redis.setex(testKey, 10, testValue);

			// Test get operation
			const retrieved = await this.redis.get(testKey);

			// Clean up
			await this.redis.del(testKey);

			const isHealthy = retrieved === testValue;

			this.logger.debug('Cache health check completed', { isHealthy });

			return isHealthy;
		}
		catch (error)
		{
			this.logger.error('Cache health check failed', {
				error: (error as Error).message,
			});
			return false;
		}
	}

	/**
	 * Cleanup resources and stop metrics collection
	 */
	destroy(): void
	{
		if (this.metricsTimer)
		{
			clearInterval(this.metricsTimer);
			this.metricsTimer = undefined;
		}

		this.logger.info('RedisCacheLayer destroyed');
	}

	/**
	 * Build cache key for domain
	 */
	private buildKey(domain: string): string
	{
		return `${this.config.keyPrefix}${domain}`;
	}

	/**
	 * Filter out domains that are already cached
	 */
	private async filterUncachedDomains(domains: string[]): Promise<string[]>
	{
		const cached = await this.getBatch(domains);
		return domains.filter(domain => !cached.has(domain));
	}

	/**
	 * Record response time for metrics
	 */
	private recordResponseTime(responseTime: number): void
	{
		this.responseTimes.push(responseTime);

		// Keep only last 1000 response times to prevent memory growth
		if (this.responseTimes.length > 1000)
		{
			this.responseTimes = this.responseTimes.slice(-1000);
		}
	}

	/**
	 * Calculate average response time from recorded times
	 */
	private calculateAverageResponseTime(): number
	{
		if (this.responseTimes.length === 0)
		{
			return 0;
		}

		const sum = this.responseTimes.reduce((acc, time) => acc + time, 0);
		return sum / this.responseTimes.length;
	}

	/**
	 * Start metrics collection timer
	 */
	private startMetricsCollection(): void
	{
		this.metricsTimer = setInterval(async () =>
		{
			try
			{
				// Update estimated cache size and memory usage
				await this.updateCacheStats();
			}
			catch (error)
			{
				this.logger.warn('Failed to update cache stats', {
					error: (error as Error).message,
				});
			}
		}, this.config.metricsInterval);

		this.logger.debug('Metrics collection started', {
			interval: this.config.metricsInterval,
		});
	}

	/**
	 * Update cache size and memory usage estimates
	 */
	private async updateCacheStats(): Promise<void>
	{
		try
		{
			// Get approximate key count using SCAN
			const pattern = `${this.config.keyPrefix}*`;
			const keys = await this.redis.keys(pattern);
			this.metrics.estimatedSize = keys.length;

			// Estimate memory usage (rough calculation)
			// Average key size + average value size + Redis overhead
			const avgKeySize = this.config.keyPrefix.length + 20; // domain name ~20 chars
			const avgValueSize = 5; // "true" or "false"
			const redisOverhead = 50; // Redis per-key overhead
			this.metrics.estimatedMemoryUsage = keys.length * (avgKeySize + avgValueSize + redisOverhead);
		}
		catch (error)
		{
			this.logger.warn('Failed to update cache stats', {
				error: (error as Error).message,
			});
		}
	}
}

export type {
	RedisCacheConfig, CacheMetrics, CacheWarmingSource, InvalidationPattern,
};

export { DEFAULT_REDIS_CACHE_CONFIG, RedisCacheLayer };
export default RedisCacheLayer;
