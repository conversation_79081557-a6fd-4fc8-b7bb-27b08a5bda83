import type RedisClientWrapper from '@shared/database/RedisClient';
import { logger } from '@shared/utils/Logger';

/**
 * Configuration for BloomFilter persistence and maintenance
 */
type BloomFilterConfig =
{
	/**
	 * Redis key prefix for bloom filter persistence
	 */
	redisKeyPrefix: string;

	/**
	 * Whether to enable Redis persistence
	 */
	enablePersistence: boolean;

	/**
	 * Auto-save interval in milliseconds (0 to disable)
	 */
	autoSaveInterval: number;

	/**
	 * Maximum age before automatic rebuild in milliseconds
	 */
	maxAge: number;

	/**
	 * Threshold for triggering rebuild based on estimated false positive rate
	 */
	rebuildThreshold: number;

	/**
	 * Whether to enable automatic warming from database snapshots
	 */
	enableAutoWarming: boolean;

	/**
	 * Batch size for warming operations
	 */
	warmingBatchSize: number;
};

/**
 * Default configuration for BloomFilter
 */
const DEFAULT_BLOOM_CONFIG: BloomFilterConfig = {
	redisKeyPrefix: 'bloom:domain:',
	enablePersistence: true,
	autoSaveInterval: 300000, // 5 minutes
	maxAge: 86400000, // 24 hours
	rebuildThreshold: 0.05, // 5% false positive rate triggers rebuild
	enableAutoWarming: true,
	warmingBatchSize: 10000,
};

/**
 * Bloom filter metadata for persistence
 */
type BloomFilterMetadata =
{
	size: number;
	hashFunctions: number;
	falsePositiveRate: number;
	itemCount: number;
	createdAt: number;
	lastSavedAt: number;
	version: string;
};

/**
 * Bloom filter implementation for fast domain existence checking
 * Provides probabilistic membership testing with configurable false positive rate,
 * Redis-backed persistence, automatic warming, and maintenance
 */
class BloomFilter
{
	private readonly logger = logger.getLogger('BloomFilter');

	private readonly bitArray: Uint8Array;

	private readonly size: number;

	private readonly hashFunctions: number;

	private readonly falsePositiveRate: number;

	private readonly config: BloomFilterConfig;

	private readonly redisClient?: RedisClientWrapper;

	private itemCount: number = 0;

	private createdAt: number = Date.now();

	private lastSavedAt: number = 0;

	private autoSaveTimer?: NodeJS.Timeout;

	private readonly version = '1.0.0';

	constructor(
		expectedItems: number,
		falsePositiveRate: number = 0.01,
		redisClient?: RedisClientWrapper,
		config: Partial<BloomFilterConfig> = {},
	)
	{
		this.falsePositiveRate = falsePositiveRate;
		this.config = { ...DEFAULT_BLOOM_CONFIG, ...config };
		this.redisClient = redisClient;

		// Calculate optimal bit array size
		this.size = Math.ceil(
			(-expectedItems * Math.log(falsePositiveRate)) / (Math.log(2) ** 2),
		);

		// Calculate optimal number of hash functions
		this.hashFunctions = Math.ceil((this.size / expectedItems) * Math.log(2));

		// Initialize bit array
		this.bitArray = new Uint8Array(Math.ceil(this.size / 8));

		this.logger.info('BloomFilter initialized', {
			expectedItems,
			falsePositiveRate,
			bitArraySize: this.size,
			hashFunctions: this.hashFunctions,
			memoryUsage: `${Math.ceil(this.size / 8)} bytes`,
			persistenceEnabled: this.config.enablePersistence && !!this.redisClient,
		});

		// Set up auto-save if enabled
		if (this.config.autoSaveInterval > 0 && this.redisClient && this.config.enablePersistence)
		{
			this.setupAutoSave();
		}
	}

	/**
	 * Add a domain to the bloom filter
	 */
	add(domain: string): void
	{
		const hashes = this.getHashes(domain);

		for (const hash of hashes)
		{
			const bitIndex = hash % this.size;
			const byteIndex = Math.floor(bitIndex / 8);
			const bitOffset = bitIndex % 8;

			this.bitArray[byteIndex] |= (1 << bitOffset);
		}

		this.itemCount++;
	}

	/**
	 * Test if a domain might exist in the bloom filter
	 * Returns false if definitely not present, true if possibly present
	 */
	test(domain: string): boolean
	{
		const hashes = this.getHashes(domain);

		for (const hash of hashes)
		{
			const bitIndex = hash % this.size;
			const byteIndex = Math.floor(bitIndex / 8);
			const bitOffset = bitIndex % 8;

			if ((this.bitArray[byteIndex] & (1 << bitOffset)) === 0)
			{
				return false; // Definitely not present
			}
		}

		return true; // Possibly present
	}

	/**
	 * Add multiple domains to the bloom filter
	 */
	addBatch(domains: string[]): void
	{
		for (const domain of domains)
		{
			this.add(domain);
		}
	}

	/**
	 * Test multiple domains against the bloom filter
	 */
	testBatch(domains: string[]): Map<string, boolean>
	{
		const results = new Map<string, boolean>();

		for (const domain of domains)
		{
			results.set(domain, this.test(domain));
		}

		return results;
	}

	/**
	 * Clear the bloom filter
	 */
	clear(): void
	{
		this.bitArray.fill(0);
		this.itemCount = 0;
		this.createdAt = Date.now();
		this.lastSavedAt = 0;
		this.logger.debug('BloomFilter cleared');
	}

	/**
	 * Get current statistics
	 */
	getStats(): {
		itemCount: number;
		size: number;
		hashFunctions: number;
		falsePositiveRate: number;
		estimatedFalsePositiveRate: number;
		memoryUsage: number;
		age: number;
		lastSavedAt: number;
		needsRebuild: boolean;
		version: string;
		createdAt: number;
		}
	{
		// Calculate estimated false positive rate based on current load
		const estimatedFalsePositiveRate = (1 - Math.exp((-this.hashFunctions * this.itemCount) / this.size)) ** this.hashFunctions;

		const age = Date.now() - this.createdAt;

		// Calculate needsRebuild directly to avoid circular dependency
		const needsRebuild = age > this.config.maxAge ||
			estimatedFalsePositiveRate > this.config.rebuildThreshold;

		return {
			itemCount: this.itemCount,
			size: this.size,
			hashFunctions: this.hashFunctions,
			falsePositiveRate: this.falsePositiveRate,
			estimatedFalsePositiveRate,
			memoryUsage: this.bitArray.length,
			age,
			lastSavedAt: this.lastSavedAt,
			needsRebuild,
			version: this.version,
			createdAt: this.createdAt,
		};
	}

	/**
	 * Serialize bloom filter to JSON for persistence
	 */
	serialize(): string
	{
		const metadata: BloomFilterMetadata = {
			size: this.size,
			hashFunctions: this.hashFunctions,
			falsePositiveRate: this.falsePositiveRate,
			itemCount: this.itemCount,
			createdAt: this.createdAt,
			lastSavedAt: Date.now(),
			version: this.version,
		};

		return JSON.stringify({
			metadata,
			bitArray: Array.from(this.bitArray),
		});
	}

	/**
	 * Deserialize bloom filter from JSON
	 */
	static deserialize(
		data: string,
		redisClient?: RedisClientWrapper,
		config: Partial<BloomFilterConfig> = {},
	): BloomFilter
	{
		const parsed = JSON.parse(data);
		const metadata: BloomFilterMetadata = parsed.metadata;

		// Create a minimal filter first
		const filter = new BloomFilter(1, metadata.falsePositiveRate, redisClient, config);

		// Override the calculated values with the exact serialized values
		(filter as any).size = metadata.size;
		(filter as any).hashFunctions = metadata.hashFunctions;

		// Create new bit array with correct size
		const byteSize = Math.ceil(metadata.size / 8);
		(filter as any).bitArray = new Uint8Array(byteSize);

		// Restore bit array state
		const restoredArray = new Uint8Array(parsed.bitArray);
		filter.bitArray.set(restoredArray);

		// Restore metadata
		filter.itemCount = metadata.itemCount;
		(filter as any).createdAt = metadata.createdAt;
		(filter as any).lastSavedAt = metadata.lastSavedAt;

		return filter;
	}

	/**
	 * Generate hash values for a domain using multiple hash functions
	 */
	private getHashes(domain: string): number[]
	{
		const hashes: number[] = [];

		// Use FNV-1a hash as base
		const hash1 = this.fnv1aHash(domain);
		const hash2 = this.djb2Hash(domain);

		// Generate multiple hash values using double hashing
		for (let i = 0; i < this.hashFunctions; i++)
		{
			const hash = (hash1 + i * hash2) >>> 0; // Ensure unsigned 32-bit
			hashes.push(hash);
		}

		return hashes;
	}

	/**
	 * FNV-1a hash function
	 */
	private fnv1aHash(str: string): number
	{
		let hash = 2166136261; // FNV offset basis

		for (let i = 0; i < str.length; i++)
		{
			hash ^= str.charCodeAt(i);
			hash = (hash * 16777619) >>> 0; // FNV prime, ensure unsigned 32-bit
		}

		return hash;
	}

	/**
	 * DJB2 hash function
	 */
	private djb2Hash(str: string): number
	{
		let hash = 5381;

		for (let i = 0; i < str.length; i++)
		{
			hash = ((hash << 5) + hash + str.charCodeAt(i)) >>> 0; // Ensure unsigned 32-bit
		}

		return hash;
	}

	// ===== REDIS PERSISTENCE METHODS =====

	/**
	 * Save bloom filter to Redis
	 */
	async saveToRedis(key?: string): Promise<void>
	{
		if (!this.redisClient || !this.config.enablePersistence)
		{
			this.logger.warn('Redis persistence not available or disabled');
			return;
		}

		const redisKey = key || `${this.config.redisKeyPrefix}main`;

		try
		{
			const serialized = this.serialize();
			await this.redisClient.set(redisKey, serialized);
			this.lastSavedAt = Date.now();

			this.logger.debug('BloomFilter saved to Redis', {
				key: redisKey,
				itemCount: this.itemCount,
				size: this.bitArray.length,
			});
		}
		catch (error)
		{
			this.logger.error('Failed to save BloomFilter to Redis', {
				key: redisKey,
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Load bloom filter from Redis
	 */
	async loadFromRedis(key?: string): Promise<boolean>
	{
		if (!this.redisClient || !this.config.enablePersistence)
		{
			this.logger.warn('Redis persistence not available or disabled');
			return false;
		}

		const redisKey = key || `${this.config.redisKeyPrefix}main`;

		try
		{
			const serialized = await this.redisClient.get<string>(redisKey);

			if (!serialized)
			{
				this.logger.debug('No BloomFilter found in Redis', { key: redisKey });
				return false;
			}

			const loaded = BloomFilter.deserialize(serialized, this.redisClient, this.config);

			// Copy loaded state to current instance
			this.bitArray.set(loaded.bitArray);
			this.itemCount = loaded.itemCount;
			(this as any).createdAt = (loaded as any).createdAt;
			this.lastSavedAt = (loaded as any).lastSavedAt;

			this.logger.info('BloomFilter loaded from Redis', {
				key: redisKey,
				itemCount: this.itemCount,
				age: Date.now() - this.createdAt,
			});

			return true;
		}
		catch (error)
		{
			this.logger.error('Failed to load BloomFilter from Redis', {
				key: redisKey,
				error: (error as Error).message,
			});
			return false;
		}
	}

	/**
	 * Delete bloom filter from Redis
	 */
	async deleteFromRedis(key?: string): Promise<void>
	{
		if (!this.redisClient || !this.config.enablePersistence)
		{
			return;
		}

		const redisKey = key || `${this.config.redisKeyPrefix}main`;

		try
		{
			await this.redisClient.del(redisKey);
			this.logger.debug('BloomFilter deleted from Redis', { key: redisKey });
		}
		catch (error)
		{
			this.logger.error('Failed to delete BloomFilter from Redis', {
				key: redisKey,
				error: (error as Error).message,
			});
		}
	}

	// ===== WARMING STRATEGIES =====

	/**
	 * Warm bloom filter from database snapshot
	 */
	async warmFromDatabaseSnapshot(
		getDomains: (offset: number, limit: number) => Promise<string[]>,
		totalEstimate?: number,
	): Promise<void>
	{
		if (!this.config.enableAutoWarming)
		{
			this.logger.debug('Auto warming disabled');
			return;
		}

		this.logger.info('Starting bloom filter warming from database snapshot', {
			batchSize: this.config.warmingBatchSize,
			totalEstimate,
		});

		let offset = 0;
		let totalWarmed = 0;
		const startTime = Date.now();

		try
		{
			while (true)
			{
				const domains = await getDomains(offset, this.config.warmingBatchSize);

				if (domains.length === 0)
				{
					break;
				}

				this.addBatch(domains);
				totalWarmed += domains.length;
				offset += this.config.warmingBatchSize;

				// Log progress periodically
				if (totalWarmed % (this.config.warmingBatchSize * 10) === 0)
				{
					this.logger.debug('Warming progress', {
						totalWarmed,
						currentBatch: domains.length,
						estimatedProgress: totalEstimate ? (totalWarmed / totalEstimate) * 100 : undefined,
					});
				}

				// Break if we got fewer domains than requested (end of data)
				if (domains.length < this.config.warmingBatchSize)
				{
					break;
				}
			}

			const duration = Date.now() - startTime;

			this.logger.info('Bloom filter warming completed', {
				totalWarmed,
				duration,
				bloomStats: this.getStats(),
			});

			// Save to Redis after warming
			if (this.config.enablePersistence)
			{
				await this.saveToRedis();
			}
		}
		catch (error)
		{
			this.logger.error('Bloom filter warming failed', {
				totalWarmed,
				error: (error as Error).message,
			});
			throw error;
		}
	}

	/**
	 * Warm bloom filter from multiple database sources
	 */
	async warmFromMultipleSources(
		sources: Array<{
			name: string;
			getDomains: (offset: number, limit: number) => Promise<string[]>;
			weight?: number;
		}>,
	): Promise<void>
	{
		this.logger.info('Starting bloom filter warming from multiple sources', {
			sources: sources.map(s => ({ name: s.name, weight: s.weight || 1 })),
		});

		const startTime = Date.now();
		let totalWarmed = 0;

		try
		{
			for (const source of sources)
			{
				const sourceStartTime = Date.now();
				let sourceWarmed = 0;

				this.logger.debug('Warming from source', { source: source.name });

				let offset = 0;

				while (true)
				{
					const domains = await source.getDomains(offset, this.config.warmingBatchSize);

					if (domains.length === 0)
					{
						break;
					}

					this.addBatch(domains);
					sourceWarmed += domains.length;
					totalWarmed += domains.length;
					offset += this.config.warmingBatchSize;

					if (domains.length < this.config.warmingBatchSize)
					{
						break;
					}
				}

				const sourceDuration = Date.now() - sourceStartTime;
				this.logger.info('Source warming completed', {
					source: source.name,
					sourceWarmed,
					sourceDuration,
				});
			}

			const totalDuration = Date.now() - startTime;

			this.logger.info('Multi-source warming completed', {
				totalWarmed,
				totalDuration,
				bloomStats: this.getStats(),
			});

			// Save to Redis after warming
			if (this.config.enablePersistence)
			{
				await this.saveToRedis();
			}
		}
		catch (error)
		{
			this.logger.error('Multi-source warming failed', {
				totalWarmed,
				error: (error as Error).message,
			});
			throw error;
		}
	}

	// ===== MAINTENANCE AND REBUILDING =====

	/**
	 * Check if bloom filter needs rebuilding
	 */
	shouldRebuild(): boolean
	{
		const age = Date.now() - this.createdAt;

		// Check age
		if (age > this.config.maxAge)
		{
			return true;
		}

		// Check false positive rate
		const estimatedFalsePositiveRate = (1 - Math.exp((-this.hashFunctions * this.itemCount) / this.size)) ** this.hashFunctions;

		if (estimatedFalsePositiveRate > this.config.rebuildThreshold)
		{
			return true;
		}

		return false;
	}

	/**
	 * Rebuild bloom filter with fresh data
	 */
	async rebuild(
		getDomains: (offset: number, limit: number) => Promise<string[]>,
		totalEstimate?: number,
	): Promise<void>
	{
		this.logger.info('Starting bloom filter rebuild', {
			reason: this.shouldRebuild() ? 'maintenance_threshold' : 'manual',
			currentStats: this.getStats(),
		});

		const oldStats = this.getStats();

		// Clear current filter
		this.clear();

		// Warm with fresh data
		await this.warmFromDatabaseSnapshot(getDomains, totalEstimate);

		const newStats = this.getStats();

		this.logger.info('Bloom filter rebuild completed', {
			oldStats: {
				itemCount: oldStats.itemCount,
				estimatedFalsePositiveRate: oldStats.estimatedFalsePositiveRate,
				age: oldStats.age,
			},
			newStats: {
				itemCount: newStats.itemCount,
				estimatedFalsePositiveRate: newStats.estimatedFalsePositiveRate,
				age: newStats.age,
			},
		});
	}

	/**
	 * Perform automatic maintenance if needed
	 */
	async performMaintenance(
		getDomains?: (offset: number, limit: number) => Promise<string[]>,
		totalEstimate?: number,
	): Promise<boolean>
	{
		if (!this.shouldRebuild())
		{
			return false;
		}

		if (!getDomains)
		{
			this.logger.warn('Maintenance needed but no data source provided');
			return false;
		}

		await this.rebuild(getDomains, totalEstimate);
		return true;
	}

	// ===== AUTO-SAVE FUNCTIONALITY =====

	/**
	 * Set up automatic saving to Redis
	 */
	private setupAutoSave(): void
	{
		if (this.autoSaveTimer)
		{
			clearInterval(this.autoSaveTimer);
		}

		this.autoSaveTimer = setInterval(async () =>
		{
			try
			{
				await this.saveToRedis();
			}
			catch (error)
			{
				this.logger.error('Auto-save failed', { error: (error as Error).message });
			}
		}, this.config.autoSaveInterval);

		this.logger.debug('Auto-save enabled', {
			interval: this.config.autoSaveInterval,
		});
	}

	/**
	 * Cleanup resources
	 */
	destroy(): void
	{
		if (this.autoSaveTimer)
		{
			clearInterval(this.autoSaveTimer);
			this.autoSaveTimer = undefined;
		}

		this.logger.debug('BloomFilter destroyed');
	}

	// ===== UTILITY METHODS =====

	/**
	 * Create a backup of the current bloom filter
	 */
	async createBackup(backupKey?: string): Promise<string>
	{
		if (!this.redisClient || !this.config.enablePersistence)
		{
			throw new Error('Redis persistence not available');
		}

		const timestamp = Date.now();
		const key = backupKey || `${this.config.redisKeyPrefix}backup:${timestamp}`;

		await this.saveToRedis(key);

		this.logger.info('Bloom filter backup created', {
			backupKey: key,
			itemCount: this.itemCount,
		});

		return key;
	}

	/**
	 * Restore from a backup
	 */
	async restoreFromBackup(backupKey: string): Promise<boolean>
	{
		const success = await this.loadFromRedis(backupKey);

		if (success)
		{
			this.logger.info('Bloom filter restored from backup', {
				backupKey,
				itemCount: this.itemCount,
			});
		}

		return success;
	}

	/**
	 * List available backups
	 */
	async listBackups(): Promise<string[]>
	{
		if (!this.redisClient)
		{
			return [];
		}

		try
		{
			const pattern = `${this.config.redisKeyPrefix}backup:*`;
			const keys = await this.redisClient.keys(pattern);
			return keys.sort();
		}
		catch (error)
		{
			this.logger.error('Failed to list backups', { error: (error as Error).message });
			return [];
		}
	}

	/**
	 * Get configuration
	 */
	getConfig(): BloomFilterConfig
	{
		return { ...this.config };
	}
}

export { DEFAULT_BLOOM_CONFIG };

export default BloomFilter;
