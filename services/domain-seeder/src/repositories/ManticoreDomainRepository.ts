import type ManticoreClient from '@shared/database/ManticoreClient';
import { logger } from '@shared/utils/Logger';
import type { DomainRepository, DomainRepositoryConfig, BatchCheckResult } from './DomainRepository';
import { DEFAULT_REPOSITORY_CONFIG } from './DomainRepository';

/**
 * Manticore Search implementation of domain repository for search index integration
 * Optimized for search-based domain existence checking and full-text search capabilities
 */
class ManticoreDomainRepository implements DomainRepository
{
	private readonly logger = logger.getLogger('ManticoreDomainRepository');

	private readonly config: DomainRepositoryConfig;

	private readonly indexName: string = 'domains_index';

	private queryCount = 0;

	private errorCount = 0;

	constructor(
		private readonly manticoreClient: ManticoreClient,
		config: Partial<DomainRepositoryConfig> = {},
	)
	{
		this.config = { ...DEFAULT_REPOSITORY_CONFIG, ...config };
	}

	/**
	 * Check if a single domain exists in Manticore Search index
	 */
	async hasDomain(domain: string): Promise<boolean>
	{
		try
		{
			const startTime = Date.now();

			this.queryCount++;
			const searchResult = await this.manticoreClient.searchDomains({
				query: domain,
				filters: { domain },
				limit: 1,
			});

			const executionTime = Date.now() - startTime;
			const found = searchResult.total > 0;

			this.logger.debug(`Single domain check completed in ${executionTime}ms`, {
				domain,
				found,
				searchTime: searchResult.took,
			});

			return found;
		}
		catch (error)
		{
			this.errorCount++;
			this.logger.error('Failed to check single domain existence in Manticore', {
				domain,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Check existence of multiple domains in batch using Manticore search
	 */
	async batchHasDomains(domains: string[]): Promise<Map<string, boolean>>
	{
		if (domains.length === 0)
		{
			return new Map();
		}

		const startTime = Date.now();
		const results = new Map<string, boolean>();
		const errors = new Map<string, Error>();

		try
		{
			// Process domains in batches to avoid query size limits
			for (let i = 0; i < domains.length; i += this.config.batchSize)
			{
				const batch = domains.slice(i, i + this.config.batchSize);
				const batchResults = await this.processBatch(batch);

				// Merge batch results
				batchResults.results.forEach((exists, domain) =>
				{
					results.set(domain, exists);
				});

				// Merge batch errors
				batchResults.errors.forEach((error, domain) =>
				{
					errors.set(domain, error);
				});
			}

			const executionTime = Date.now() - startTime;
			const foundCount = Array.from(results.values()).filter(Boolean).length;

			this.logger.info('Batch domain check completed', {
				totalDomains: domains.length,
				foundCount,
				errorCount: errors.size,
				executionTime,
				batchSize: this.config.batchSize,
			});

			return results;
		}
		catch (error)
		{
			this.errorCount++;
			this.logger.error('Batch domain check failed', {
				totalDomains: domains.length,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Process a single batch of domains using Manticore terms query
	 */
	private async processBatch(domains: string[]): Promise<BatchCheckResult>
	{
		const startTime = Date.now();
		const results = new Map<string, boolean>();
		const errors = new Map<string, Error>();

		try
		{
			// Initialize all domains as not found
			domains.forEach((domain) =>
			{
				results.set(domain, false);
			});

			// Use terms query for efficient batch checking in Manticore
			this.queryCount++;
			const searchResult = await this.manticoreClient.compareDomains(domains);

			// Mark found domains as true
			searchResult.results.forEach((result) =>
			{
				const domain = result.domain as string;
				if (results.has(domain))
				{
					results.set(domain, true);
				}
			});

			const executionTime = Date.now() - startTime;
			const foundCount = Array.from(results.values()).filter(Boolean).length;

			this.logger.debug('Batch processed successfully', {
				batchSize: domains.length,
				foundCount,
				executionTime,
				searchTime: searchResult.took || 0,
			});

			return {
				results,
				errors,
				totalProcessed: domains.length,
				foundCount,
				executionTime,
			};
		}
		catch (error)
		{
			this.errorCount++;
			this.logger.error('Batch processing failed', {
				batchSize: domains.length,
				error: error.message,
			});

			// Mark all domains in this batch as errors
			domains.forEach((domain) =>
			{
				errors.set(domain, error as Error);
				results.delete(domain);
			});

			return {
				results,
				errors,
				totalProcessed: 0,
				foundCount: 0,
				executionTime: Date.now() - startTime,
			};
		}
	}

	/**
	 * Health check for Manticore Search connection
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			const isHealthy = await this.manticoreClient.healthCheck();
			this.logger.debug('Manticore health check result', { isHealthy });
			return isHealthy;
		}
		catch (error)
		{
			this.logger.error('Manticore health check failed', { error: error.message });
			return false;
		}
	}

	/**
	 * Get repository type identifier
	 */
	getRepositoryType(): string
	{
		return 'manticore';
	}

	/**
	 * Get current configuration
	 */
	getConfig(): DomainRepositoryConfig
	{
		return { ...this.config };
	}

	/**
	 * Get performance metrics for monitoring
	 */
	async getMetrics(): Promise<{
		connectionStatus: boolean;
		lastQueryTime?: number;
		totalQueries: number;
		errorRate: number;
	}>
	{
		const connectionStatus = await this.healthCheck();

		return {
			connectionStatus,
			totalQueries: this.queryCount,
			errorRate: this.queryCount > 0 ? this.errorCount / this.queryCount : 0,
		};
	}

	/**
	 * Check if the domains index exists and is ready
	 */
	async isIndexReady(): Promise<boolean>
	{
		try
		{
			const exists = await this.manticoreClient.indexExists(this.indexName);
			this.logger.debug('Index existence check', { indexName: this.indexName, exists });
			return exists;
		}
		catch (error)
		{
			this.logger.error('Failed to check index existence', {
				indexName: this.indexName,
				error: error.message,
			});
			return false;
		}
	}

	/**
	 * Get search index statistics
	 */
	async getIndexStats(): Promise<{
		totalDocuments: number;
		indexSize: string;
		lastUpdated?: string;
	}>
	{
		try
		{
			// Use a match_all query to get total document count
			this.queryCount++;
			const searchResult = await this.manticoreClient.searchDomains({
				limit: 0, // We only want the count
			});

			return {
				totalDocuments: searchResult.total,
				indexSize: 'unknown', // Manticore doesn't provide size info via search API
				lastUpdated: new Date().toISOString(),
			};
		}
		catch (error)
		{
			this.logger.error('Failed to get index statistics', { error: error.message });
			return {
				totalDocuments: 0,
				indexSize: 'unknown',
			};
		}
	}

	/**
	 * Search for domains with advanced filtering capabilities
	 */
	async searchDomains(params: {
		query?: string;
		category?: string;
		country?: string;
		minRank?: number;
		maxRank?: number;
		limit?: number;
		offset?: number;
	}): Promise<{
		domains: string[];
		total: number;
		took: number;
	}>
	{
		try
		{
			const filters: Record<string, unknown> = {};

			if (params.category)
			{
				filters.category = params.category;
			}
			if (params.country)
			{
				filters.country = params.country;
			}
			if (params.minRank)
			{
				filters.min_rank = params.minRank;
			}
			if (params.maxRank)
			{
				filters.max_rank = params.maxRank;
			}

			this.queryCount++;
			const searchResult = await this.manticoreClient.searchDomains({
				query: params.query,
				filters,
				limit: params.limit || 50,
				offset: params.offset || 0,
			});

			const domains = searchResult.results.map(result => result.domain as string);

			return {
				domains,
				total: searchResult.total,
				took: searchResult.took,
			};
		}
		catch (error)
		{
			this.logger.error('Domain search failed', { error: error.message, params });
			throw error;
		}
	}
}

export default ManticoreDomainRepository;
