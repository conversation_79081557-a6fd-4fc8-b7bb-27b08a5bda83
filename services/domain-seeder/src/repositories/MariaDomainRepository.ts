import type Maria<PERSON><PERSON> from '@shared/database/MariaClient';
import type { RowDataPacket } from 'mysql2/promise';
import { logger } from '@shared';
import type {
	DomainRepository,
	DomainRepositoryConfig,
	BatchCheckResult,
} from './DomainRepository';
import { DEFAULT_REPOSITORY_CONFIG } from './DomainRepository';

/**
 * MariaDB implementation of domain repository with connection pooling and optimized queries
 * Handles MariaDB-specific query patterns and connection management
 */
class MariaDomainRepository implements DomainRepository
{
	private readonly logger = logger.getLogger('MariaDomainRepository');

	private readonly config: DomainRepositoryConfig;

	private queryCount = 0;

	private errorCount = 0;

	constructor(
		private readonly mariaClient: MariaClient,
		config: Partial<DomainRepositoryConfig> = {},
	)
	{
		this.config = { ...DEFAULT_REPOSITORY_CONFIG, ...config };
	}

	/**
	 * Check if a single domain exists in MariaDB
	 */
	async hasDomain(domain: string): Promise<boolean>
	{
		try
		{
			const startTime = Date.now();

			// Check multiple tables where domains might exist
			const queries = [
				'SELECT 1 FROM domain_whois WHERE domain = ? LIMIT 1',
				'SELECT 1 FROM domain_category_mapping WHERE domain = ? LIMIT 1',
				'SELECT 1 FROM backlinks WHERE target_domain = ? LIMIT 1',
			];

			// Use Promise.any to return as soon as any query finds the domain
			try
			{
				await Promise.any(queries.map(async (query) =>
				{
					this.queryCount++;
					const result = await this.mariaClient.execute<RowDataPacket[]>(query, [domain], {
						maxRetries: this.config.maxRetries,
					});

					if (result.rows.length > 0)
					{
						return true;
					}
					throw new Error('Domain not found in this table');
				}));

				const executionTime = Date.now() - startTime;
				this.logger.debug(`Single domain check completed in ${executionTime}ms`, {
					domain,
					found: true,
				});

				return true;
			}
			catch (aggregateError)
			{
				// All queries failed to find the domain
				const executionTime = Date.now() - startTime;
				this.logger.debug(`Single domain check completed in ${executionTime}ms`, {
					domain,
					found: false,
				});

				return false;
			}
		}
		catch (error)
		{
			this.errorCount++;
			this.logger.error('Failed to check single domain existence in MariaDB', {
				domain,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Check existence of multiple domains in batch with MariaDB optimization
	 */
	async batchHasDomains(domains: string[]): Promise<Map<string, boolean>>
	{
		if (domains.length === 0)
		{
			return new Map();
		}

		const startTime = Date.now();
		const results = new Map<string, boolean>();
		const errors = new Map<string, Error>();

		try
		{
			// Process domains in batches to avoid query size limits
			for (let i = 0; i < domains.length; i += this.config.batchSize)
			{
				const batch = domains.slice(i, i + this.config.batchSize);
				const batchResults = await this.processBatch(batch);

				// Merge batch results
				batchResults.results.forEach((exists, domain) =>
				{
					results.set(domain, exists);
				});

				// Merge batch errors
				batchResults.errors.forEach((error, domain) =>
				{
					errors.set(domain, error);
				});
			}

			const executionTime = Date.now() - startTime;
			const foundCount = Array.from(results.values()).filter(Boolean).length;

			this.logger.info('Batch domain check completed', {
				totalDomains: domains.length,
				foundCount,
				errorCount: errors.size,
				executionTime,
				batchSize: this.config.batchSize,
			});

			return results;
		}
		catch (error)
		{
			this.errorCount++;
			this.logger.error('Batch domain check failed', {
				totalDomains: domains.length,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Process a single batch of domains with MariaDB UNION query optimization
	 */
	private async processBatch(domains: string[]): Promise<BatchCheckResult>
	{
		const startTime = Date.now();
		const results = new Map<string, boolean>();
		const errors = new Map<string, Error>();

		try
		{
			// Initialize all domains as not found
			domains.forEach((domain) =>
			{
				results.set(domain, false);
			});

			// Use UNION query to check multiple tables efficiently
			const placeholders = domains.map(() => '?').join(',');
			const query = `
				SELECT DISTINCT domain FROM (
					SELECT domain FROM domain_whois WHERE domain IN (${placeholders})
					UNION
					SELECT domain FROM domain_category_mapping WHERE domain IN (${placeholders})
					UNION
					SELECT target_domain as domain FROM backlinks WHERE target_domain IN (${placeholders})
				) AS found_domains
			`;

			// Prepare parameters (domains repeated for each UNION clause)
			const queryParams = [...domains, ...domains, ...domains];

			this.queryCount += 1;
			const result = await this.mariaClient.execute<RowDataPacket[]>(
				query,
				queryParams,
				{ maxRetries: this.config.maxRetries },
			);

			// Mark found domains as true
			result.rows.forEach((row) =>
			{
				const domain = row.domain as string;
				if (results.has(domain))
				{
					results.set(domain, true);
				}
			});

			const executionTime = Date.now() - startTime;
			const foundCount = Array.from(results.values()).filter(Boolean).length;

			this.logger.debug('Batch processed successfully', {
				batchSize: domains.length,
				foundCount,
				executionTime,
			});

			return ({
				results,
				errors,
				totalProcessed: domains.length,
				foundCount,
				executionTime,
			});
		}
		catch (error)
		{
			this.errorCount += 1;
			this.logger.error('Batch processing failed', {
				batchSize: domains.length,
				error: error.message,
			});

			// Mark all domains in this batch as errors
			domains.forEach((domain) =>
			{
				errors.set(domain, error as Error);
				results.delete(domain);
			});

			return ({
				results,
				errors,
				totalProcessed: 0,
				foundCount: 0,
				executionTime: Date.now() - startTime,
			});
		}
	}

	/**
	 * Health check for MariaDB connection
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			const isHealthy = await this.mariaClient.healthCheck();
			this.logger.debug('MariaDB health check result', { isHealthy });
			return isHealthy;
		}
		catch (error)
		{
			this.logger.error('MariaDB health check failed', { error: error.message });
			return false;
		}
	}

	/**
	 * Get repository type identifier
	 */
	getRepositoryType(): string
	{
		return 'maria';
	}

	/**
	 * Get current configuration
	 */
	getConfig(): DomainRepositoryConfig
	{
		return ({ ...this.config });
	}

	/**
	 * Get performance metrics for monitoring
	 */
	async getMetrics(): Promise<{
		connectionStatus: boolean;
		lastQueryTime?: number;
		totalQueries: number;
		errorRate: number;
	}>
	{
		const connectionStatus = await this.healthCheck();

		return ({
			connectionStatus,
			totalQueries: this.queryCount,
			errorRate: this.queryCount > 0 ? this.errorCount / this.queryCount : 0,
		});
	}

	/**
	 * Get domain statistics from MariaDB tables
	 */
	async getDomainStats(): Promise<{
		whoisRecords: number;
		categoryMappings: number;
		backlinks: number;
	}>
	{
		try
		{
			this.queryCount += 3; // Three queries in parallel
			const [whoisResult, categoryResult, backlinkResult] = await Promise.all([
				this.mariaClient.execute<RowDataPacket[]>('SELECT COUNT(*) as count FROM domain_whois'),
				this.mariaClient.execute<RowDataPacket[]>('SELECT COUNT(DISTINCT domain) as count FROM domain_category_mapping'),
				this.mariaClient.execute<RowDataPacket[]>('SELECT COUNT(DISTINCT target_domain) as count FROM backlinks'),
			]);

			return ({
				whoisRecords: whoisResult.rows[0]?.count || 0,
				categoryMappings: categoryResult.rows[0]?.count || 0,
				backlinks: backlinkResult.rows[0]?.count || 0,
			});
		}
		catch (error)
		{
			this.logger.error('Failed to get domain statistics', { error: error.message });
			return ({
				whoisRecords: 0,
				categoryMappings: 0,
				backlinks: 0,
			});
		}
	}
}

export default MariaDomainRepository;
