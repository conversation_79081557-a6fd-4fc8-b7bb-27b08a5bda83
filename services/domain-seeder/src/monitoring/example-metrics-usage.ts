/**
 * Example usage of PrometheusMetricsCollector
 * This file demonstrates how to integrate the metrics collector
 * into various parts of the domain-seeder service
 */

import RedisClientWrapper from '@shared/database/RedisClient';
import PrometheusMetricsCollector from './PrometheusMetricsCollector';

// Initialize the metrics collector
const redisClient = new RedisClientWrapper();
const metricsCollector = new PrometheusMetricsCollector(redisClient);

/**
 * Example: Domain Processing Pipeline with Metrics
 */
async function exampleDomainProcessingPipeline()
{
	const source = 'tranco';
	const strategy = 'differential';

	try
	{
		// 1. Record candidates fetched from source
		const candidatesFetched = 1000;
		metricsCollector.recordCandidatesFetched(source, candidatesFetched);

		// 2. Simulate normalization process
		const startNormalization = Date.now();
		// ... normalization logic here ...
		const normalizedCandidates = 950; // Some candidates filtered out
		const normalizationTime = Date.now() - startNormalization;

		metricsCollector.recordCandidatesAfterNormalize(normalizedCandidates, source);
		metricsCollector.recordPipelineStage('normalization', normalizationTime, true, normalizedCandidates);

		// 3. Check database existence
		const startDbCheck = Date.now();
		// ... database check logic here ...
		const knownDomains = 800;
		const newDomains = normalizedCandidates - knownDomains;
		const dbCheckTime = Date.now() - startDbCheck;

		metricsCollector.recordKnownInDatabase(knownDomains, source);
		metricsCollector.recordDatabaseCheckLatency(dbCheckTime, 'scylla', 'batch_check');

		// 4. Record discoveries
		metricsCollector.recordNewDiscovered(newDomains, source, strategy);

		// 5. Record strategy execution metrics
		const avgConfidence = 0.85;
		const totalProcessingTime = Date.now() - startNormalization;
		metricsCollector.recordStrategyExecution(
			source,
			strategy,
			candidatesFetched,
			newDomains,
			avgConfidence,
			totalProcessingTime,
		);

		// 6. Record individual confidence scores
		for (let i = 0; i < newDomains; i++)
		{
			const confidence = 0.5 + Math.random() * 0.5; // Random confidence between 0.5-1.0
			metricsCollector.recordConfidenceDistribution(confidence, source, strategy);
		}

		console.log(`Processed ${candidatesFetched} candidates, discovered ${newDomains} new domains`);
	}
	catch (error)
	{
		// Record error in strategy execution
		metricsCollector.recordStrategyExecution(
			source,
			strategy,
			0,
			0,
			0,
			0,
			error.message,
		);
		throw error;
	}
}

/**
 * Example: Content Generation with Metrics
 */
async function exampleContentGeneration()
{
	const domains = ['example.com', 'test.org', 'sample.net'];
	const generatorVersion = 'v1.2.3';

	for (const domain of domains)
	{
		try
		{
			// Generate content
			const content = await generateDomainContent(domain);

			// Record successful content generation
			metricsCollector.recordContentGenerated(1, 'self', generatorVersion);

			// Record SEO summary length
			metricsCollector.recordSeoSummaryLength(content.summary.length, 'generated');

			// Validate content
			const isValid = validateContent(content);
			if (!isValid)
			{
				metricsCollector.recordContentValidationFailures(1, 'schema_validation');
			}
		}
		catch (error)
		{
			metricsCollector.recordContentValidationFailures(1, 'generation_error');
		}
	}
}

/**
 * Example: Enqueue Process with Metrics
 */
async function exampleEnqueueProcess()
{
	const domains = ['new1.com', 'new2.org', 'new3.net'];
	const queueName = 'new:domains';

	// Record current queue depth
	const currentQueueDepth = await getQueueDepth(queueName);
	metricsCollector.recordQueueDepth(currentQueueDepth, queueName);

	// Check if we should rate limit
	if (currentQueueDepth > 10000)
	{
		metricsCollector.recordRateLimited(domains.length, 'queue_depth_exceeded');
		console.log('Rate limited due to high queue depth');
		return;
	}

	// Attempt to enqueue domains
	metricsCollector.recordEnqueueAttempts(domains.length, queueName);

	let successCount = 0;
	for (const domain of domains)
	{
		try
		{
			await enqueueDomain(domain, queueName);
			successCount++;
		}
		catch (error)
		{
			console.error(`Failed to enqueue ${domain}:`, error);
		}
	}

	metricsCollector.recordEnqueueSuccess(successCount, queueName);

	// Record final queue depth
	const finalQueueDepth = await getQueueDepth(queueName);
	metricsCollector.recordQueueDepth(finalQueueDepth, queueName);
}

/**
 * Example: Batch Processing with Metrics
 */
async function exampleBatchProcessing()
{
	const batchSize = 1000;
	const operation = 'domain_existence_check';

	const startTime = Date.now();
	let successCount = 0;
	let errorCount = 0;

	try
	{
		// Simulate batch processing
		for (let i = 0; i < batchSize; i++)
		{
			try
			{
				// Process individual item
				await processItem(i);
				successCount++;
			}
			catch (error)
			{
				errorCount++;
			}
		}
	}
	finally
	{
		const processingTime = Date.now() - startTime;

		// Record batch processing metrics
		metricsCollector.recordBatchProcessing(
			operation,
			batchSize,
			processingTime,
			successCount,
			errorCount,
		);
	}
}

/**
 * Example: System Monitoring
 */
async function exampleSystemMonitoring()
{
	// Record system metrics periodically
	setInterval(() =>
	{
		// Record memory usage
		metricsCollector.recordMemoryUsage();

		// Record service uptime
		metricsCollector.recordServiceUptime();

		// Record queue depths for all queues
		const queues = ['new:domains', 'new:domains:high', 'failed:domains'];
		queues.forEach(async (queue) =>
		{
			const depth = await getQueueDepth(queue);
			metricsCollector.recordQueueDepth(depth, queue);
		});

		// Record source staleness
		const sources = ['tranco', 'radar', 'umbrella', 'czds'];
		sources.forEach(async (source) =>
		{
			const staleness = await getSourceStaleness(source);
			metricsCollector.recordSourceStaleness(staleness, source);
		});
	}, 30000); // Every 30 seconds
}

/**
 * Example: HTTP Metrics Endpoint
 */
async function exampleMetricsEndpoint(req: any, res: any)
{
	try
	{
		// Generate Prometheus format metrics
		const prometheusMetrics = await metricsCollector.getPrometheusMetrics();

		res.setHeader('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
		res.send(prometheusMetrics);
	}
	catch (error)
	{
		console.error('Failed to generate metrics:', error);
		res.status(500).send('Internal Server Error');
	}
}

/**
 * Example: Metrics Summary for Monitoring Dashboard
 */
async function exampleMetricsSummary()
{
	try
	{
		const summary = await metricsCollector.getMetricsSummary();

		console.log('=== Metrics Summary ===');
		console.log(`Total Metrics: ${summary.totalMetrics}`);
		console.log(`Time Range: ${new Date(summary.timeRange.start)} - ${new Date(summary.timeRange.end)}`);

		// Display key metrics
		const keyMetrics = [
			'candidates_fetched_total',
			'new_discovered_total',
			'content_generated_total',
			'enqueue_success_total',
			'queue_depth',
		];

		keyMetrics.forEach((metricName) =>
		{
			const metric = summary.metrics[metricName];
			if (metric)
			{
				console.log(`${metricName}: ${metric.latest} (count: ${metric.count}, avg: ${metric.avg.toFixed(2)})`);
			}
		});
	}
	catch (error)
	{
		console.error('Failed to get metrics summary:', error);
	}
}

/**
 * Example: Cleanup and Shutdown
 */
async function exampleCleanupAndShutdown()
{
	try
	{
		// Clean up old metrics (older than 7 days)
		const cleanedCount = await metricsCollector.cleanupOldMetrics(7);
		console.log(`Cleaned up ${cleanedCount} old metrics`);

		// Flush any remaining metrics
		await metricsCollector.flush();

		// Stop the metrics collector
		await metricsCollector.stop();

		console.log('Metrics collector stopped gracefully');
	}
	catch (error)
	{
		console.error('Error during cleanup:', error);
	}
}

// Mock functions for examples
async function generateDomainContent(domain: string): Promise<{ summary: string }>
{
	return { summary: `Generated content for ${domain}. This is a sample summary that would be much longer in real usage.` };
}

function validateContent(content: any): boolean
{
	return content && content.summary && content.summary.length > 50;
}

// Simulate realistic queue depths based on time of day
const queueDepths = new Map<string, number>();

async function getQueueDepth(queueName: string): Promise<number>
{
	const currentDepth = queueDepths.get(queueName) || Math.floor(Math.random() * 1000);

	// Simulate queue depth changes over time
	const change = Math.floor(Math.random() * 200) - 100; // -100 to +100
	const newDepth = Math.max(0, currentDepth + change);

	queueDepths.set(queueName, newDepth);
	return newDepth;
}

// Track enqueue success rates
let enqueueSuccessCount = 0;
let enqueueTotalCount = 0;

async function enqueueDomain(domain: string, queueName: string): Promise<void>
{
	enqueueTotalCount++;

	// Simulate realistic failure scenarios
	const failureRate = queueName === 'high-priority' ? 0.02 : 0.05; // 2% vs 5% failure

	if (Math.random() < failureRate)
	{
		throw new Error(`Enqueue failed for ${domain}: Queue ${queueName} temporarily unavailable`);
	}

	enqueueSuccessCount++;

	// Update queue depth
	const currentDepth = queueDepths.get(queueName) || 0;
	queueDepths.set(queueName, currentDepth + 1);
}

// Track processing metrics
const processingMetrics = {
	successCount: 0,
	failureCount: 0,
	totalProcessingTime: 0,
};

async function processItem(index: number): Promise<void>
{
	const startTime = Date.now();

	// Simulate processing time (50-500ms)
	const processingTime = 50 + Math.random() * 450;
	await new Promise(resolve => setTimeout(resolve, processingTime));

	processingMetrics.totalProcessingTime += processingTime;

	// Simulate realistic failure scenarios
	const failureRate = 0.08; // 8% failure rate

	if (Math.random() < failureRate)
	{
		processingMetrics.failureCount++;
		throw new Error(`Processing failed for item ${index}: Validation error`);
	}

	processingMetrics.successCount++;
}

// Track source staleness with realistic patterns
const sourceLastUpdate = new Map<string, number>();

async function getSourceStaleness(source: string): Promise<number>
{
	const now = Date.now();

	// Initialize if not exists
	if (!sourceLastUpdate.has(source))
	{
		// Simulate different update frequencies for different sources
		const updateFrequencies = {
			tranco: 24 * 60 * 60 * 1000, // Daily
			radar: 6 * 60 * 60 * 1000, // Every 6 hours
			umbrella: 12 * 60 * 60 * 1000, // Every 12 hours
			czds: 7 * 24 * 60 * 60 * 1000, // Weekly
		};

		const frequency = updateFrequencies[source] || 24 * 60 * 60 * 1000;
		const lastUpdate = now - Math.random() * frequency;
		sourceLastUpdate.set(source, lastUpdate);
	}

	const lastUpdate = sourceLastUpdate.get(source)!;
	const staleness = Math.floor((now - lastUpdate) / 1000); // Convert to seconds

	// Occasionally simulate source updates
	if (Math.random() < 0.01) // 1% chance of update
	{
		sourceLastUpdate.set(source, now);
		return 0; // Fresh update
	}

	return staleness;
}

// Export the metrics collector for use in other modules
export default metricsCollector;

// Export example functions for testing
export {
	exampleDomainProcessingPipeline,
	exampleContentGeneration,
	exampleEnqueueProcess,
	exampleBatchProcessing,
	exampleSystemMonitoring,
	exampleMetricsEndpoint,
	exampleMetricsSummary,
	exampleCleanupAndShutdown,
};
