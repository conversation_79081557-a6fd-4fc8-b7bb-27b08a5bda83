import type { RedisClientWrapper, DatabaseManager } from '@shared';
import { logger as sharedLogger } from '@shared';
import type { DiscoveredDomain, DiscoveryStrategy } from '../interfaces/DiscoveryEngine';
// import type { DomainCandidate } from '../interfaces/SourceConnector';

const logger = sharedLogger.getLogger('ProvenanceTracker');

interface ProvenanceRecord
{
	domain: string;
	firstSeenAt: string;
	sources: string[];
	discoveryStrategy: DiscoveryStrategy;
	confidence: number;
	discoveryReason: string;
	preGeneratedContent?: boolean;
	metadata: {
		rank?: number;
		originalSource: string;
		enqueuedAt?: string;
		messageId?: string;
		priority?: 'high' | 'normal';
		[key: string]: any;
	};
}

interface DiscoveryStatistics
{
	totalDiscovered: number;
	bySource: Record<string, number>;
	byStrategy: Record<DiscoveryStrategy, number>;
	byConfidenceRange: {
		high: number; // 0.8+
		medium: number; // 0.6-0.8
		low: number; // <0.6
	};
	averageConfidence: number;
	timeRange: {
		start: string;
		end: string;
	};
}

interface ProvenanceQuery
{
	domain?: string;
	source?: string;
	strategy?: DiscoveryStrategy;
	confidenceMin?: number;
	confidenceMax?: number;
	dateFrom?: string;
	dateTo?: string;
	limit?: number;
	offset?: number;
}

interface ProvenanceConfig
{
	ttlSeconds: number; // TTL for Redis storage
	batchSize: number; // Batch size for database operations
	enableScyllaStorage: boolean; // Whether to store in ScyllaDB
	enableStatisticsAggregation: boolean; // Whether to maintain statistics
	statisticsWindowDays: number; // Rolling window for statistics
}

/**
 * Comprehensive provenance tracking system for domain discovery audit trails
 * Implements lightweight metadata storage, strategy tracking, and statistics aggregation
 */
class ProvenanceTracker
{
	private readonly redis: RedisClientWrapper;

	private readonly dbManager: DatabaseManager;

	private readonly config: ProvenanceConfig;

	private readonly statisticsCache = new Map<string, DiscoveryStatistics>();

	constructor(
		redis: RedisClientWrapper,
		dbManager: DatabaseManager,
		config: Partial<ProvenanceConfig> = {},
	)
	{
		this.redis = redis;
		this.dbManager = dbManager;

		this.config = {
			ttlSeconds: config.ttlSeconds || 2592000, // 30 days
			batchSize: config.batchSize || 1000,
			enableScyllaStorage: config.enableScyllaStorage ?? true,
			enableStatisticsAggregation: config.enableStatisticsAggregation ?? true,
			statisticsWindowDays: config.statisticsWindowDays || 7,
		};

		logger.info('ProvenanceTracker initialized', {
			config: this.config,
		});
	}

	/**
	 * Record discovery provenance for a single domain
	 */
	async recordDiscovery(
		discoveredDomain: DiscoveredDomain,
		additionalMetadata: Record<string, any> = {},
	): Promise<void>
	{
		const provenanceRecord: ProvenanceRecord = {
			domain: discoveredDomain.domain,
			firstSeenAt: new Date().toISOString(),
			sources: [discoveredDomain.source],
			discoveryStrategy: discoveredDomain.discoveryStrategy,
			confidence: discoveredDomain.confidence,
			discoveryReason: discoveredDomain.discoveryReason,
			metadata: {
				rank: discoveredDomain.rank,
				originalSource: discoveredDomain.source,
				...discoveredDomain.metadata,
				...additionalMetadata,
			},
		};

		await this.storeProvenanceRecord(provenanceRecord);

		// Update statistics if enabled
		if (this.config.enableStatisticsAggregation)
		{
			await this.updateStatistics(provenanceRecord);
		}

		logger.debug('Discovery provenance recorded', {
			domain: discoveredDomain.domain,
			strategy: discoveredDomain.discoveryStrategy,
			confidence: discoveredDomain.confidence,
		});
	}

	/**
	 * Record discovery provenance for multiple domains in batch
	 */
	async recordDiscoveryBatch(
		discoveredDomains: DiscoveredDomain[],
		additionalMetadata: Record<string, any> = {},
	): Promise<void>
	{
		if (discoveredDomains.length === 0)
		{
			return;
		}

		const provenanceRecords: ProvenanceRecord[] = discoveredDomains.map(domain => ({
			domain: domain.domain,
			firstSeenAt: new Date().toISOString(),
			sources: [domain.source],
			discoveryStrategy: domain.discoveryStrategy,
			confidence: domain.confidence,
			discoveryReason: domain.discoveryReason,
			metadata: {
				rank: domain.rank,
				originalSource: domain.source,
				...domain.metadata,
				...additionalMetadata,
			},
		}));

		// Process in batches
		const batches = this.createBatches(provenanceRecords, this.config.batchSize);

		for (const batch of batches)
		{
			await this.storeProvenanceRecordBatch(batch);

			// Update statistics if enabled
			if (this.config.enableStatisticsAggregation)
			{
				for (const record of batch)
				{
					await this.updateStatistics(record);
				}
			}
		}

		logger.info('Discovery provenance batch recorded', {
			domainCount: discoveredDomains.length,
			batchCount: batches.length,
		});
	}

	/**
	 * Update provenance record when domain is enqueued
	 */
	async recordEnqueue(
		domain: string,
		messageId: string,
		priority: 'high' | 'normal' = 'normal',
		preGeneratedContent = true,
	): Promise<void>
	{
		try
		{
			const existingRecord = await this.getProvenanceRecord(domain);
			if (!existingRecord)
			{
				logger.warn('No existing provenance record found for enqueued domain', {
					domain,
				});
				return;
			}

			// Update the record with enqueue information
			const updatedRecord: ProvenanceRecord = {
				...existingRecord,
				preGeneratedContent,
				metadata: {
					...existingRecord.metadata,
					enqueuedAt: new Date().toISOString(),
					messageId,
					priority,
				},
			};

			await this.storeProvenanceRecord(updatedRecord);

			logger.debug('Provenance record updated with enqueue information', {
				domain,
				messageId,
				priority,
			});
		}
		catch (error)
		{
			logger.error('Failed to record enqueue provenance', {
				domain,
				error: error.message,
			});
		}
	}

	/**
	 * Get provenance record for a domain
	 */
	async getProvenanceRecord(domain: string): Promise<ProvenanceRecord | null>
	{
		try
		{
			const provenanceKey = `provenance:${domain}`;
			const data = await this.redis.get(provenanceKey);

			if (!data)
			{
				// Try ScyllaDB if Redis doesn't have it
				if (this.config.enableScyllaStorage)
				{
					return await this.getProvenanceFromScylla(domain);
				}
				return null;
			}

			return JSON.parse(data) as ProvenanceRecord;
		}
		catch (error)
		{
			logger.error('Failed to get provenance record', {
				domain,
				error: error.message,
			});
			return null;
		}
	}

	/**
	 * Query provenance records with filters
	 */
	async queryProvenance(query: ProvenanceQuery): Promise<ProvenanceRecord[]>
	{
		try
		{
			// If querying by domain, use direct lookup
			if (query.domain)
			{
				const record = await this.getProvenanceRecord(query.domain);
				return record ? [record] : [];
			}

			// For complex queries, use ScyllaDB if available
			if (this.config.enableScyllaStorage)
			{
				return await this.queryProvenanceFromScylla(query);
			}

			// Fallback to Redis scan (less efficient)
			return await this.queryProvenanceFromRedis(query);
		}
		catch (error)
		{
			logger.error('Failed to query provenance records', {
				query,
				error: error.message,
			});
			return [];
		}
	}

	/**
	 * Get discovery statistics for a time range
	 */
	async getDiscoveryStatistics(
		dateFrom?: string,
		dateTo?: string,
	): Promise<DiscoveryStatistics>
	{
		const cacheKey = `stats:${dateFrom || 'all'}:${dateTo || 'all'}`;

		// Check cache first
		if (this.statisticsCache.has(cacheKey))
		{
			const cached = this.statisticsCache.get(cacheKey)!;
			// Return cached if less than 5 minutes old
			const cacheAge = Date.now() - new Date(cached.timeRange.end).getTime();
			if (cacheAge < 300000) // 5 minutes
			{
				return cached;
			}
		}

		try
		{
			const statistics = await this.calculateStatistics(dateFrom, dateTo);
			this.statisticsCache.set(cacheKey, statistics);
			return statistics;
		}
		catch (error)
		{
			logger.error('Failed to get discovery statistics', {
				dateFrom,
				dateTo,
				error: error.message,
			});

			// Return empty statistics on error
			return {
				totalDiscovered: 0,
				bySource: {},
				byStrategy: {} as Record<DiscoveryStrategy, number>,
				byConfidenceRange: { high: 0, medium: 0, low: 0 },
				averageConfidence: 0,
				timeRange: {
					start: dateFrom || new Date().toISOString(),
					end: dateTo || new Date().toISOString(),
				},
			};
		}
	}

	/**
	 * Get aggregated statistics by source
	 */
	async getSourceStatistics(
		source: string,
		dateFrom?: string,
		dateTo?: string,
	): Promise<{
		totalDiscovered: number;
		byStrategy: Record<DiscoveryStrategy, number>;
		averageConfidence: number;
		confidenceDistribution: Record<string, number>;
	}>
	{
		try
		{
			const query: ProvenanceQuery = {
				source,
				dateFrom,
				dateTo,
				limit: 10000, // Large limit for statistics
			};

			const records = await this.queryProvenance(query);

			const statistics = {
				totalDiscovered: records.length,
				byStrategy: {} as Record<DiscoveryStrategy, number>,
				averageConfidence: 0,
				confidenceDistribution: {} as Record<string, number>,
			};

			if (records.length === 0)
			{
				return statistics;
			}

			// Calculate strategy breakdown
			for (const record of records)
			{
				statistics.byStrategy[record.discoveryStrategy] =
					(statistics.byStrategy[record.discoveryStrategy] || 0) + 1;
			}

			// Calculate confidence statistics
			const confidences = records.map(r => r.confidence);
			statistics.averageConfidence =
				confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;

			// Confidence distribution
			for (const confidence of confidences)
			{
				const range = confidence >= 0.8 ? 'high' : confidence >= 0.6 ? 'medium' : 'low';
				statistics.confidenceDistribution[range] =
					(statistics.confidenceDistribution[range] || 0) + 1;
			}

			return statistics;
		}
		catch (error)
		{
			logger.error('Failed to get source statistics', {
				source,
				error: error.message,
			});
			return {
				totalDiscovered: 0,
				byStrategy: {} as Record<DiscoveryStrategy, number>,
				averageConfidence: 0,
				confidenceDistribution: {},
			};
		}
	}

	/**
	 * Clean up old provenance records
	 */
	async cleanup(olderThanDays: number = 30): Promise<number>
	{
		try
		{
			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
			const cutoffIso = cutoffDate.toISOString();

			let cleanedCount = 0;

			// Clean up Redis records
			const pattern = 'provenance:*';
			const keys = await this.redis.keys(pattern);

			for (const key of keys)
			{
				try
				{
					const data = await this.redis.get(key);
					if (data)
					{
						const record = JSON.parse(data) as ProvenanceRecord;
						if (record.firstSeenAt < cutoffIso)
						{
							await this.redis.del(key);
							cleanedCount++;
						}
					}
				}
				catch (error)
				{
					// Skip invalid records
					await this.redis.del(key);
					cleanedCount++;
				}
			}

			// Clean up ScyllaDB records if enabled
			if (this.config.enableScyllaStorage)
			{
				const scyllaCleanedCount = await this.cleanupScyllaProvenance(cutoffIso);
				cleanedCount += scyllaCleanedCount;
			}

			logger.info('Provenance cleanup completed', {
				cleanedCount,
				olderThanDays,
				cutoffDate: cutoffIso,
			});

			return cleanedCount;
		}
		catch (error)
		{
			logger.error('Failed to cleanup provenance records', {
				error: error.message,
			});
			return 0;
		}
	}

	/**
	 * Get health status of provenance tracking
	 */
	async getHealthStatus(): Promise<{
		isHealthy: boolean;
		redisConnected: boolean;
		scyllaConnected: boolean;
		recordCount: number;
		oldestRecord?: string;
		newestRecord?: string;
	}>
	{
		try
		{
			const redisConnected = await this.redis.ping();
			let scyllaConnected = false;
			let recordCount = 0;

			if (this.config.enableScyllaStorage)
			{
				try
				{
					const scyllaClient = this.dbManager.getScyllaClient();
					await scyllaClient.execute('SELECT NOW() FROM system.local');
					scyllaConnected = true;
				}
				catch (error)
				{
					logger.warn('ScyllaDB health check failed', { error: error.message });
				}
			}

			// Get approximate record count from Redis
			const keys = await this.redis.keys('provenance:*');
			recordCount = keys.length;

			// Get oldest and newest records for monitoring
			let oldestRecord: string | undefined;
			let newestRecord: string | undefined;

			if (keys.length > 0)
			{
				const sampleKeys = keys.slice(0, Math.min(100, keys.length));
				const records: ProvenanceRecord[] = [];

				for (const key of sampleKeys)
				{
					try
					{
						const data = await this.redis.get(key);
						if (data)
						{
							records.push(JSON.parse(data));
						}
					}
					catch (error)
					{
						// Skip invalid records
					}
				}

				if (records.length > 0)
				{
					records.sort((a, b) => a.firstSeenAt.localeCompare(b.firstSeenAt));
					oldestRecord = records[0].firstSeenAt;
					newestRecord = records[records.length - 1].firstSeenAt;
				}
			}

			const isHealthy = redisConnected && (!this.config.enableScyllaStorage || scyllaConnected);

			return {
				isHealthy,
				redisConnected,
				scyllaConnected,
				recordCount,
				oldestRecord,
				newestRecord,
			};
		}
		catch (error)
		{
			logger.error('Failed to get provenance health status', {
				error: error.message,
			});

			return {
				isHealthy: false,
				redisConnected: false,
				scyllaConnected: false,
				recordCount: 0,
			};
		}
	}

	private async storeProvenanceRecord(record: ProvenanceRecord): Promise<void>
	{
		const provenanceKey = `provenance:${record.domain}`;

		try
		{
			// Store in Redis with TTL
			await this.redis.setex(
				provenanceKey,
				this.config.ttlSeconds,
				JSON.stringify(record),
			);
		}
		catch (error)
		{
			logger.error('Failed to store provenance in Redis', {
				domain: record.domain,
				error: error.message,
			});
			// Continue to try ScyllaDB even if Redis fails
		}

		// Store in ScyllaDB if enabled
		if (this.config.enableScyllaStorage)
		{
			await this.storeProvenanceInScylla(record);
		}
	}

	private async storeProvenanceRecordBatch(records: ProvenanceRecord[]): Promise<void>
	{
		// Batch Redis operations
		const pipeline = this.redis.pipeline();
		for (const record of records)
		{
			const provenanceKey = `provenance:${record.domain}`;
			pipeline.setex(provenanceKey, this.config.ttlSeconds, JSON.stringify(record));
		}
		await pipeline.exec();

		// Batch ScyllaDB operations if enabled
		if (this.config.enableScyllaStorage)
		{
			await this.storeProvenanceBatchInScylla(records);
		}
	}

	private async storeProvenanceInScylla(record: ProvenanceRecord): Promise<void>
	{
		try
		{
			const scyllaClient = this.dbManager.getScyllaClient();

			const query = `
				INSERT INTO domain_discovery (
					domain, first_seen_at, sources, discovery_strategy,
					confidence, discovery_reason, pre_generated_content, metadata
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
			`;

			await scyllaClient.execute(query, [
				record.domain,
				record.firstSeenAt,
				record.sources,
				record.discoveryStrategy,
				record.confidence,
				record.discoveryReason,
				record.preGeneratedContent || false,
				JSON.stringify(record.metadata),
			]);
		}
		catch (error)
		{
			logger.error('Failed to store provenance in ScyllaDB', {
				domain: record.domain,
				error: error.message,
			});
		}
	}

	private async storeProvenanceBatchInScylla(records: ProvenanceRecord[]): Promise<void>
	{
		try
		{
			const scyllaClient = this.dbManager.getScyllaClient();

			const query = `
				INSERT INTO domain_discovery (
					domain, first_seen_at, sources, discovery_strategy,
					confidence, discovery_reason, pre_generated_content, metadata
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
			`;

			const batch = records.map(record => [
				record.domain,
				record.firstSeenAt,
				record.sources,
				record.discoveryStrategy,
				record.confidence,
				record.discoveryReason,
				record.preGeneratedContent || false,
				JSON.stringify(record.metadata),
			]);

			await scyllaClient.batch([{ query, params: batch }]);
		}
		catch (error)
		{
			logger.error('Failed to store provenance batch in ScyllaDB', {
				recordCount: records.length,
				error: error.message,
			});
		}
	}

	private async getProvenanceFromScylla(domain: string): Promise<ProvenanceRecord | null>
	{
		try
		{
			const scyllaClient = this.dbManager.getScyllaClient();

			const query = `
				SELECT domain, first_seen_at, sources, discovery_strategy,
					   confidence, discovery_reason, pre_generated_content, metadata
				FROM domain_discovery
				WHERE domain = ?
			`;

			const result = await scyllaClient.execute(query, [domain]);

			if (result.rows.length === 0)
			{
				return null;
			}

			const row = result.rows[0];
			return {
				domain: row.domain,
				firstSeenAt: row.first_seen_at,
				sources: row.sources,
				discoveryStrategy: row.discovery_strategy,
				confidence: row.confidence,
				discoveryReason: row.discovery_reason,
				preGeneratedContent: row.pre_generated_content,
				metadata: JSON.parse(row.metadata || '{}'),
			};
		}
		catch (error)
		{
			logger.error('Failed to get provenance from ScyllaDB', {
				domain,
				error: error.message,
			});
			return null;
		}
	}

	private async queryProvenanceFromScylla(query: ProvenanceQuery): Promise<ProvenanceRecord[]>
	{
		try
		{
			const scyllaClient = this.dbManager.getScyllaClient();

			let cqlQuery = 'SELECT * FROM domain_discovery WHERE';
			const params: any[] = [];
			const conditions: string[] = [];

			if (query.source)
			{
				conditions.push('sources CONTAINS ?');
				params.push(query.source);
			}

			if (query.strategy)
			{
				conditions.push('discovery_strategy = ?');
				params.push(query.strategy);
			}

			if (query.confidenceMin !== undefined)
			{
				conditions.push('confidence >= ?');
				params.push(query.confidenceMin);
			}

			if (query.confidenceMax !== undefined)
			{
				conditions.push('confidence <= ?');
				params.push(query.confidenceMax);
			}

			if (query.dateFrom)
			{
				conditions.push('first_seen_at >= ?');
				params.push(query.dateFrom);
			}

			if (query.dateTo)
			{
				conditions.push('first_seen_at <= ?');
				params.push(query.dateTo);
			}

			if (conditions.length === 0)
			{
				// No conditions - this would be a full table scan, limit it
				cqlQuery = 'SELECT * FROM domain_discovery LIMIT ?';
				params.push(query.limit || 1000);
			}
			else
			{
				cqlQuery += ` ${ conditions.join(' AND ')}`;
				if (query.limit)
				{
					cqlQuery += ' LIMIT ?';
					params.push(query.limit);
				}
			}

			cqlQuery += ' ALLOW FILTERING';

			const result = await scyllaClient.execute(cqlQuery, params);

			return result.rows.map(row => ({
				domain: row.domain,
				firstSeenAt: row.first_seen_at,
				sources: row.sources,
				discoveryStrategy: row.discovery_strategy,
				confidence: row.confidence,
				discoveryReason: row.discovery_reason,
				preGeneratedContent: row.pre_generated_content,
				metadata: JSON.parse(row.metadata || '{}'),
			}));
		}
		catch (error)
		{
			logger.error('Failed to query provenance from ScyllaDB', {
				query,
				error: error.message,
			});
			return [];
		}
	}

	private async queryProvenanceFromRedis(query: ProvenanceQuery): Promise<ProvenanceRecord[]>
	{
		try
		{
			const pattern = 'provenance:*';
			const keys = await this.redis.keys(pattern);
			const records: ProvenanceRecord[] = [];

			for (const key of keys)
			{
				try
				{
					const data = await this.redis.get(key);
					if (data)
					{
						const record = JSON.parse(data) as ProvenanceRecord;

						// Apply filters
						if (query.source && !record.sources.includes(query.source))
						{
							continue;
						}

						if (query.strategy && record.discoveryStrategy !== query.strategy)
						{
							continue;
						}

						if (query.confidenceMin !== undefined && record.confidence < query.confidenceMin)
						{
							continue;
						}

						if (query.confidenceMax !== undefined && record.confidence > query.confidenceMax)
						{
							continue;
						}

						if (query.dateFrom && record.firstSeenAt < query.dateFrom)
						{
							continue;
						}

						if (query.dateTo && record.firstSeenAt > query.dateTo)
						{
							continue;
						}

						records.push(record);

						// Apply limit
						if (query.limit && records.length >= query.limit)
						{
							break;
						}
					}
				}
				catch (error)
				{
					// Skip invalid records
				}
			}

			return records;
		}
		catch (error)
		{
			logger.error('Failed to query provenance from Redis', {
				query,
				error: error.message,
			});
			return [];
		}
	}

	private async updateStatistics(record: ProvenanceRecord): Promise<void>
	{
		try
		{
			const statsKey = `stats:daily:${new Date().toISOString().split('T')[0]}`;
			const client = this.redis.getClient();

			if (!client)
			{
				return;
			}

			// Update daily statistics using Redis hash
			await Promise.all([
				client.hIncrBy(statsKey, 'total', 1),
				client.hIncrBy(statsKey, `source:${record.sources[0]}`, 1),
				client.hIncrBy(statsKey, `strategy:${record.discoveryStrategy}`, 1),
				client.hIncrBy(statsKey, `confidence:${this.getConfidenceRange(record.confidence)}`, 1),
			]);

			// Set TTL for statistics
			await client.expire(statsKey, 86400 * this.config.statisticsWindowDays);
		}
		catch (error)
		{
			logger.error('Failed to update statistics', {
				domain: record.domain,
				error: error.message,
			});
		}
	}

	private async calculateStatistics(
		dateFrom?: string,
		dateTo?: string,
	): Promise<DiscoveryStatistics>
	{
		const statistics: DiscoveryStatistics = {
			totalDiscovered: 0,
			bySource: {},
			byStrategy: {} as Record<DiscoveryStrategy, number>,
			byConfidenceRange: { high: 0, medium: 0, low: 0 },
			averageConfidence: 0,
			timeRange: {
				start: dateFrom || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
				end: dateTo || new Date().toISOString(),
			},
		};

		try
		{
			// Query records for the time range
			const query: ProvenanceQuery = {
				dateFrom: statistics.timeRange.start,
				dateTo: statistics.timeRange.end,
				limit: 100000, // Large limit for statistics
			};

			const records = await this.queryProvenance(query);

			if (records.length === 0)
			{
				return statistics;
			}

			statistics.totalDiscovered = records.length;

			// Calculate breakdowns
			const confidences: number[] = [];

			for (const record of records)
			{
				// Source breakdown
				for (const source of record.sources)
				{
					statistics.bySource[source] = (statistics.bySource[source] || 0) + 1;
				}

				// Strategy breakdown
				statistics.byStrategy[record.discoveryStrategy] =
					(statistics.byStrategy[record.discoveryStrategy] || 0) + 1;

				// Confidence breakdown
				const range = this.getConfidenceRange(record.confidence);
				statistics.byConfidenceRange[range]++;

				confidences.push(record.confidence);
			}

			// Calculate average confidence
			statistics.averageConfidence =
				confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;

			return statistics;
		}
		catch (error)
		{
			logger.error('Failed to calculate statistics', {
				dateFrom,
				dateTo,
				error: error.message,
			});
			return statistics;
		}
	}

	private async cleanupScyllaProvenance(cutoffDate: string): Promise<number>
	{
		try
		{
			const scyllaClient = this.dbManager.getScyllaClient();

			// ScyllaDB doesn't support DELETE with WHERE on non-key columns efficiently
			// So we'll query first, then delete by primary key
			const query = `
				SELECT domain FROM domain_discovery
				WHERE first_seen_at < ?
				ALLOW FILTERING
			`;

			const result = await scyllaClient.execute(query, [cutoffDate]);
			const domainsToDelete = result.rows.map(row => row.domain);

			if (domainsToDelete.length === 0)
			{
				return 0;
			}

			// Delete in batches
			const batches = this.createBatches(domainsToDelete, 100);
			let deletedCount = 0;

			for (const batch of batches)
			{
				const deleteQuery = 'DELETE FROM domain_discovery WHERE domain = ?';
				const deleteParams = batch.map(domain => [domain]);

				await scyllaClient.batch([{ query: deleteQuery, params: deleteParams }]);
				deletedCount += batch.length;
			}

			return deletedCount;
		}
		catch (error)
		{
			logger.error('Failed to cleanup ScyllaDB provenance', {
				error: error.message,
			});
			return 0;
		}
	}

	private getConfidenceRange(confidence: number): 'high' | 'medium' | 'low'
	{
		if (confidence >= 0.85)
		{
			return 'high';
		}
		if (confidence >= 0.6)
		{
			return 'medium';
		}

		return 'low';
	}

	private createBatches<T>(items: T[], batchSize: number): T[][]
	{
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize)
		{
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}
}

export type {
	ProvenanceRecord,
	DiscoveryStatistics,
	ProvenanceQuery,
	ProvenanceConfig,
};

export { ProvenanceTracker };
