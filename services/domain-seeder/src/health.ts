#!/usr/bin/env node

import axios from 'axios';

async function healthCheck(): Promise<void>
{
	try
	{
		const port = process.env.PORT || 3004;
		const response = await axios.get(`http://localhost:${port}/health/live`, {
			timeout: 5000,
			validateStatus: status => status < 500, // Accept 4xx as valid response
		});

		if (response.status === 200)
		{
			const data = response.data;
			if (data && (data.alive === true || data.healthy === true))
			{
				console.log('Health check passed');
				process.exit(0);
			}
			else
			{
				console.error('Health check failed - service not healthy:', data);
				process.exit(1);
			}
		}
		else
		{
			console.error(`Health check failed with status ${response.status}:`, response.data);
			process.exit(1);
		}
	}
	catch (error)
	{
		if (error.code === 'ECONNREFUSED')
		{
			console.error('Health check error: Service not responding (connection refused)');
		}
		else if (error.code === 'ETIMEDOUT')
		{
			console.error('Health check error: Service timeout');
		}
		else
		{
			console.error('Health check error:', error.message);
		}
		process.exit(1);
	}
}

// Handle process signals gracefully
process.on('SIGTERM', () => process.exit(1));
process.on('SIGINT', () => process.exit(1));

healthCheck();
