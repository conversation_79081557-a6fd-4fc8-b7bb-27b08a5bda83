const {
	createServerRules, createBrowserRules, createWebTestRules, createMobileTestRules
} = require('./_config/eslint.rules');

// Helper function to extract override-compatible config
const extractOverrideConfig = (config) =>
{
	const { root, ignorePatterns, ...overrideConfig } = config;
	return overrideConfig;
};

module.exports = {
	root: true,
	ignorePatterns: [
		'node_modules/',
		'dist/',
		'build/',
		'coverage/',
		'*.min.js',
		'*.bundle.js',
		'logs/',
		'temp/',
		'uploads/',
		'**/*.d.ts',
		'**/__mocks__/**',
		// React Native platform folders
		'**/android/**',
		'**/ios/**',
		'**/.expo/**',
		'**/metro.config.js',
	],
	overrides: [
		{
			files: ['services/crawler/**/*.{js,jsx,ts,tsx}'],
			excludedFiles: [
				'services/crawler/node_modules/**',
				'services/crawler/dist/**',
				'services/crawler/**/__tests__/**',
				'services/crawler/**/__mocks__/**',
			],
			...extractOverrideConfig(createServerRules(__dirname, './services/crawler/tsconfig.json')),
		},
		{
			// Web test files (Vitest/Jest for web)
			files: [
				'services/**/src/__tests__/**/*.test.{js,jsx,ts,tsx}',
				'services/**/src/__tests__/**/*.spec.{js,jsx,ts,tsx}',
				'services/**/__tests__/**/*.test.{js,jsx,ts,tsx}',
				'services/**/__tests__/**/*.spec.{js,jsx,ts,tsx}',
				'shared/**/__tests__/**/*.test.{js,jsx,ts,tsx}',
				'shared/**/__tests__/**/*.spec.{js,jsx,ts,tsx}',
				'**/__mocks__/**/*.{js,jsx,ts,tsx}',
				'**/*.{test,spec}.{js,jsx,ts,tsx}',
			],
			excludedFiles: [
				'**/node_modules/**',
				'**/dist/**',
				'**/build/**',
				// Exclude mobile test patterns
				'**/*.(test|spec).(native|rn|mobile).{js,jsx,ts,tsx}',
				'**/react-native/**',
				'**/mobile/**',
			],
			...extractOverrideConfig(createWebTestRules(__dirname, './_config/tsconfig.test-web.json')),
		},
		{
			// Mobile test files (Jest for React Native)
			files: [
				'**/*.(test|spec).(native|rn|mobile).{js,jsx,ts,tsx}',
				'**/react-native/**/__tests__/**/*.{js,jsx,ts,tsx}',
				'**/mobile/**/__tests__/**/*.{js,jsx,ts,tsx}',
			],
			excludedFiles: ['**/node_modules/**', '**/dist/**', '**/build/**', '**/android/**', '**/ios/**'],
			...extractOverrideConfig(createMobileTestRules(__dirname, './_config/tsconfig.test-mobile.json')),
		},
		{
			files: ['services/web-app/**/*.{js,jsx,ts,tsx}'],
			excludedFiles: [
				'services/web-app/node_modules/**',
				'services/web-app/dist/**',
				'services/web-app/public/**',
				'services/web-app/**/__tests__/**',
				'services/web-app/**/__mocks__/**',
			],
			...extractOverrideConfig(createBrowserRules(__dirname, './services/web-app/tsconfig.json')),
		},
		{
			files: ['services/ranking-engine/**/*.{js,ts}'],
			excludedFiles: [
				'services/ranking-engine/node_modules/**',
				'services/ranking-engine/dist/**',
				'services/ranking-engine/**/__tests__/**',
				'services/ranking-engine/**/__mocks__/**',
			],
			...extractOverrideConfig(createServerRules(__dirname, './services/ranking-engine/tsconfig.json')),
		},
		{
			files: ['services/scheduler/**/*.{js,ts}'],
			excludedFiles: [
				'services/scheduler/node_modules/**',
				'services/scheduler/dist/**',
				'services/scheduler/**/__tests__/**',
				'services/scheduler/**/__mocks__/**',
			],
			...extractOverrideConfig(createServerRules(__dirname, './services/scheduler/tsconfig.json')),
		},
		{
			files: ['services/domain-seeder/**/*.{js,ts}'],
			excludedFiles: [
				'services/domain-seeder/node_modules/**',
				'services/domain-seeder/dist/**',
				'services/domain-seeder/**/__tests__/**',
				'services/domain-seeder/**/__mocks__/**',
			],
			...extractOverrideConfig(createServerRules(__dirname, './services/domain-seeder/tsconfig.json')),
		},
		{
			files: ['shared/**/*.{js,jsx,ts,tsx}'],
			excludedFiles: [
				'shared/node_modules/**',
				'shared/dist/**',
				'shared/**/__tests__/**',
				'shared/**/__mocks__/**',
			],
			...extractOverrideConfig(createBrowserRules(__dirname, './shared/tsconfig.json')),
		},
		{
			files: ['scripts/**/*.{js,ts}'],
			...extractOverrideConfig(createServerRules(__dirname, './_config/tsconfig.base.json')),
		},
		{
			files: ['*.{js,ts}'],
			excludedFiles: ['node_modules/**', 'services/**', 'shared/**', 'scripts/**'],
			...extractOverrideConfig(createServerRules(__dirname, './_config/tsconfig.base.json')),
		},
	],
};
